"""
设备分组关联模型
"""

from sqlalchemy import <PERSON>umn, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app.db import Base

class DeviceGroup(Base):
    """设备分组关联表"""
    __tablename__ = "device_groups"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=False)
    group_id = Column(Integer, ForeignKey("groups.id"), nullable=False)
    
    # 关系
    device = relationship("Device", back_populates="device_groups")
    group = relationship("Group", back_populates="device_groups")
