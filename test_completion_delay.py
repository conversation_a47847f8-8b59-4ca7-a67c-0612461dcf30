#!/usr/bin/env python3
"""
测试任务完成后的分组延迟
验证延迟是基于任务完成时间而不是创建时间
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_task(task_name, device_id, delay_group_sec, delay_like_sec=0.5):
    """创建任务"""
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test_name": task_name},
        "target_scope": "single",
        "target_id": device_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def monitor_task_status(task_id, task_name, max_wait=30):
    """监控任务状态变化"""
    log_with_time(f"👀 开始监控 {task_name} (ID: {task_id})")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status != last_status:
                    elapsed = time.time() - start_time
                    log_with_time(f"📊 {task_name} 状态变化: {last_status} → {current_status} (耗时: {elapsed:.1f}s)")
                    last_status = current_status
                
                # 如果任务完成，返回完成时间
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 最终状态: {current_status}, 总耗时: {total_time:.1f}s")
                    return completion_time, current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 监控 {task_name} 出错: {e}")
        
        time.sleep(1)
    
    log_with_time(f"⏰ {task_name} 监控超时")
    return None, 'timeout'

def test_completion_based_delay():
    """测试基于完成时间的分组延迟"""
    log_with_time("=== 测试基于完成时间的分组延迟 ===")
    
    # 获取设备
    try:
        devices_response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        devices = devices_response.json()
    except:
        log_with_time("❌ 无法获取设备列表")
        return
    
    if not devices:
        log_with_time("❌ 没有可用设备")
        return
    
    device = devices[0]
    device_id = device.get('id')
    device_number = device.get('device_number')
    
    log_with_time(f"📱 使用设备: {device_number} (ID: {device_id})")
    log_with_time(f"⏰ 设置: 分组延迟=3秒, 操作延迟=1秒")
    
    # 记录测试开始时间
    test_start_time = time.time()
    
    # 创建第一个任务
    log_with_time("🚀 创建第一个任务...")
    task1 = create_task("任务1", device_id, 3.0, 1.0)
    if not task1:
        return
    
    # 立即创建第二个任务
    log_with_time("🚀 立即创建第二个任务...")
    task2 = create_task("任务2", device_id, 3.0, 1.0)
    if not task2:
        return
    
    # 立即创建第三个任务
    log_with_time("🚀 立即创建第三个任务...")
    task3 = create_task("任务3", device_id, 3.0, 1.0)
    if not task3:
        return
    
    log_with_time("📋 所有任务已创建，开始监控执行过程...")
    
    # 监控任务执行
    task1_completion, task1_status = monitor_task_status(task1.get('id'), "任务1", 60)
    task2_completion, task2_status = monitor_task_status(task2.get('id'), "任务2", 60)
    task3_completion, task3_status = monitor_task_status(task3.get('id'), "任务3", 60)
    
    # 分析结果
    log_with_time("📊 执行结果分析:")
    
    if task1_completion and task2_completion:
        delay_1_to_2 = task2_completion - task1_completion
        log_with_time(f"   任务1完成 → 任务2完成: {delay_1_to_2:.1f}秒")
        
        if delay_1_to_2 >= 3.0:
            log_with_time("   ✅ 分组延迟正常工作 (≥3秒)")
        else:
            log_with_time("   ⚠️ 分组延迟可能未生效 (<3秒)")
    
    if task2_completion and task3_completion:
        delay_2_to_3 = task3_completion - task2_completion
        log_with_time(f"   任务2完成 → 任务3完成: {delay_2_to_3:.1f}秒")
        
        if delay_2_to_3 >= 3.0:
            log_with_time("   ✅ 分组延迟正常工作 (≥3秒)")
        else:
            log_with_time("   ⚠️ 分组延迟可能未生效 (<3秒)")
    
    total_test_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_test_time:.1f}秒")
    
    # 预期时间分析
    expected_time = 1.0 + 3.0 + 1.0 + 3.0 + 1.0  # 任务1(1s) + 延迟(3s) + 任务2(1s) + 延迟(3s) + 任务3(1s)
    log_with_time(f"📊 预期总时间: {expected_time}秒 (如果分组延迟正常)")
    
    if abs(total_test_time - expected_time) <= 2.0:  # 允许2秒误差
        log_with_time("✅ 总时间符合预期，分组延迟工作正常")
    else:
        log_with_time("⚠️ 总时间与预期不符，可能存在问题")

def test_parallel_devices():
    """测试不同设备的并行执行"""
    log_with_time("=== 测试不同设备并行执行 ===")
    
    try:
        devices_response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        devices = devices_response.json()
    except:
        log_with_time("❌ 无法获取设备列表")
        return
    
    if len(devices) < 2:
        log_with_time("⚠️ 设备数量不足，跳过并行测试")
        return
    
    device1 = devices[0]
    device2 = devices[1]
    
    log_with_time(f"📱 设备1: {device1.get('device_number')} (ID: {device1.get('id')})")
    log_with_time(f"📱 设备2: {device2.get('device_number')} (ID: {device2.get('id')})")
    
    # 同时创建任务
    start_time = time.time()
    
    task1 = create_task("设备1任务", device1.get('id'), 3.0, 2.0)
    task2 = create_task("设备2任务", device2.get('id'), 3.0, 2.0)
    
    if not task1 or not task2:
        return
    
    # 监控并行执行
    log_with_time("👀 监控并行执行...")
    
    # 这里我们简化监控，只等待一段时间然后检查状态
    time.sleep(8)  # 等待足够时间让任务完成
    
    end_time = time.time()
    total_time = end_time - start_time
    
    log_with_time(f"📊 并行执行总时间: {total_time:.1f}秒")
    
    if total_time <= 5.0:  # 如果真正并行，应该在5秒内完成
        log_with_time("✅ 不同设备任务并行执行正常")
    else:
        log_with_time("⚠️ 不同设备任务可能存在串行等待")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 任务完成后分组延迟测试")
    print("=" * 60)
    
    # 测试API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 1. 测试基于完成时间的分组延迟
    test_completion_based_delay()
    
    print("\n" + "-" * 40)
    
    # 2. 测试不同设备并行
    test_parallel_devices()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 正确的分组延迟逻辑:")
    print("   1. 任务创建后立即分发到设备队列")
    print("   2. 设备执行任务（包含操作延迟）")
    print("   3. 任务完成后记录完成时间")
    print("   4. 下一个任务需要等待分组延迟时间")
    print("   5. 不同设备的任务完全并行")
    print("=" * 60)

if __name__ == "__main__":
    main()
