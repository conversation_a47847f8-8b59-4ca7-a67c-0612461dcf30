"""
任务管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading
import json

from config import Config
from utils.time_utils import utc_to_beijing, get_relative_time, beijing_timestamp

class TaskManagerFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.tasks_data = []

        # 创建界面组件
        self.create_widgets()

        # 初始刷新任务列表
        self.refresh()

        # 启动自动刷新任务列表
        self.start_auto_refresh()

    def start_auto_refresh(self):
        """启动自动刷新"""
        self.refresh()
        # 每3秒刷新一次
        self.after(3000, self.start_auto_refresh)

    def create_widgets(self):
        """创建界面组件"""
        # 上半部分：任务查看列表（占大部分空间）
        self.create_task_management_section()

        # 分隔线
        separator = ttk.Separator(self, orient='horizontal')
        separator.pack(fill=tk.X, padx=10, pady=5)

        # 下半部分：任务创建区域（占小部分空间）
        self.create_task_creation_section()

    def create_task_management_section(self):
        """创建任务查看列表区域（上半部分）"""
        # 任务查看列表主容器 - 占据大部分空间
        management_frame = ttk.LabelFrame(self, text="📋 任务查看列表")
        management_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 2))

        # 标题和统计信息
        self.create_task_header(management_frame)

        # 过滤器
        self.create_task_filters(management_frame)

        # 任务列表和详情
        self.create_task_content(management_frame)

    def create_task_header(self, parent):
        """创建任务管理标题和统计信息"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, padx=10, pady=5)

        # 标题
        title_label = ttk.Label(header_frame, text="任务列表", font=('Segoe UI', 12, 'bold'))
        title_label.pack(side=tk.LEFT)

        # 统计信息区域
        stats_frame = ttk.Frame(header_frame)
        stats_frame.pack(side=tk.RIGHT)

        # 任务统计标签
        self.task_stats_label = ttk.Label(stats_frame, text="总任务: 0", font=('Segoe UI', 10))
        self.task_stats_label.pack(side=tk.LEFT, padx=5)

        # 成功任务统计
        self.success_stats_label = ttk.Label(stats_frame, text="成功: 0",
                                           font=('Segoe UI', 10), foreground='#27ae60')
        self.success_stats_label.pack(side=tk.LEFT, padx=5)

        # 失败任务统计
        self.failed_stats_label = ttk.Label(stats_frame, text="失败: 0",
                                          font=('Segoe UI', 10), foreground='#e74c3c')
        self.failed_stats_label.pack(side=tk.LEFT, padx=5)

        # 运行中任务统计
        self.running_stats_label = ttk.Label(stats_frame, text="运行中: 0",
                                           font=('Segoe UI', 10), foreground='#3498db')
        self.running_stats_label.pack(side=tk.LEFT, padx=5)

        # 工具按钮
        btn_frame = ttk.Frame(header_frame)
        btn_frame.pack(side=tk.RIGHT, padx=(20, 0))

        ttk.Button(btn_frame, text="🔄", command=self.refresh, width=3).pack(side=tk.LEFT, padx=1)
        ttk.Button(btn_frame, text="⏸️", command=self.pause_task, width=3).pack(side=tk.LEFT, padx=1)
        ttk.Button(btn_frame, text="▶️", command=self.resume_task, width=3).pack(side=tk.LEFT, padx=1)

    def create_task_filters(self, parent):
        """创建任务过滤器"""
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(filter_frame, text="状态:").pack(side=tk.LEFT)
        self.status_filter = ttk.Combobox(filter_frame, values=["全部", "等待中", "运行中", "已完成", "失败", "已暂停"], width=10)
        self.status_filter.set("全部")
        self.status_filter.bind('<<ComboboxSelected>>', self.on_filter_changed)
        self.status_filter.pack(side=tk.LEFT, padx=5)

        ttk.Label(filter_frame, text="类型:").pack(side=tk.LEFT, padx=(20, 5))
        self.type_filter = ttk.Combobox(filter_frame, values=["全部", "签到任务", "点赞任务", "超话签到任务", "主页关注任务"], width=12)
        self.type_filter.set("全部")
        self.type_filter.bind('<<ComboboxSelected>>', self.on_filter_changed)
        self.type_filter.pack(side=tk.LEFT, padx=2)

    def create_task_content(self, parent):
        """创建任务内容区域（列表和详情）"""
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 左侧任务列表
        self.create_task_list(content_frame)

        # 右侧任务详情
        self.create_task_details(content_frame)

    def create_task_creation_section(self):
        """创建任务创建区域（下半部分）"""
        # 任务创建主容器 - 占据较小空间
        creation_frame = ttk.LabelFrame(self, text="🚀 快速创建任务")
        creation_frame.pack(fill=tk.X, padx=10, pady=(2, 5))

        # 快速创建任务区域
        self.create_quick_task_creation(creation_frame)

    def create_quick_task_creation(self, parent):
        """创建快速任务创建区域"""
        # 快速创建任务框架
        quick_frame = ttk.Frame(parent)
        quick_frame.pack(fill=tk.X, padx=10, pady=10)

        # 第一行：任务类型和目标
        row1 = ttk.Frame(quick_frame)
        row1.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(row1, text="任务类型:").pack(side=tk.LEFT)
        self.quick_task_type = ttk.Combobox(row1, values=["签到任务", "点赞任务", "超话签到任务", "主页关注任务"], width=12)
        self.quick_task_type.set("点赞任务")
        self.quick_task_type.pack(side=tk.LEFT, padx=5)

        ttk.Label(row1, text="目标:").pack(side=tk.LEFT, padx=(20, 5))
        self.quick_target_scope = ttk.Combobox(row1, values=["设备分组"], width=12)
        self.quick_target_scope.set("设备分组")
        self.quick_target_scope.pack(side=tk.LEFT, padx=5)

        # 第二行：参数
        row2 = ttk.Frame(quick_frame)
        row2.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(row2, text="博主ID:").pack(side=tk.LEFT)
        self.quick_blogger_id = tk.StringVar()
        ttk.Entry(row2, textvariable=self.quick_blogger_id, width=20).pack(side=tk.LEFT, padx=5)

        ttk.Label(row2, text="点赞ID:").pack(side=tk.LEFT, padx=(20, 5))
        self.quick_like_id = tk.StringVar()
        ttk.Entry(row2, textvariable=self.quick_like_id, width=20).pack(side=tk.LEFT, padx=5)

        # 第三行：延迟设置
        row3 = ttk.Frame(quick_frame)
        row3.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(row3, text="分组延迟(秒):").pack(side=tk.LEFT)
        self.quick_delay_group = tk.StringVar(value="2")
        ttk.Entry(row3, textvariable=self.quick_delay_group, width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(row3, text="操作延迟(秒):").pack(side=tk.LEFT, padx=(10, 5))
        self.quick_delay_like = tk.StringVar(value="1")
        ttk.Entry(row3, textvariable=self.quick_delay_like, width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(row3, text="点赞延迟(秒):").pack(side=tk.LEFT, padx=(10, 5))
        self.quick_delay_click = tk.StringVar(value="0.5")
        ttk.Entry(row3, textvariable=self.quick_delay_click, width=8).pack(side=tk.LEFT, padx=5)

        # 第四行：分组选择
        row4 = ttk.Frame(quick_frame)
        row4.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(row4, text="选择分组:").pack(side=tk.LEFT)

        # 分组选择容器
        self.quick_groups_container = ttk.Frame(row4)
        self.quick_groups_container.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 第五行：操作按钮
        row5 = ttk.Frame(quick_frame)
        row5.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(row5, text="🚀 创建任务", command=self.quick_create_task).pack(side=tk.LEFT)
        ttk.Button(row5, text="🔄 刷新数据", command=self.load_quick_data).pack(side=tk.LEFT, padx=5)

        # 初始化数据
        self.quick_groups_data = []
        self.quick_group_vars = {}
        self.quick_group_map = {}

        # 加载数据
        self.load_quick_data()





    def load_quick_data(self):
        """加载快速创建的数据"""
        try:
            self.quick_groups_data = self.api_client.get_groups() or []
            self.update_quick_groups_display()
        except Exception as e:
            print(f"加载快速创建数据失败: {e}")

    def update_quick_groups_display(self):
        """更新快速创建的分组显示"""
        # 清空容器
        for widget in self.quick_groups_container.winfo_children():
            widget.destroy()

        self.quick_group_vars = {}
        self.quick_group_map = {}

        if not self.quick_groups_data:
            ttk.Label(self.quick_groups_container, text="暂无分组").pack()
            return

        # 创建分组复选框
        for i, group in enumerate(self.quick_groups_data):
            group_id = group.get('id')
            group_name = group.get('group_name', '')

            var = tk.BooleanVar()
            self.quick_group_vars[group_id] = var
            self.quick_group_map[group_id] = group_name

            cb = ttk.Checkbutton(
                self.quick_groups_container,
                text=group_name,
                variable=var
            )
            cb.pack(side=tk.LEFT, padx=5)

    def quick_create_task(self):
        """快速创建任务"""
        try:
            # 验证输入
            if not self.quick_blogger_id.get().strip():
                messagebox.showerror("错误", "请输入博主ID")
                return
            if not self.quick_like_id.get().strip():
                messagebox.showerror("错误", "请输入点赞ID")
                return

            # 获取选中的分组
            selected_groups = []
            for group_id, var in self.quick_group_vars.items():
                if var.get():
                    selected_groups.append(group_id)

            if not selected_groups:
                messagebox.showerror("错误", "请至少选择一个分组")
                return

            # 获取任务类型
            task_type_name = self.quick_task_type.get()
            task_type_map = {
                "签到任务": "sign",
                "点赞任务": "like",
                "超话签到任务": "page_sign",
                "主页关注任务": "inex"
            }
            task_type = task_type_map.get(task_type_name)

            # 构建参数
            blogger_id = self.quick_blogger_id.get().strip()
            like_id = self.quick_like_id.get().strip()
            delay_click_ms = int(float(self.quick_delay_click.get()) * 1000)

            if task_type == "sign":
                parameters = {"count": 1, "delay_click": delay_click_ms}
            elif task_type == "like":
                # 点赞任务只需要点赞ID
                parameters = {
                    "like_id": like_id,
                    "delay_click": delay_click_ms
                }
            elif task_type == "page_sign":
                parameters = {
                    "page_url": f"https://weibo.com/p/{blogger_id}",
                    "blogger_id": blogger_id,
                    "like_id": like_id,
                    "delay_click": delay_click_ms
                }
            elif task_type == "inex":
                parameters = {
                    "user_id": blogger_id,
                    "count": 1,
                    "delay_click": delay_click_ms
                }

            # 创建任务
            delay_group = int(float(self.quick_delay_group.get()) * 1000)
            delay_like = int(float(self.quick_delay_like.get()) * 1000)

            created_count = 0
            for group_id in selected_groups:
                task_data = {
                    "task_type": task_type,
                    "parameters": parameters,
                    "target_scope": "group",
                    "target_id": group_id,
                    "delay_group": delay_group,
                    "delay_like": delay_like
                }

                result = self.api_client.create_task(task_data)
                if result:
                    created_count += 1

            if created_count > 0:
                messagebox.showinfo("创建成功", f"成功创建 {created_count} 个任务")
                self.refresh()  # 刷新任务列表
            else:
                messagebox.showerror("创建失败", "所有任务创建失败")

        except Exception as e:
            messagebox.showerror("错误", f"创建任务失败: {e}")

    def create_task_list(self, parent):
        """创建现代化任务列表"""
        # 左侧任务列表区域
        list_container = ttk.LabelFrame(parent, text="📋 任务列表")
        list_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建现代化Treeview
        tree_frame = ttk.Frame(list_container)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        columns = ('id', 'task_type', 'status', 'target_scope', 'device_stats', 'create_time')
        self.task_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)

        # 设置现代化列标题
        headers = {
            'id': '🆔 ID',
            'task_type': '📝 类型',
            'status': '📊 状态',
            'target_scope': '🎯 目标',
            'device_stats': '📊 设备统计',
            'create_time': '⏰ 创建时间'
        }

        for col, header in headers.items():
            self.task_tree.heading(col, text=header)

        # 优化列宽
        self.task_tree.column('id', width=60, minwidth=50)
        self.task_tree.column('task_type', width=100, minwidth=80)
        self.task_tree.column('status', width=80, minwidth=70)
        self.task_tree.column('target_scope', width=100, minwidth=80)
        self.task_tree.column('device_stats', width=150, minwidth=120)
        self.task_tree.column('create_time', width=160, minwidth=140)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.task_tree.xview)
        self.task_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.task_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 绑定事件
        self.task_tree.bind('<<TreeviewSelect>>', self.on_task_selected)
        self.task_tree.bind('<Double-1>', self.on_task_double_click)

        # 右键菜单
        self.create_context_menu()

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="📊 查看状态", command=self.show_task_status)
        self.context_menu.add_command(label="⏸️ 暂停任务", command=self.pause_task)
        self.context_menu.add_command(label="▶️ 恢复任务", command=self.resume_task)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📝 查看日志", command=self.refresh_task_log)
        self.context_menu.add_command(label="📋 复制任务ID", command=self.copy_task_id)

        # 绑定右键事件
        self.task_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def copy_task_id(self):
        """复制任务ID到剪贴板"""
        task = self.get_selected_task()
        if task:
            self.clipboard_clear()
            self.clipboard_append(str(task['id']))
            messagebox.showinfo("复制成功", f"任务ID {task['id']} 已复制到剪贴板")

    def create_task_details(self, parent):
        """创建现代化任务详情面板"""
        # 右侧详情面板
        details_container = ttk.LabelFrame(parent, text="📋 任务详情")
        details_container.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        details_container.configure(width=380)

        # 详情内容区域
        details_content = ttk.Frame(details_container)
        details_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 任务状态卡片
        status_card = ttk.LabelFrame(details_content, text="📊 任务状态")
        status_card.pack(fill=tk.X, pady=(0, 10))

        self.detail_labels = {}

        # 基本信息网格
        info_grid = ttk.Frame(status_card)
        info_grid.pack(fill=tk.X, padx=10, pady=10)

        fields = [
            ('🆔 任务ID', 'id'),
            ('📝 类型', 'task_type'),
            ('📊 状态', 'status'),
            ('🎯 目标', 'target_scope'),
            ('⏰ 创建时间', 'create_time')
        ]

        for i, (label_text, field_name) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2

            ttk.Label(info_grid, text=f"{label_text}:", font=('Segoe UI', 9, 'bold')).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 5), pady=3)

            value_label = ttk.Label(info_grid, text="-", foreground="#3498db", font=('Segoe UI', 9))
            value_label.grid(row=row, column=col+1, sticky=tk.W, padx=(0, 15), pady=3)
            self.detail_labels[field_name] = value_label

        # 参数显示区域
        params_card = ttk.LabelFrame(details_content, text="⚙️ 任务参数")
        params_card.pack(fill=tk.X, pady=(0, 10))

        params_frame = ttk.Frame(params_card)
        params_frame.pack(fill=tk.X, padx=10, pady=10)

        self.params_text = tk.Text(params_frame, height=4, width=35, wrap=tk.WORD,
                                  font=('Consolas', 9), bg='#f8f9fa', relief='flat')
        params_scrollbar = ttk.Scrollbar(params_frame, orient=tk.VERTICAL, command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=params_scrollbar.set)

        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        params_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.detail_labels['parameters'] = self.params_text

        # 快速操作按钮
        actions_card = ttk.LabelFrame(details_content, text="🔧 快速操作")
        actions_card.pack(fill=tk.X, pady=(0, 10))

        actions_frame = ttk.Frame(actions_card)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)

        # 按钮网格
        buttons = [
            ("📊 查看状态", self.show_task_status, "#3498db"),
            ("⏸️ 暂停任务", self.pause_task, "#f39c12"),
            ("▶️ 恢复任务", self.resume_task, "#27ae60"),
            ("📋 复制ID", self.copy_task_id, "#9b59b6")
        ]

        for i, (text, command, color) in enumerate(buttons):
            btn = ttk.Button(actions_frame, text=text, command=command, width=12)
            btn.grid(row=i//2, column=i%2, padx=3, pady=3, sticky=tk.EW)

        actions_frame.grid_columnconfigure(0, weight=1)
        actions_frame.grid_columnconfigure(1, weight=1)

        # 执行日志区域
        log_card = ttk.LabelFrame(details_content, text="📝 执行日志")
        log_card.pack(fill=tk.BOTH, expand=True)

        log_content = ttk.Frame(log_card)
        log_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_content)
        log_toolbar.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(log_toolbar, text="🔄", command=self.refresh_task_log, width=3).pack(side=tk.LEFT, padx=1)
        ttk.Button(log_toolbar, text="🗑️", command=self.clear_task_log, width=3).pack(side=tk.LEFT, padx=1)
        ttk.Button(log_toolbar, text="💾", command=self.export_task_log, width=3).pack(side=tk.LEFT, padx=1)

        # 日志显示区域
        log_display = ttk.Frame(log_content)
        log_display.pack(fill=tk.BOTH, expand=True)

        self.task_log_text = tk.Text(log_display, height=12, wrap=tk.WORD,
                                    font=('Consolas', 8), bg='#2c3e50', fg='#ecf0f1',
                                    insertbackground='white', relief='flat')
        log_scrollbar = ttk.Scrollbar(log_display, orient=tk.VERTICAL, command=self.task_log_text.yview)
        self.task_log_text.configure(yscrollcommand=log_scrollbar.set)

        self.task_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置日志文本颜色标签
        self.task_log_text.tag_configure("success", foreground="#27ae60")
        self.task_log_text.tag_configure("error", foreground="#e74c3c")
        self.task_log_text.tag_configure("warning", foreground="#f39c12")
        self.task_log_text.tag_configure("info", foreground="#3498db")

    def refresh(self):
        """刷新任务列表"""
        def fetch_data():
            try:
                # 获取任务列表
                tasks = self.api_client.get_tasks()

                # 获取任务设备统计
                import requests
                try:
                    response = requests.get(f"{self.api_client.base_url}/tasks/stats/device-summary", timeout=10)
                    if response.status_code == 200:
                        device_stats = response.json()
                        print(f"✅ 获取设备统计成功，共{len(device_stats)}个任务的统计数据")
                    else:
                        device_stats = {}
                        print(f"❌ 获取设备统计失败，状态码: {response.status_code}")
                except Exception as e:
                    device_stats = {}
                    print(f"❌ 获取设备统计异常: {e}")

                self.winfo_toplevel().after(0, lambda: self.update_task_list(tasks, device_stats))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: messagebox.showerror("刷新失败", f"获取任务数据失败: {e}"))

        threading.Thread(target=fetch_data, daemon=True).start()

    def update_task_list(self, tasks, device_stats=None):
        """更新现代化任务列表显示"""
        # 清空现有数据
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)

        self.tasks_data = tasks or []
        device_stats = device_stats or {}

        # 统计任务状态
        status_counts = {'pending': 0, 'running': 0, 'done': 0, 'failed': 0, 'paused': 0}

        # 添加任务数据
        for task in self.tasks_data:
            # 统计状态
            status = task.get('status', 'pending')
            if status in status_counts:
                status_counts[status] += 1

            # 格式化创建时间 - 直接使用服务器返回的时间
            create_time = task.get('create_time', '')
            if create_time:
                # 服务器返回格式：2025-06-09T11:27:02
                # 转换为显示格式：2025-06-09 11:27
                try:
                    from datetime import datetime
                    # 处理ISO格式时间
                    if 'T' in create_time:
                        dt = datetime.fromisoformat(create_time.replace('Z', ''))
                    else:
                        dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    create_time = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    # 如果解析失败，使用原始格式
                    create_time = str(create_time)

            # 格式化状态显示
            status_display = Config.TASK_STATUS.get(status, status)
            status_icon = self.get_status_icon(status)

            # 格式化任务类型
            task_type_display = Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))

            # 格式化目标范围
            target_display = Config.TARGET_SCOPE.get(task.get('target_scope', ''), task.get('target_scope', ''))

            # 获取设备统计信息
            task_id = task.get('id')
            task_device_stats = device_stats.get(str(task_id), {})

            # 格式化设备统计显示
            total_devices = task_device_stats.get('total', 0)
            success_devices = task_device_stats.get('done', 0)
            failed_devices = task_device_stats.get('failed', 0)
            running_devices = task_device_stats.get('running', 0)
            pending_devices = task_device_stats.get('pending', 0)

            if total_devices > 0:
                device_stats_display = f"✅{success_devices} ❌{failed_devices}"
                if running_devices > 0:
                    device_stats_display += f" 🔄{running_devices}"
                if pending_devices > 0:
                    device_stats_display += f" ⏳{pending_devices}"
                device_stats_display += f" (共{total_devices}台)"

                # 调试信息：显示前几个任务的统计
                if task_id <= 5:
                    print(f"📊 任务{task_id}设备统计: {device_stats_display}")
            else:
                device_stats_display = "无设备"

            values = (
                task.get('id', ''),
                task_type_display,
                f"{status_icon} {status_display}",
                target_display,
                device_stats_display,
                create_time
            )

            item = self.task_tree.insert('', tk.END, values=values)

            # 设置行的标签用于颜色区分
            if status == 'running':
                self.task_tree.set(item, 'status', f"🔄 {status_display}")
            elif status == 'done':
                self.task_tree.set(item, 'status', f"✅ {status_display}")
            elif status == 'failed':
                self.task_tree.set(item, 'status', f"❌ {status_display}")
            elif status == 'paused':
                self.task_tree.set(item, 'status', f"⏸️ {status_display}")

        # 更新统计信息
        total_tasks = len(self.tasks_data)
        running_tasks = status_counts['running']
        completed_tasks = status_counts['done']
        failed_tasks = status_counts['failed']
        pending_tasks = status_counts['pending']

        # 更新各个统计标签
        self.task_stats_label.configure(text=f"总任务: {total_tasks}")
        self.success_stats_label.configure(text=f"成功: {completed_tasks}")
        self.failed_stats_label.configure(text=f"失败: {failed_tasks}")
        self.running_stats_label.configure(text=f"运行中: {running_tasks}")

        # 如果有等待中的任务，也显示
        if pending_tasks > 0:
            self.running_stats_label.configure(text=f"运行中: {running_tasks} | 等待中: {pending_tasks}")

        # 应用过滤
        self.apply_filters()

    def get_status_icon(self, status):
        """获取状态图标"""
        icons = {
            'pending': '⏳',
            'running': '🔄',
            'done': '✅',
            'failed': '❌',
            'paused': '⏸️',
            'cancelled': '🚫'
        }
        return icons.get(status, '❓')

    def calculate_task_progress(self, task):
        """计算任务进度"""
        # 这里可以根据实际需求计算进度
        # 暂时返回简单的状态描述
        status = task.get('status', 'pending')
        if status == 'done':
            return "100%"
        elif status == 'running':
            return "执行中"
        elif status == 'failed':
            return "失败"
        elif status == 'paused':
            return "暂停"
        else:
            return "等待中"

    def apply_filters(self):
        """应用过滤器"""
        status_filter = self.status_filter.get()
        type_filter = self.type_filter.get()

        # 这里可以实现过滤逻辑
        # 由于篇幅限制，暂时省略具体实现
        pass

    def on_filter_changed(self, event):
        """过滤器变化"""
        self.apply_filters()

    def on_task_selected(self, event):
        """任务选择事件"""
        selection = self.task_tree.selection()
        if not selection:
            return

        item = selection[0]
        task_id = self.task_tree.item(item)['values'][0]

        # 查找任务数据
        task_data = None
        for task in self.tasks_data:
            if str(task.get('id')) == str(task_id):
                task_data = task
                break

        if task_data:
            self.update_task_details(task_data)

    def on_task_double_click(self, event):
        """任务双击事件"""
        self.show_task_status()

    def update_task_details(self, task_data):
        """更新任务详情显示"""
        # 格式化创建时间 - 直接使用服务器返回的时间
        create_time = task_data.get('create_time', '')
        if create_time:
            # 直接使用服务器时间，格式化为可读格式
            try:
                from datetime import datetime
                if 'T' in create_time:
                    dt = datetime.fromisoformat(create_time.replace('Z', ''))
                else:
                    dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                create_time = str(create_time)

        # 更新详情标签
        self.detail_labels['id'].config(text=str(task_data.get('id', '-')))
        self.detail_labels['task_type'].config(text=Config.TASK_TYPES.get(task_data.get('task_type', ''), '-'))
        self.detail_labels['status'].config(
            text=Config.TASK_STATUS.get(task_data.get('status', ''), '-'),
            foreground=Config.COLORS.get(task_data.get('status', ''), 'black')
        )
        self.detail_labels['target_scope'].config(text=Config.TARGET_SCOPE.get(task_data.get('target_scope', ''), '-'))
        self.detail_labels['create_time'].config(text=create_time)

        # 更新参数显示
        parameters = task_data.get('parameters', {})
        if isinstance(parameters, dict):
            param_text = json.dumps(parameters, indent=2, ensure_ascii=False)
        else:
            param_text = str(parameters)

        self.detail_labels['parameters'].delete(1.0, tk.END)
        self.detail_labels['parameters'].insert(1.0, param_text)

        # 自动刷新任务日志
        self.refresh_task_log()

    def get_selected_task(self):
        """获取当前选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            return None

        item = selection[0]
        task_id = self.task_tree.item(item)['values'][0]

        for task in self.tasks_data:
            if str(task.get('id')) == str(task_id):
                return task
        return None

    def show_create_task_dialog(self):
        """显示创建任务对话框"""
        CreateTaskDialog(self, self.api_client, self.refresh)

    def show_task_status(self):
        """显示任务状态"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择任务", "请先选择要查看状态的任务")
            return

        TaskStatusDialog(self, self.api_client, task['id'])

    def pause_task(self):
        """暂停任务"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择任务", "请先选择要暂停的任务")
            return

        if messagebox.askyesno("确认暂停", f"确定要暂停任务 {task['id']} 吗？"):
            def pause():
                try:
                    result = self.api_client.pause_task(task['id'])
                    if result:
                        self.winfo_toplevel().after(0, lambda: messagebox.showinfo("操作成功", "任务已暂停"))
                        self.winfo_toplevel().after(0, self.refresh)
                    else:
                        self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作失败", "暂停任务失败"))
                except Exception as e:
                    self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作异常", f"暂停任务时发生错误: {e}"))

            threading.Thread(target=pause, daemon=True).start()

    def resume_task(self):
        """恢复任务"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择任务", "请先选择要恢复的任务")
            return

        if messagebox.askyesno("确认恢复", f"确定要恢复任务 {task['id']} 吗？"):
            def resume():
                try:
                    result = self.api_client.resume_task(task['id'])
                    if result:
                        self.winfo_toplevel().after(0, lambda: messagebox.showinfo("操作成功", "任务已恢复"))
                        self.winfo_toplevel().after(0, self.refresh)
                    else:
                        self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作失败", "恢复任务失败"))
                except Exception as e:
                    self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作异常", f"恢复任务时发生错误: {e}"))

            threading.Thread(target=resume, daemon=True).start()

    def refresh_task_log(self):
        """刷新任务日志"""
        task = self.get_selected_task()
        if not task:
            return

        def fetch_task_log():
            try:
                import requests
                # 获取任务的设备执行日志
                response = requests.get(f"{self.api_client.base_url}/task-sync/task-device-logs/{task['id']}", timeout=10)
                if response.status_code == 200:
                    logs = response.json()
                    self.winfo_toplevel().after(0, lambda: self.update_task_log_display(logs))
                else:
                    self.winfo_toplevel().after(0, lambda: self.add_task_log("获取任务日志失败"))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: self.add_task_log(f"获取任务日志异常: {e}"))

        threading.Thread(target=fetch_task_log, daemon=True).start()

    def update_task_log_display(self, logs):
        """更新任务日志显示"""
        self.task_log_text.delete('1.0', tk.END)

        if not logs:
            self.add_task_log("暂无设备执行日志")
            return

        for log_entry in logs:
            device_number = log_entry.get('device_number', '未知设备')
            task_type = log_entry.get('task_type', '未知任务')
            status = log_entry.get('status', '未知状态')
            result = log_entry.get('result_summary', '')
            finish_time = log_entry.get('finish_time', '')

            # 格式化时间 - 直接使用服务器返回的时间
            if finish_time:
                try:
                    from datetime import datetime
                    if 'T' in finish_time:
                        dt = datetime.fromisoformat(finish_time.replace('Z', ''))
                    else:
                        dt = datetime.strptime(finish_time, '%Y-%m-%d %H:%M:%S')
                    finish_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    finish_time = str(finish_time)
            else:
                finish_time = "未完成"

            # 格式化状态
            status_icon = "✅" if status == "done" else "❌" if status == "failed" else "⏳"

            log_message = f"[{finish_time}] {status_icon} {device_number} - {task_type}"
            if result:
                # 截取结果摘要
                result_short = result[:50] + "..." if len(result) > 50 else result
                log_message += f"\n    结果: {result_short}"

            self.add_task_log(log_message)

    def add_task_log(self, message):
        """添加任务日志消息"""
        timestamp = beijing_timestamp().split(' ')[1]  # 只取时间部分
        log_entry = f"[{timestamp}] {message}\n"

        self.task_log_text.insert(tk.END, log_entry)
        self.task_log_text.see(tk.END)

        # 限制日志行数
        lines = self.task_log_text.get('1.0', tk.END).split('\n')
        if len(lines) > 200:
            # 保留最后200行
            self.task_log_text.delete('1.0', f'{len(lines)-200}.0')

    def clear_task_log(self):
        """清空任务日志"""
        self.task_log_text.delete('1.0', tk.END)
        self.add_task_log("任务日志已清空")

    def export_task_log(self):
        """导出任务日志"""
        from tkinter import filedialog

        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择任务", "请先选择要导出日志的任务")
            return

        filename = filedialog.asksaveasfilename(
            title="导出任务日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialname=f"task_{task['id']}_log.txt"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"任务 {task['id']} 设备执行日志\n")
                    f.write(f"导出时间: {beijing_timestamp()}\n")
                    f.write("="*50 + "\n\n")
                    f.write(self.task_log_text.get('1.0', tk.END))
                self.add_task_log(f"任务日志已导出到: {filename}")
            except Exception as e:
                self.add_task_log(f"导出任务日志失败: {e}")
                messagebox.showerror("导出失败", f"导出任务日志失败: {e}")


class CreateTaskDialog:
    def __init__(self, parent, api_client, refresh_callback):
        self.parent = parent
        self.api_client = api_client
        self.refresh_callback = refresh_callback

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("创建任务")
        self.dialog.geometry("600x650")  # 增加高度以容纳更多内容
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 任务类型
        ttk.Label(main_frame, text="任务类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.task_type = ttk.Combobox(main_frame, values=list(Config.TASK_TYPES.values()), width=30)
        self.task_type.grid(row=0, column=1, sticky=tk.W, pady=5)
        self.task_type.bind('<<ComboboxSelected>>', self.on_task_type_changed)

        # 目标范围
        ttk.Label(main_frame, text="目标范围:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.target_scope = ttk.Combobox(main_frame, values=list(Config.TARGET_SCOPE.values()), width=30)
        self.target_scope.grid(row=1, column=1, sticky=tk.W, pady=5)
        self.target_scope.bind('<<ComboboxSelected>>', self.on_target_scope_changed)

        # 目标选择框架
        self.target_frame = ttk.LabelFrame(main_frame, text="目标选择")
        self.target_frame.grid(row=2, column=0, columnspan=2, sticky=tk.EW, pady=5)

        # 参数框架
        self.params_frame = ttk.LabelFrame(main_frame, text="任务参数")
        self.params_frame.grid(row=3, column=0, columnspan=2, sticky=tk.EW, pady=5)

        # 延迟设置框架
        self.delay_frame = ttk.LabelFrame(main_frame, text="延迟设置")
        self.delay_frame.grid(row=4, column=0, columnspan=2, sticky=tk.EW, pady=5)

        # 延迟设置
        ttk.Label(self.delay_frame, text="分组延迟(秒):").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.delay_group = tk.StringVar(value="1")
        ttk.Entry(self.delay_frame, textvariable=self.delay_group, width=15).grid(row=0, column=1, pady=5, padx=5)

        ttk.Label(self.delay_frame, text="点赞延迟(秒):").grid(row=0, column=2, sticky=tk.W, pady=5, padx=5)
        self.delay_like = tk.StringVar(value="0.5")
        ttk.Entry(self.delay_frame, textvariable=self.delay_like, width=15).grid(row=0, column=3, pady=5, padx=5)

        # 添加说明
        ttk.Label(self.delay_frame, text="分组延迟：分组间任务执行的间隔时间",
                 foreground="gray", font=('Arial', 8)).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2, padx=5)
        ttk.Label(self.delay_frame, text="点赞延迟：单次操作间的间隔时间",
                 foreground="gray", font=('Arial', 8)).grid(row=1, column=2, columnspan=2, sticky=tk.W, pady=2, padx=5)

        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=20)

        ttk.Button(btn_frame, text="创建", command=self.create_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT, padx=5)

        # 初始化数据
        self.load_devices_and_groups()

    def load_devices_and_groups(self):
        """加载设备和分组数据"""
        def fetch_data():
            try:
                # 获取设备列表
                devices = self.api_client.get_devices()
                self.devices_data = devices or []

                # 获取分组列表
                groups = self.api_client.get_groups()
                self.groups_data = groups or []

                # 在主线程中更新界面
                self.dialog.after(0, self.update_target_options)

            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("加载失败", f"加载数据失败: {e}"))

        threading.Thread(target=fetch_data, daemon=True).start()

    def update_target_options(self):
        """更新目标选择选项"""
        # 这个方法会在目标范围变化时被调用
        pass

    def on_target_scope_changed(self, event):
        """目标范围变化时更新目标选择界面"""
        # 清空目标框架
        for widget in self.target_frame.winfo_children():
            widget.destroy()

        target_scope_name = self.target_scope.get()

        if target_scope_name == "单个设备":
            ttk.Label(self.target_frame, text="选择设备:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)

            # 创建设备选择下拉框
            device_options = []
            device_map = {}

            for device in getattr(self, 'devices_data', []):
                device_id = device.get('id')
                device_number = device.get('device_number', '')
                device_ip = device.get('device_ip', '')
                status = device.get('online_status', 'offline')
                status_icon = "🟢" if status == 'online' else "🔴"

                display_text = f"{status_icon} {device_number} ({device_ip})"
                device_options.append(display_text)
                device_map[display_text] = device_id

            self.target_device = ttk.Combobox(self.target_frame, values=device_options, width=40)
            self.target_device.grid(row=0, column=1, pady=5, padx=5)
            self.device_map = device_map

        elif target_scope_name == "设备分组":
            ttk.Label(self.target_frame, text="选择分组:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)

            # 创建分组选择下拉框
            group_options = []
            group_map = {}

            for group in getattr(self, 'groups_data', []):
                group_id = group.get('id')
                group_name = group.get('group_name', '')
                description = group.get('description', '')

                # 计算分组中的设备数量
                device_count = len([d for d in getattr(self, 'devices_data', []) if d.get('group_id') == group_id])

                display_text = f"{group_name} ({device_count}个设备)"
                if description:
                    display_text += f" - {description}"

                group_options.append(display_text)
                group_map[display_text] = group_id

            self.target_group = ttk.Combobox(self.target_frame, values=group_options, width=40)
            self.target_group.grid(row=0, column=1, pady=5, padx=5)
            self.group_map = group_map

    def on_task_type_changed(self, event):
        """任务类型变化时更新参数界面"""
        # 清空参数框架
        for widget in self.params_frame.winfo_children():
            widget.destroy()

        task_type_name = self.task_type.get()

        # 根据任务类型显示不同的参数输入框
        if task_type_name == "签到任务":
            # 签到任务参数
            ttk.Label(self.params_frame, text="签到次数:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
            self.count_var = tk.StringVar(value="1")
            ttk.Entry(self.params_frame, textvariable=self.count_var, width=20).grid(row=0, column=1, pady=5, padx=5)

        elif task_type_name == "点赞任务":
            # 点赞任务参数 - 只需要点赞ID
            ttk.Label(self.params_frame, text="点赞ID:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
            self.like_id_var = tk.StringVar()
            ttk.Entry(self.params_frame, textvariable=self.like_id_var, width=20).grid(row=0, column=1, pady=5, padx=5)

            # 添加说明
            ttk.Label(self.params_frame, text="提示：点赞任务只需要填写点赞ID",
                     foreground="gray").grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5, padx=5)

        elif task_type_name == "页面签到任务":
            # 页面签到任务参数
            ttk.Label(self.params_frame, text="页面URL:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
            self.page_url_var = tk.StringVar()
            ttk.Entry(self.params_frame, textvariable=self.page_url_var, width=30).grid(row=0, column=1, pady=5, padx=5)

        elif task_type_name == "主页关注任务":
            # 主页关注任务参数
            ttk.Label(self.params_frame, text="用户ID:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
            self.user_id_var = tk.StringVar()
            ttk.Entry(self.params_frame, textvariable=self.user_id_var, width=20).grid(row=0, column=1, pady=5, padx=5)

            ttk.Label(self.params_frame, text="关注次数:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
            self.count_var = tk.StringVar(value="1")
            ttk.Entry(self.params_frame, textvariable=self.count_var, width=20).grid(row=1, column=1, pady=5, padx=5)

            # 添加说明
            ttk.Label(self.params_frame, text="说明: 主页关注功能",
                     foreground="gray").grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5, padx=5)

    def create_task(self):
        """创建任务"""
        try:
            # 获取任务类型
            task_type_name = self.task_type.get()
            if not task_type_name:
                messagebox.showerror("错误", "请选择任务类型")
                return

            # 获取任务类型的英文名
            task_type_map = {v: k for k, v in Config.TASK_TYPES.items()}
            task_type = task_type_map.get(task_type_name)

            if not task_type:
                messagebox.showerror("错误", "无效的任务类型")
                return

            # 获取参数
            parameters = self.get_task_parameters(task_type)
            if parameters is None:
                return  # 参数验证失败

            # 获取目标范围和ID
            target_scope = self.target_scope.get()
            if target_scope == "单个设备":
                target_scope = "single"
                device_display = self.target_device.get()
                if not device_display:
                    messagebox.showerror("错误", "请选择目标设备")
                    return

                # 从映射中获取设备ID
                target_id = getattr(self, 'device_map', {}).get(device_display)
                if not target_id:
                    messagebox.showerror("错误", "无效的设备选择")
                    return

            elif target_scope == "设备分组":
                target_scope = "group"
                group_display = self.target_group.get()
                if not group_display:
                    messagebox.showerror("错误", "请选择目标分组")
                    return

                # 从映射中获取分组ID
                target_id = getattr(self, 'group_map', {}).get(group_display)
                if not target_id:
                    messagebox.showerror("错误", "无效的分组选择")
                    return

            else:
                messagebox.showerror("错误", "请选择目标范围")
                return

            # 获取延迟设置（转换为毫秒）
            try:
                delay_group_sec = float(self.delay_group.get()) if self.delay_group.get() else 1.0
                delay_like_sec = float(self.delay_like.get()) if self.delay_like.get() else 0.5

                # 转换为毫秒
                delay_group = int(delay_group_sec * 1000)
                delay_like = int(delay_like_sec * 1000)
            except ValueError:
                messagebox.showerror("错误", "延迟设置必须是数字")
                return

            # 构建任务数据
            task_data = {
                "task_type": task_type,
                "parameters": parameters,
                "target_scope": target_scope,
                "target_id": target_id,
                "delay_group": delay_group,
                "delay_like": delay_like
            }

            # 创建任务
            result = self.api_client.create_task(task_data)

            if result:
                messagebox.showinfo("成功", f"任务创建成功！\n任务ID: {result.get('id')}")
                self.dialog.destroy()
                self.refresh_callback()  # 调用回调函数刷新任务列表
            else:
                messagebox.showerror("错误", "任务创建失败")

        except Exception as e:
            messagebox.showerror("错误", f"创建任务时发生错误: {e}")

    def get_task_parameters(self, task_type):
        """获取任务参数"""
        try:
            if task_type == "sign":
                count = self.count_var.get() if hasattr(self, 'count_var') else "1"
                try:
                    count = int(count) if count else 1
                except ValueError:
                    messagebox.showerror("错误", "签到次数必须是数字")
                    return None
                return {"count": count}

            elif task_type == "like":
                like_id = self.like_id_var.get() if hasattr(self, 'like_id_var') else ""

                if not like_id:
                    messagebox.showerror("错误", "请输入点赞ID")
                    return None

                # 点赞任务只需要点赞ID
                return {
                    "like_id": like_id
                }

            elif task_type == "page_sign":
                page_url = self.page_url_var.get() if hasattr(self, 'page_url_var') else ""
                if not page_url:
                    messagebox.showerror("错误", "请输入页面URL")
                    return None
                return {"page_url": page_url}

            elif task_type == "inex":
                user_id = self.user_id_var.get() if hasattr(self, 'user_id_var') else ""
                count = self.count_var.get() if hasattr(self, 'count_var') else "1"

                if not user_id:
                    messagebox.showerror("错误", "请输入用户ID")
                    return None

                try:
                    count = int(count) if count else 1
                except ValueError:
                    messagebox.showerror("错误", "关注次数必须是数字")
                    return None

                return {
                    "user_id": user_id,
                    "count": count
                }

            else:
                messagebox.showerror("错误", f"不支持的任务类型: {task_type}")
                return None

        except Exception as e:
            messagebox.showerror("错误", f"获取参数时发生错误: {e}")
            return None


class TaskStatusDialog:
    def __init__(self, parent, api_client, task_id):
        self.parent = parent
        self.api_client = api_client
        self.task_id = task_id

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"任务状态 - {task_id}")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()
        self.refresh_status()

    def create_widgets(self):
        """创建状态对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 状态信息
        self.status_text = tk.Text(main_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮
        btn_frame = ttk.Frame(self.dialog)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=10)

        ttk.Button(btn_frame, text="刷新", command=self.refresh_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=self.dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def refresh_status(self):
        """刷新任务状态"""
        def fetch_status():
            try:
                status = self.api_client.get_task_status(self.task_id)
                self.dialog.after(0, lambda: self.update_status_display(status))
            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("获取失败", f"获取任务状态失败: {e}"))

        threading.Thread(target=fetch_status, daemon=True).start()

    def update_status_display(self, status):
        """更新状态显示"""
        self.status_text.delete(1.0, tk.END)
        if status:
            status_text = json.dumps(status, indent=2, ensure_ascii=False)
            self.status_text.insert(1.0, status_text)
        else:
            self.status_text.insert(1.0, "无法获取任务状态")

    def init_quick_task_creation(self):
        """延迟初始化快速任务创建功能"""
        try:
            # 绑定事件和命令
            self.quick_target_scope.bind('<<ComboboxSelected>>', self.on_quick_target_scope_changed)
            self.quick_create_btn.configure(command=self.quick_create_task)
            self.save_settings_btn.configure(command=self.save_quick_settings)
            self.load_settings_btn.configure(command=self.load_quick_settings)

            # 加载数据和设置
            self.load_devices_and_groups_for_quick()
            self.load_quick_settings()
        except Exception as e:
            print(f"快速任务创建初始化失败: {e}")

    # 快速任务创建相关方法
    def load_devices_and_groups_for_quick(self):
        """为快速创建加载设备和分组数据"""
        def fetch_data():
            try:
                print("开始加载快速创建数据...")

                # 获取设备列表
                devices = self.api_client.get_devices()
                self.quick_devices_data = devices or []
                print(f"加载了 {len(self.quick_devices_data)} 个设备")

                # 获取分组列表
                groups = self.api_client.get_groups()
                self.quick_groups_data = groups or []
                print(f"加载了 {len(self.quick_groups_data)} 个分组")

                # 在主线程中更新界面
                self.winfo_toplevel().after(0, self.update_quick_target_options)

            except Exception as e:
                print(f"加载快速创建数据失败: {e}")
                # 设置空数据
                self.quick_devices_data = []
                self.quick_groups_data = []
                self.winfo_toplevel().after(0, self.update_quick_target_options)

        threading.Thread(target=fetch_data, daemon=True).start()

    def on_quick_target_scope_changed(self, event):
        """快速创建目标范围变化"""
        self.update_quick_target_options()

    def select_all_groups(self):
        """全选分组"""
        for var in getattr(self, 'quick_group_vars', {}).values():
            var.set(True)

    def deselect_all_groups(self):
        """全不选分组"""
        for var in getattr(self, 'quick_group_vars', {}).values():
            var.set(False)

    def update_quick_target_options(self):
        """更新快速创建的目标选择选项"""
        try:
            # 清空目标容器
            for widget in self.quick_target_container.winfo_children():
                widget.destroy()

            target_scope = self.quick_target_scope.get()
            print(f"更新目标选择选项: {target_scope}")

            if target_scope == "单个设备":
                # 单个设备选择
                device_options = []
                self.quick_device_map = {}

                devices_data = getattr(self, 'quick_devices_data', [])
                print(f"设备数据: {len(devices_data)} 个设备")

                for device in devices_data:
                    device_id = device.get('id')
                    device_number = device.get('device_number', '')
                    status = device.get('online_status', 'offline')
                    status_icon = "🟢" if status == 'online' else "🔴"

                    display_text = f"{status_icon} {device_number}"
                    device_options.append(display_text)
                    self.quick_device_map[display_text] = device_id

                self.quick_target_device = ttk.Combobox(self.quick_target_container, values=device_options, width=25)
                self.quick_target_device.pack()

            elif target_scope == "设备分组":
                # 多分组选择（支持勾选多个）
                self.quick_group_vars = {}
                self.quick_group_map = {}

                groups_data = getattr(self, 'quick_groups_data', [])
                devices_data = getattr(self, 'quick_devices_data', [])
                print(f"分组数据: {len(groups_data)} 个分组")
                print(f"设备数据: {len(devices_data)} 个设备")

                if not groups_data:
                    # 如果没有分组数据，显示提示
                    ttk.Label(self.quick_target_container, text="暂无分组数据，请先创建分组").pack()
                    return

                # 创建分组选择区域
                groups_frame = ttk.Frame(self.quick_target_container)
                groups_frame.pack(fill=tk.BOTH, expand=True)

                # 添加"全选"和"全不选"按钮
                control_frame = ttk.Frame(groups_frame)
                control_frame.pack(fill=tk.X, pady=2)

                ttk.Button(control_frame, text="全选", command=self.select_all_groups, width=8).pack(side=tk.LEFT, padx=2)
                ttk.Button(control_frame, text="全不选", command=self.deselect_all_groups, width=8).pack(side=tk.LEFT, padx=2)

                # 创建滚动框架
                canvas = tk.Canvas(groups_frame, height=120, width=450)
                scrollbar = ttk.Scrollbar(groups_frame, orient="vertical", command=canvas.yview)
                scrollable_frame = ttk.Frame(canvas)

                scrollable_frame.bind(
                    "<Configure>",
                    lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
                )

                canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                canvas.configure(yscrollcommand=scrollbar.set)

                # 添加分组复选框
                for i, group in enumerate(groups_data):
                    group_id = group.get('id')
                    group_name = group.get('group_name', '')

                    # 计算分组中的设备数量
                    device_count = len([d for d in devices_data if d.get('group_id') == group_id])

                    var = tk.BooleanVar()
                    self.quick_group_vars[group_id] = var
                    self.quick_group_map[group_id] = group_name

                    cb = ttk.Checkbutton(
                        scrollable_frame,
                        text=f"{group_name} ({device_count}设备)",
                        variable=var
                    )
                    cb.grid(row=i//2, column=i%2, sticky=tk.W, padx=10, pady=3)

                canvas.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")

                print(f"已创建 {len(groups_data)} 个分组复选框")

        except Exception as e:
            print(f"更新目标选择选项失败: {e}")
            # 显示错误信息
            ttk.Label(self.quick_target_container, text=f"加载失败: {e}").pack()

    def quick_create_task(self):
        """快速创建任务"""
        try:
            # 获取任务类型
            task_type_name = self.quick_task_type.get()
            if not task_type_name:
                messagebox.showerror("错误", "请选择任务类型")
                return

            # 任务类型映射
            task_type_map = {
                "签到任务": "sign",
                "点赞任务": "like",
                "超话签到任务": "page_sign",
                "主页关注任务": "inex"
            }
            task_type = task_type_map.get(task_type_name)

            # 获取参数（统一使用博主ID和点赞ID）
            blogger_id = self.quick_blogger_id.get().strip()
            like_id = self.quick_like_id.get().strip()

            if not blogger_id:
                messagebox.showerror("错误", "请输入博主ID")
                return
            if not like_id:
                messagebox.showerror("错误", "请输入点赞ID")
                return

            # 构建参数
            if task_type == "sign":
                parameters = {"count": 1}  # 签到任务固定1次
            elif task_type == "like":
                parameters = {
                    "blogger_id": blogger_id,
                    "like_id": like_id
                }
            elif task_type == "page_sign":
                parameters = {
                    "page_url": f"https://weibo.com/p/{blogger_id}",  # 使用博主ID构建超话URL
                    "blogger_id": blogger_id,
                    "like_id": like_id
                }
            elif task_type == "inex":
                parameters = {
                    "user_id": blogger_id,  # 主页关注使用博主ID作为用户ID
                    "count": 1
                }

            # 获取目标
            target_scope = self.quick_target_scope.get()
            if target_scope == "单个设备":
                target_scope = "single"
                device_display = self.quick_target_device.get()
                if not device_display:
                    messagebox.showerror("错误", "请选择目标设备")
                    return
                target_id = self.quick_device_map.get(device_display)
                if not target_id:
                    messagebox.showerror("错误", "无效的设备选择")
                    return

                # 创建单个任务
                self.create_single_quick_task(task_type, parameters, target_scope, target_id)

            elif target_scope == "设备分组":
                target_scope = "group"
                # 获取选中的分组
                selected_groups = []
                for group_id, var in self.quick_group_vars.items():
                    if var.get():
                        selected_groups.append(group_id)

                if not selected_groups:
                    messagebox.showerror("错误", "请至少选择一个分组")
                    return

                # 为每个选中的分组创建任务
                self.create_multiple_quick_tasks(task_type, parameters, target_scope, selected_groups)

        except Exception as e:
            messagebox.showerror("错误", f"创建任务时发生错误: {e}")

    def create_single_quick_task(self, task_type, parameters, target_scope, target_id):
        """创建单个快速任务"""
        try:
            # 获取延迟设置
            delay_group = int(float(self.quick_delay_group.get()) * 1000)  # 转换为毫秒
            delay_like = int(float(self.quick_delay_like.get()) * 1000)

            task_data = {
                "task_type": task_type,
                "parameters": parameters,
                "target_scope": target_scope,
                "target_id": target_id,
                "delay_group": delay_group,
                "delay_like": delay_like
            }

            result = self.api_client.create_task(task_data)
            if result:
                messagebox.showinfo("成功", f"任务创建成功！任务ID: {result.get('id')}")
                self.refresh()
            else:
                messagebox.showerror("错误", "任务创建失败")

        except Exception as e:
            messagebox.showerror("错误", f"创建任务失败: {e}")

    def create_multiple_quick_tasks(self, task_type, parameters, target_scope, group_ids):
        """为多个分组创建任务"""
        try:
            # 获取延迟设置
            delay_group = int(float(self.quick_delay_group.get()) * 1000)
            delay_like = int(float(self.quick_delay_like.get()) * 1000)

            created_tasks = []
            failed_tasks = []

            for group_id in group_ids:
                try:
                    task_data = {
                        "task_type": task_type,
                        "parameters": parameters,
                        "target_scope": target_scope,
                        "target_id": group_id,
                        "delay_group": delay_group,
                        "delay_like": delay_like
                    }

                    result = self.api_client.create_task(task_data)
                    if result:
                        group_name = self.quick_group_map.get(group_id, f"分组{group_id}")
                        created_tasks.append(f"{group_name} (ID: {result.get('id')})")
                    else:
                        group_name = self.quick_group_map.get(group_id, f"分组{group_id}")
                        failed_tasks.append(group_name)

                except Exception as e:
                    group_name = self.quick_group_map.get(group_id, f"分组{group_id}")
                    failed_tasks.append(f"{group_name}: {e}")

            # 显示结果
            message = ""
            if created_tasks:
                message += f"成功创建 {len(created_tasks)} 个任务:\n"
                message += "\n".join(created_tasks)

            if failed_tasks:
                if message:
                    message += "\n\n"
                message += f"失败 {len(failed_tasks)} 个任务:\n"
                message += "\n".join(failed_tasks)

            if created_tasks and not failed_tasks:
                messagebox.showinfo("全部成功", message)
            elif created_tasks and failed_tasks:
                messagebox.showwarning("部分成功", message)
            else:
                messagebox.showerror("全部失败", message)

            if created_tasks:
                self.refresh()

        except Exception as e:
            messagebox.showerror("错误", f"批量创建任务失败: {e}")

    def save_quick_settings(self):
        """保存快速创建设置"""
        try:
            settings = {
                "task_type": self.quick_task_type.get(),
                "target_scope": self.quick_target_scope.get(),
                "blogger_id": self.quick_blogger_id.get(),
                "like_id": self.quick_like_id.get(),
                "delay_group": self.quick_delay_group.get(),
                "delay_like": self.quick_delay_like.get()
            }

            import os
            # 使用相对于当前文件的路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            settings_dir = os.path.join(current_dir, "..", "settings")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)

            settings_file = os.path.join(settings_dir, "quick_task_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("保存成功", "快速创建设置已保存")
            print(f"设置已保存到: {settings_file}")

        except Exception as e:
            print(f"保存设置失败: {e}")
            messagebox.showerror("保存失败", f"保存设置失败: {e}")

    def load_quick_settings(self):
        """加载快速创建设置"""
        try:
            import os
            # 使用相对于当前文件的路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            settings_file = os.path.join(current_dir, "..", "settings", "quick_task_settings.json")

            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 应用设置
                if hasattr(self, 'quick_task_type'):
                    self.quick_task_type.set(settings.get("task_type", "点赞任务"))
                if hasattr(self, 'quick_target_scope'):
                    self.quick_target_scope.set(settings.get("target_scope", "设备分组"))
                if hasattr(self, 'quick_blogger_id'):
                    self.quick_blogger_id.set(settings.get("blogger_id", ""))
                if hasattr(self, 'quick_like_id'):
                    self.quick_like_id.set(settings.get("like_id", ""))
                if hasattr(self, 'quick_delay_group'):
                    self.quick_delay_group.set(settings.get("delay_group", "2"))
                if hasattr(self, 'quick_delay_like'):
                    self.quick_delay_like.set(settings.get("delay_like", "1"))

                # 更新目标选择
                self.update_quick_target_options()
                print(f"设置已加载: {settings_file}")

        except Exception as e:
            print(f"加载设置失败: {e}")  # 静默失败，不影响使用