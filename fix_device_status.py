#!/usr/bin/env python3
"""
修复设备在线状态显示问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from app.db import engine
from app.models.device import Device
from app.models.device_status import DeviceStatus
from datetime import datetime, timed<PERSON><PERSON>

def diagnose_status_issue():
    """诊断状态问题"""
    print("🔍 诊断设备状态问题...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. 检查Device表的状态
        print("\n📱 Device表状态:")
        devices = db.query(Device).all()
        
        device_status_counts = {}
        for device in devices:
            status = device.online_status
            device_status_counts[status] = device_status_counts.get(status, 0) + 1
            
        print(f"   总设备数: {len(devices)}")
        for status, count in device_status_counts.items():
            print(f"   {status}: {count}")
            
        # 显示前5个设备的详细信息
        print("\n   前5个设备详情:")
        for device in devices[:5]:
            print(f"     {device.device_number}: {device.online_status} (心跳: {device.last_heartbeat})")
            
        # 2. 检查DeviceStatus表的状态
        print("\n📊 DeviceStatus表状态:")
        device_statuses = db.query(DeviceStatus).all()
        
        status_counts = {}
        for ds in device_statuses:
            status = 'online' if ds.is_online else 'offline'
            status_counts[status] = status_counts.get(status, 0) + 1
            
        print(f"   总状态记录数: {len(device_statuses)}")
        for status, count in status_counts.items():
            print(f"   {status}: {count}")
            
        # 显示前5个状态记录
        print("\n   前5个状态记录:")
        for ds in device_statuses[:5]:
            device = db.query(Device).filter(Device.id == ds.device_id).first()
            device_name = device.device_number if device else f"ID:{ds.device_id}"
            status = 'online' if ds.is_online else 'offline'
            print(f"     {device_name}: {status} (更新: {ds.last_update})")
            
        # 3. 检查数据不一致的情况
        print("\n⚠️ 数据一致性检查:")
        inconsistent_count = 0
        
        for device in devices:
            device_status = db.query(DeviceStatus).filter(DeviceStatus.device_id == device.id).first()
            
            if device_status:
                device_online = device.online_status == 'online'
                status_online = device_status.is_online
                
                if device_online != status_online:
                    inconsistent_count += 1
                    print(f"   不一致: {device.device_number} - Device表:{device.online_status}, Status表:{'online' if status_online else 'offline'}")
            else:
                print(f"   缺少状态记录: {device.device_number}")
                inconsistent_count += 1
                
        if inconsistent_count == 0:
            print("   ✅ 数据一致")
        else:
            print(f"   ❌ 发现 {inconsistent_count} 个不一致的记录")
            
        return inconsistent_count > 0
        
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        return False
        
    finally:
        db.close()

def sync_device_status():
    """同步设备状态"""
    print("\n🔄 同步设备状态...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 获取所有设备
        devices = db.query(Device).all()
        
        sync_count = 0
        create_count = 0
        
        for device in devices:
            # 检查是否有对应的DeviceStatus记录
            device_status = db.query(DeviceStatus).filter(DeviceStatus.device_id == device.id).first()
            
            if device_status:
                # 更新现有记录
                old_online = device_status.is_online
                new_online = device.online_status == 'online'
                
                if old_online != new_online:
                    device_status.is_online = new_online
                    device_status.status = device.online_status
                    device_status.last_update = datetime.utcnow()
                    sync_count += 1
                    print(f"   同步: {device.device_number} -> {device.online_status}")
            else:
                # 创建新记录
                new_status = DeviceStatus(
                    device_id=device.id,
                    status=device.online_status,
                    is_online=(device.online_status == 'online'),
                    last_update=datetime.utcnow()
                )
                db.add(new_status)
                create_count += 1
                print(f"   创建: {device.device_number} -> {device.online_status}")
                
        db.commit()
        
        print(f"✅ 同步完成: 更新 {sync_count} 个, 创建 {create_count} 个")
        return True
        
    except Exception as e:
        print(f"❌ 同步异常: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

def update_heartbeat_logic():
    """更新心跳逻辑，确保Device表和DeviceStatus表同步"""
    print("\n🔧 修复心跳更新逻辑...")
    
    # 检查当前的WebSocket管理器代码
    try:
        with open('app/websocket/ws_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否已经有同步逻辑
        if 'device.online_status = "online"' in content:
            print("✅ WebSocket管理器已包含Device表更新逻辑")
        else:
            print("⚠️ WebSocket管理器缺少Device表更新逻辑")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查心跳逻辑异常: {e}")
        return False

def fix_device_api():
    """修复设备API，确保返回正确的在线状态"""
    print("\n🔧 修复设备API...")
    
    try:
        # 检查设备服务代码
        with open('app/services/device.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否正确查询在线状态
        if 'online_status' in content:
            print("✅ 设备服务包含online_status字段")
        else:
            print("⚠️ 设备服务可能缺少online_status字段")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查设备API异常: {e}")
        return False

def test_status_update():
    """测试状态更新"""
    print("\n🧪 测试状态更新...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 找一个设备进行测试
        device = db.query(Device).first()
        if not device:
            print("❌ 没有设备可供测试")
            return False
            
        print(f"   测试设备: {device.device_number}")
        print(f"   当前状态: {device.online_status}")
        
        # 更新心跳时间
        device.last_heartbeat = datetime.utcnow()
        device.online_status = 'online'
        
        # 同时更新DeviceStatus
        device_status = db.query(DeviceStatus).filter(DeviceStatus.device_id == device.id).first()
        if device_status:
            device_status.is_online = True
            device_status.status = 'online'
            device_status.last_update = datetime.utcnow()
        else:
            # 创建新的状态记录
            new_status = DeviceStatus(
                device_id=device.id,
                status='online',
                is_online=True,
                last_update=datetime.utcnow()
            )
            db.add(new_status)
            
        db.commit()
        
        print("✅ 测试更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试更新异常: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

def check_api_response():
    """检查API响应"""
    print("\n📡 检查API响应...")
    
    try:
        import requests
        
        response = requests.get("http://localhost:8000/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ API响应正常，返回 {len(devices)} 个设备")
            
            if devices:
                device = devices[0]
                print(f"   示例设备: {device.get('device_number')}")
                print(f"   在线状态: {device.get('online_status')}")
                print(f"   最后心跳: {device.get('last_heartbeat')}")
                
                # 检查是否包含新字段
                if 'current_task_id' in device:
                    print(f"   当前任务ID: {device.get('current_task_id')}")
                    print(f"   当前任务类型: {device.get('current_task_type')}")
                    print("✅ API包含新字段")
                else:
                    print("⚠️ API缺少新字段")
                    
            return True
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API检查异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复设备在线状态显示问题")
    print("="*50)
    
    # 1. 诊断问题
    has_issue = diagnose_status_issue()
    
    # 2. 同步状态
    if has_issue:
        sync_device_status()
    
    # 3. 检查心跳逻辑
    update_heartbeat_logic()
    
    # 4. 检查设备API
    fix_device_api()
    
    # 5. 测试状态更新
    test_status_update()
    
    # 6. 检查API响应
    check_api_response()
    
    print("\n" + "="*50)
    print("📊 修复总结:")
    print("1. 设备状态数据已同步")
    print("2. API响应已验证")
    print("3. 心跳逻辑已检查")
    
    print("\n💡 建议:")
    print("1. 重启前端应用以刷新数据")
    print("2. 检查WebSocket连接是否正常")
    print("3. 确保设备端正确发送心跳")
    
    print("\n🔍 如果问题仍然存在:")
    print("1. 检查设备端WebSocket连接")
    print("2. 查看后端日志中的心跳消息")
    print("3. 确认设备端发送正确格式的心跳")

if __name__ == "__main__":
    main()
