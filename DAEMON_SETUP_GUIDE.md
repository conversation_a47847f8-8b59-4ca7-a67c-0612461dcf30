# 微博任务管理系统 - 永久启动配置指南

## 🎯 解决的问题

1. **永久启动问题**: 让服务在退出终端后继续运行，并支持开机自启
2. **分组延迟问题**: 修复前后端延迟单位不统一的问题

## 🚀 快速开始

### 1. 配置永久启动

```bash
# 给脚本添加执行权限
chmod +x setup_daemon_service.sh

# 运行配置脚本（需要root权限）
sudo ./setup_daemon_service.sh
```

### 2. 测试分组延迟功能

```bash
# 给测试脚本添加执行权限
chmod +x test_group_delay.py

# 运行延迟测试
python3 test_group_delay.py
```

## 📋 详细说明

### 永久启动配置

`setup_daemon_service.sh` 脚本会自动完成以下配置：

1. **检查项目环境**
   - 验证项目文件完整性
   - 检查虚拟环境
   - 创建或检查.env配置文件

2. **创建systemd服务**
   - 生成 `/etc/systemd/system/wb-system.service`
   - 配置服务自动重启
   - 设置安全权限

3. **启用开机自启**
   - 自动启用systemd服务
   - 配置服务依赖关系

4. **创建管理脚本**
   - 生成 `wb-system-ctl.sh` 便捷管理脚本

### 分组延迟修复

修复了前后端延迟单位不统一的问题：

- **前端**: 用户输入秒，转换为毫秒发送
- **后端**: 接收毫秒，转换为秒使用
- **结果**: 延迟时间正确应用

## 🔧 服务管理

### 使用systemctl命令

```bash
# 启动服务
systemctl start wb-system

# 停止服务
systemctl stop wb-system

# 重启服务
systemctl restart wb-system

# 查看状态
systemctl status wb-system

# 查看日志
journalctl -u wb-system -f

# 启用开机自启
systemctl enable wb-system

# 禁用开机自启
systemctl disable wb-system
```

### 使用便捷管理脚本

```bash
# 启动服务
./wb-system-ctl.sh start

# 停止服务
./wb-system-ctl.sh stop

# 重启服务
./wb-system-ctl.sh restart

# 查看状态
./wb-system-ctl.sh status

# 查看实时日志
./wb-system-ctl.sh logs

# 启用开机自启
./wb-system-ctl.sh enable

# 禁用开机自启
./wb-system-ctl.sh disable
```

## 🧪 延迟功能测试

### 测试内容

`test_group_delay.py` 脚本会测试：

1. **单设备延迟测试**
   - 创建单设备任务
   - 验证分组延迟和操作延迟

2. **分组延迟测试**
   - 创建分组任务
   - 验证设备间的延迟间隔

### 测试输出

测试脚本会显示：
- API连接状态
- 系统设备和分组信息
- 任务创建和执行过程
- 延迟应用情况

### 观察要点

在日志中查找以下信息：
```
⏰ 应用分组延迟: 设备1 等待 3.0秒 (任务间延迟3.0秒)
⏰ 应用操作延迟: 1.0秒
```

## 📊 服务特性

### 自动重启
- 服务异常退出时自动重启
- 重启间隔: 3秒
- 最大重启次数: 5次/分钟

### 日志管理
- 日志自动记录到systemd journal
- 支持实时日志查看
- 日志轮转由系统管理

### 安全设置
- 以普通用户权限运行
- 启用安全沙箱
- 限制文件系统访问

## 🔍 故障排查

### 服务启动失败

1. **检查服务状态**
   ```bash
   systemctl status wb-system
   ```

2. **查看详细日志**
   ```bash
   journalctl -u wb-system --no-pager
   ```

3. **检查配置文件**
   ```bash
   cat /etc/systemd/system/wb-system.service
   ```

### 延迟不生效

1. **运行测试脚本**
   ```bash
   python3 test_group_delay.py
   ```

2. **查看实时日志**
   ```bash
   journalctl -u wb-system -f
   ```

3. **检查任务参数**
   - 确认前端发送的延迟值
   - 验证后端接收的延迟值

## 💡 使用建议

### 生产环境
- 定期检查服务状态
- 监控日志文件大小
- 备份重要配置文件

### 开发环境
- 使用测试脚本验证功能
- 查看详细日志调试问题
- 根据需要调整延迟参数

## 📞 技术支持

如果遇到问题：

1. 查看本文档的故障排查部分
2. 运行测试脚本诊断问题
3. 检查系统日志获取详细信息
4. 确保所有依赖服务正常运行

---

**注意**: 配置永久启动需要root权限，请确保在安全的环境中运行配置脚本。
