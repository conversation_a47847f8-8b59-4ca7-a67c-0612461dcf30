#!/usr/bin/env python3
"""
持续连接的分组延迟测试
保持设备连接，测试任务完成后是否能正确分发下一个任务
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class ContinuousTestDevice:
    """持续连接测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []
        self.task_counts = {}
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            if self.running:
                log_with_time(f"📨 设备 {self.device_number} 消息监听异常: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                task_id = data.get('task_id')
                
                # 统计任务接收次数
                if task_id in self.task_counts:
                    self.task_counts[task_id] += 1
                    log_with_time(f"⚠️ 设备 {self.device_number} 重复收到任务 {task_id} (第{self.task_counts[task_id]}次)")
                else:
                    self.task_counts[task_id] = 1
                    log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id}")
                
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                pass
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_delay_test_task(task_name, group_id, delay_group_sec=10):
    """创建分组延迟测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": f"continuous_test_{int(time.time())}"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 秒转毫秒
        "delay_like": 500  # 0.5秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 分组延迟: {delay_group_sec}秒")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_continuous_group_delay():
    """持续连接的分组延迟测试"""
    log_with_time("=== 持续连接分组延迟测试 ===")
    
    # 创建测试设备
    device = ContinuousTestDevice("ceshi212")
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(3)
        
        # 2. 验证设备在线
        log_with_time("🔍 验证设备在线状态...")
        import sys
        sys.path.append('.')
        from app.services.new_simple_scheduler import new_simple_scheduler
        
        device_online = await new_simple_scheduler._is_device_online(12)
        log_with_time(f"设备12在线状态: {device_online}")
        
        if not device_online:
            log_with_time("❌ 设备离线，测试终止")
            return
        
        # 3. 创建测试任务
        delay_seconds = 10  # 10秒分组延迟
        log_with_time(f"🚀 创建持续测试任务（{delay_seconds}秒延迟）")
        
        test_start_time = time.time()
        
        # 创建3个任务，间隔短
        tasks = []
        for i in range(3):
            task_name = f"持续测试{i+1}"
            task = create_delay_test_task(task_name, 5, delay_seconds)
            if task:
                tasks.append(task)
            await asyncio.sleep(0.5)  # 任务创建间隔0.5秒
        
        if len(tasks) < 3:
            log_with_time("❌ 任务创建不完整，测试终止")
            return
        
        log_with_time(f"✅ 成功创建 {len(tasks)} 个任务")
        
        # 4. 持续监控任务执行 - 关键：保持设备连接
        expected_time = len(tasks) * (1 + delay_seconds)  # 每个任务1秒执行 + 10秒延迟
        log_with_time(f"⏳ 开始监控任务执行（预计需要约{expected_time}秒）...")
        log_with_time("💡 设备将保持连接，监控分组延迟效果")
        
        # 每5秒检查一次进度
        check_interval = 5
        total_checks = int((expected_time + 15) / check_interval)  # 额外等待15秒
        
        for check in range(total_checks):
            await asyncio.sleep(check_interval)
            
            # 检查进度
            received_count = len(device.received_tasks)
            elapsed_time = time.time() - test_start_time
            
            log_with_time(f"📊 第{(check+1)*check_interval}秒检查: 已收到 {received_count}/{len(tasks)} 个任务")
            
            # 检查调度器状态
            status = new_simple_scheduler.get_status()
            log_with_time(f"📊 调度器状态: 运行={status['is_running']}, 已处理={status['processed_tasks_count']}, 处理中={status['processing_devices_count']}")
            
            # 检查设备在线状态
            device_online = await new_simple_scheduler._is_device_online(12)
            log_with_time(f"📊 设备12在线: {device_online}")
            
            # 如果收到所有任务，提前结束
            if received_count >= len(tasks):
                log_with_time("✅ 所有任务已接收，提前结束监控")
                break
        
        # 5. 分析分组延迟效果
        log_with_time("📊 分组延迟效果分析...")
        
        # 检查重复任务
        duplicate_tasks = []
        for task_id, count in device.task_counts.items():
            if count > 1:
                duplicate_tasks.append((task_id, count))
        
        if duplicate_tasks:
            log_with_time("❌ 发现重复任务:")
            for task_id, count in duplicate_tasks:
                log_with_time(f"   任务{task_id}: 收到{count}次")
        else:
            log_with_time("✅ 没有重复任务，去重功能正常")
        
        # 检查分组延迟
        if len(device.task_times) >= 2:
            log_with_time("分组延迟效果分析:")
            
            for i, receive_time in enumerate(device.task_times):
                relative_time = receive_time - test_start_time
                task_id = device.received_tasks[i].get('task_id')
                log_with_time(f"   任务{task_id}: 第{relative_time:.1f}秒接收")
            
            log_with_time("\\n任务间隔分析:")
            intervals = []
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                intervals.append(interval)
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期
                expected_min = delay_seconds + 0.5  # 10 + 0.5 = 10.5秒
                expected_max = delay_seconds + 2.0  # 10 + 2.0 = 12秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒在预期范围)")
                else:
                    log_with_time(f"   ❌ 分组延迟异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒)")
            
            # 总体分析
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                log_with_time(f"\\n📊 平均间隔: {avg_interval:.1f}秒")
                log_with_time(f"📊 预期间隔: {delay_seconds + 1}秒左右")
                
                if delay_seconds + 0.5 <= avg_interval <= delay_seconds + 2.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                    log_with_time("✅ 任务完成后能正确分发下一个任务！")
                else:
                    log_with_time("❌ 分组延迟功能仍有问题")
                    log_with_time(f"   实际平均间隔: {avg_interval:.1f}秒")
                    log_with_time(f"   预期间隔范围: {delay_seconds + 0.5}-{delay_seconds + 2.0}秒")
        else:
            log_with_time("❌ 收到的任务数量不足，无法分析间隔")
        
        total_time = time.time() - test_start_time
        log_with_time(f"\\n📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: {len(tasks)}")
        
        # 最终判断
        if len(device.received_tasks) >= len(tasks):
            log_with_time("🎉 最终结论: 任务分发完整！")
            if len(device.task_times) >= 2:
                intervals = []
                for i in range(len(device.task_times) - 1):
                    intervals.append(device.task_times[i + 1] - device.task_times[i])
                
                avg_interval = sum(intervals) / len(intervals)
                if delay_seconds + 0.5 <= avg_interval <= delay_seconds + 2.0:
                    log_with_time("🎉 分组延迟功能完全正常！")
                    log_with_time("✅ 任务完成后能正确等待并分发下一个任务！")
                else:
                    log_with_time("⚠️ 分组延迟时间不准确")
        else:
            log_with_time("❌ 最终结论: 任务分发不完整")
            log_with_time("💡 可能原因: 调度器停止运行或设备离线检查有问题")
        
    finally:
        # 保持连接一段时间，确保所有任务都能完成
        log_with_time("⏳ 保持连接5秒，确保任务完成...")
        await asyncio.sleep(5)
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 持续连接分组延迟测试")
    print("💡 保持设备连接，测试任务完成后是否能正确分发下一个任务")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\\n" + "=" * 50)
    print("🚀 开始持续连接测试")
    print("=" * 50)
    
    # 持续连接分组延迟测试
    await test_continuous_group_delay()
    
    print("\\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 这个测试验证了:")
    print("   1. 设备保持连接状态")
    print("   2. 任务完成后调度器是否继续工作")
    print("   3. 分组延迟是否在任务间正确应用")
    print("   4. 是否会出现任务卡住的问题")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
