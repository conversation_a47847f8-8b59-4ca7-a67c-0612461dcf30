([('run.exe',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\build\\run\\run.exe',
   'EXECUTABLE'),
  ('python39.dll', 'D:\\myProgram\\Python39\\python39.dll', 'BINARY'),
  ('_decimal.pyd', 'D:\\myProgram\\Python39\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\myProgram\\Python39\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\myProgram\\Python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\myProgram\\Python39\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\myProgram\\Python39\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\myProgram\\Python39\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\myProgram\\Python39\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\myProgram\\Python39\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\myProgram\\Python39\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\myProgram\\Python39\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\myProgram\\Python39\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\myProgram\\Python39\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\myProgram\\Python39\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\myProgram\\Python39\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'D:\\myProgram\\Python39\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\myProgram\\Python39\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\myProgram\\Python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\myProgram\\Python39\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\myProgram\\Python39\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'D:\\myProgram\\Python39\\python3.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\myProgram\\Python39\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\myProgram\\Python39\\DLLs\\tk86t.dll', 'BINARY'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\myProgram\\Python39\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\myProgram\\Python39\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\myProgram\\Python39\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\myProgram\\Python39\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\myProgram\\Python39\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\myProgram\\Python39\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\myProgram\\Python39\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\myProgram\\Python39\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('base_library.zip',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\build\\run\\base_library.zip',
   'DATA')],)
