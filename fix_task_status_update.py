#!/usr/bin/env python3
"""
任务状态更新问题修复脚本
解决数据库状态更新延迟的核心问题
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_scheduler_duplicate_init():
    """修复调度器中重复的__init__方法"""
    print("🔧 修复调度器重复__init__方法...")
    
    scheduler_file = "app/services/scheduler.py"
    
    try:
        with open(scheduler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找重复的__init__方法
        lines = content.split('\n')
        init_lines = []
        for i, line in enumerate(lines):
            if line.strip().startswith('def __init__(self):'):
                init_lines.append(i)
        
        if len(init_lines) > 1:
            print(f"   发现 {len(init_lines)} 个__init__方法，位于行: {[i+1 for i in init_lines]}")
            
            # 移除第二个__init__方法 (第303行附近)
            if len(init_lines) >= 2:
                second_init_start = init_lines[1]
                # 找到第二个__init__方法的结束位置
                second_init_end = second_init_start
                indent_level = len(lines[second_init_start]) - len(lines[second_init_start].lstrip())
                
                for i in range(second_init_start + 1, len(lines)):
                    line = lines[i]
                    if line.strip() == "":
                        continue
                    current_indent = len(line) - len(line.lstrip())
                    if current_indent <= indent_level and line.strip():
                        second_init_end = i - 1
                        break
                
                # 移除重复的__init__方法
                del lines[second_init_start:second_init_end + 1]
                
                # 写回文件
                with open(scheduler_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                print(f"   ✅ 已移除重复的__init__方法 (行 {second_init_start+1}-{second_init_end+1})")
        else:
            print("   ✅ 未发现重复的__init__方法")
            
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")

def fix_status_enum_inconsistency():
    """修复状态枚举不一致问题"""
    print("🔧 修复状态枚举不一致...")
    
    files_to_fix = [
        "app/websocket/ws_manager.py",
        "app/services/scheduler.py"
    ]
    
    for file_path in files_to_fix:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换错误的状态值
            original_content = content
            content = content.replace("'completed'", "'done'")
            content = content.replace('"completed"', '"done"')
            content = content.replace("== 'completed'", "== 'done'")
            content = content.replace("!= 'completed'", "!= 'done'")
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   ✅ 已修复 {file_path} 中的状态枚举")
            else:
                print(f"   ✅ {file_path} 状态枚举正确")
                
        except Exception as e:
            print(f"   ❌ 修复 {file_path} 失败: {e}")

def create_unified_status_updater():
    """创建统一的状态更新服务"""
    print("🔧 创建统一状态更新服务...")
    
    status_updater_content = '''"""
统一任务状态更新服务
解决多路径状态更新冲突问题
"""

import asyncio
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db import get_db
from app.models.task import TaskQueue
from typing import Optional

class TaskStatusUpdater:
    """统一的任务状态更新服务"""
    
    def __init__(self):
        self._update_lock = asyncio.Lock()
        self._update_queue = asyncio.Queue()
        self._running = False
    
    async def start(self):
        """启动状态更新服务"""
        if not self._running:
            self._running = True
            asyncio.create_task(self._process_updates())
    
    async def stop(self):
        """停止状态更新服务"""
        self._running = False
    
    async def update_task_status(
        self, 
        task_id: int, 
        device_id: int, 
        status: str, 
        result_summary: Optional[str] = None
    ):
        """异步更新任务状态"""
        update_data = {
            'task_id': task_id,
            'device_id': device_id,
            'status': status,
            'result_summary': result_summary,
            'timestamp': datetime.utcnow()
        }
        await self._update_queue.put(update_data)
    
    async def _process_updates(self):
        """处理状态更新队列"""
        while self._running:
            try:
                # 批量处理更新
                updates = []
                try:
                    # 获取第一个更新
                    update = await asyncio.wait_for(
                        self._update_queue.get(), 
                        timeout=1.0
                    )
                    updates.append(update)
                    
                    # 尝试获取更多更新进行批处理
                    while len(updates) < 10:
                        try:
                            update = self._update_queue.get_nowait()
                            updates.append(update)
                        except asyncio.QueueEmpty:
                            break
                            
                except asyncio.TimeoutError:
                    continue
                
                if updates:
                    await self._batch_update(updates)
                    
            except Exception as e:
                print(f"状态更新处理异常: {e}")
                await asyncio.sleep(1)
    
    async def _batch_update(self, updates):
        """批量更新状态"""
        async with self._update_lock:
            db = None
            try:
                db = next(get_db())
                
                for update in updates:
                    task_queue = db.query(TaskQueue).filter(
                        TaskQueue.task_id == update['task_id'],
                        TaskQueue.device_id == update['device_id']
                    ).first()
                    
                    if task_queue:
                        task_queue.status = update['status']
                        if update['status'] in ['done', 'failed']:
                            task_queue.finish_time = update['timestamp']
                        if update['result_summary']:
                            task_queue.result_summary = update['result_summary']
                
                db.commit()
                print(f"✅ 批量更新 {len(updates)} 个任务状态")
                
            except Exception as e:
                print(f"批量更新失败: {e}")
                if db:
                    db.rollback()
            finally:
                if db:
                    db.close()

# 全局状态更新器实例
status_updater = TaskStatusUpdater()
'''
    
    try:
        with open("app/services/status_updater.py", 'w', encoding='utf-8') as f:
            f.write(status_updater_content)
        print("   ✅ 已创建统一状态更新服务")
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")

def optimize_db_config():
    """优化数据库配置"""
    print("🔧 优化数据库配置...")
    
    try:
        with open("app/db.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 优化连接池配置
        optimized_config = '''from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from app.config import DATABASE_URL

engine = create_engine(
    DATABASE_URL,
    pool_size=30,           # 增加连接池大小
    max_overflow=50,        # 增加溢出连接数
    pool_timeout=30,        # 增加超时时间
    pool_pre_ping=True,
    isolation_level="REPEATABLE READ",  # 改为更严格的隔离级别
    pool_recycle=1800,      # 增加连接回收时间
    connect_args={
        "connect_timeout": 20,
        "autocommit": False
    },
    echo_pool=False         # 关闭连接池日志以提高性能
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 依赖注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
'''
        
        with open("app/db.py", 'w', encoding='utf-8') as f:
            f.write(optimized_config)
        
        print("   ✅ 已优化数据库配置")
        
    except Exception as e:
        print(f"   ❌ 优化失败: {e}")

async def main():
    """主修复函数"""
    print("🚀 开始修复任务状态更新问题...\n")
    
    # 1. 修复重复的__init__方法
    fix_scheduler_duplicate_init()
    print()
    
    # 2. 修复状态枚举不一致
    fix_status_enum_inconsistency()
    print()
    
    # 3. 创建统一状态更新服务
    create_unified_status_updater()
    print()
    
    # 4. 优化数据库配置
    optimize_db_config()
    print()
    
    print("🎉 修复完成！")
    print("\n📋 后续步骤:")
    print("1. 重启应用服务")
    print("2. 监控任务状态更新性能")
    print("3. 检查数据库连接池使用情况")
    print("4. 验证状态更新的一致性")

if __name__ == "__main__":
    asyncio.run(main())
