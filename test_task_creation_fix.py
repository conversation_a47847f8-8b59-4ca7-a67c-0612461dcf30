#!/usr/bin/env python3
"""
测试任务创建修复
"""

import requests
import json

def test_task_creation():
    """测试任务创建"""
    print("🚀 测试任务创建修复...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取分组
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取分组数据: {response.status_code}")
            return False
            
        groups = response.json()
        if not groups:
            print("⚠️ 没有可用的分组")
            return True
            
        test_group = groups[0]
        group_id = test_group.get('id')
        group_name = test_group.get('group_name', '测试分组')
        
        print(f"📋 使用分组: {group_name} (ID: {group_id})")
        
        # 测试分组任务创建
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "fix_test_blogger",
                "like_id": "fix_test_like",
                "delay_click": 500
            },
            "target_scope": "group",
            "target_id": group_id,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        print("📤 发送任务创建请求...")
        print(f"   任务数据: {json.dumps(task_data, indent=2)}")
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功!")
            print(f"   任务ID: {result.get('id')}")
            print(f"   任务类型: {result.get('task_type')}")
            print(f"   状态: {result.get('status')}")
            print(f"   目标分组: {group_name}")
            return True
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务创建测试异常: {e}")
        return False

def test_device_task_creation():
    """测试设备任务创建"""
    print("\n📱 测试设备任务创建...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取设备数据: {response.status_code}")
            return False
            
        devices = response.json()
        if not devices:
            print("⚠️ 没有可用的设备")
            return True
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number', '测试设备')
        
        print(f"📱 使用设备: {device_number} (ID: {device_id})")
        
        # 测试设备任务创建
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "fix_device_blogger",
                "like_id": "fix_device_like",
                "delay_click": 300
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 1500
        }
        
        print("📤 发送设备任务创建请求...")
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 设备任务创建成功!")
            print(f"   任务ID: {result.get('id')}")
            print(f"   目标设备: {device_number}")
            return True
        else:
            print(f"❌ 设备任务创建失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 设备任务创建测试异常: {e}")
        return False

def test_backend_status():
    """测试后端状态"""
    print("🔗 测试后端状态...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试根路径
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   根路径状态: {response.status_code}")
        
        # 测试任务API
        response = requests.get(f"{base_url}/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 后端正常，当前任务数: {len(tasks)}")
            return True
        else:
            print(f"❌ 任务API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 后端连接异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 任务创建修复测试")
    print("="*50)
    
    tests = [
        ("后端状态", test_backend_status),
        ("分组任务创建", test_task_creation),
        ("设备任务创建", test_device_task_creation)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*50)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:
        print("🎉 任务创建修复成功！")
        
        print("\n✅ 修复成果:")
        print("• 🔧 修复了调度器的数据库连接问题")
        print("• 📝 简化了任务创建流程")
        print("• 🚀 支持分组和设备两种任务创建")
        print("• ⏱️ 支持延迟参数传递")
        print("• 📊 任务状态正确设置")
        
        print("\n💡 现在可以:")
        print("• 📋 在任务管理页面创建分组任务")
        print("• 📱 在任务管理页面创建设备任务")
        print("• ⏱️ 设置点赞延迟、分组间隔、操作延迟")
        print("• 📊 查看任务创建结果和状态")
        
    else:
        print("⚠️ 部分功能仍有问题，请检查后端日志")

if __name__ == "__main__":
    main()
