#!/usr/bin/env python3
"""
WebSocket调试工具
"""

import asyncio
import websockets
import json
from datetime import datetime

class WebSocketDebugger:
    def __init__(self, uri="ws://localhost:8000/ws/debug_device"):
        self.uri = uri
        self.device_number = "debug_device"
        
    async def connect_and_test(self):
        """连接并测试WebSocket通信"""
        print(f"🔗 连接到 {self.uri}")
        
        try:
            async with websockets.connect(self.uri) as websocket:
                print("✅ WebSocket连接成功")
                
                # 1. 发送心跳测试
                await self.test_heartbeat(websocket)
                
                # 2. 等待接收任务
                await self.wait_for_task(websocket)
                
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            
    async def test_heartbeat(self, websocket):
        """测试心跳功能"""
        print("\n💓 测试心跳功能...")
        
        # 发送标准心跳
        heartbeat_msg = {
            "type": "heartbeat",
            "timestamp": datetime.utcnow().isoformat(),
            "device_number": self.device_number
        }
        
        await websocket.send(json.dumps(heartbeat_msg))
        print(f"→ 发送心跳: {heartbeat_msg}")
        
        # 等待响应
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"← 收到响应: {response}")
        except asyncio.TimeoutError:
            print("⚠️ 心跳响应超时")
            
    async def wait_for_task(self, websocket):
        """等待接收任务并模拟处理"""
        print("\n📋 等待接收任务...")
        
        while True:
            try:
                # 等待消息
                message = await asyncio.wait_for(websocket.recv(), timeout=30)
                print(f"← 收到消息: {message}")
                
                try:
                    data = json.loads(message)
                    
                    # 检查是否是任务消息
                    if data.get('type') in ['page_sign', 'like', 'sign', 'inex']:
                        await self.handle_task(websocket, data)
                    elif data.get('type') == 'error':
                        print(f"⚠️ 收到错误消息: {data}")
                    else:
                        print(f"ℹ️ 收到其他消息: {data}")
                        
                except json.JSONDecodeError:
                    print(f"⚠️ 非JSON消息: {message}")
                    
            except asyncio.TimeoutError:
                print("⏰ 等待消息超时，发送心跳...")
                await self.test_heartbeat(websocket)
                continue
                
    async def handle_task(self, websocket, task_data):
        """处理接收到的任务"""
        print(f"\n🎯 处理任务: {task_data}")
        
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        parameters = task_data.get('parameters', {})
        
        print(f"   任务ID: {task_id}")
        print(f"   任务类型: {task_type}")
        print(f"   参数: {parameters}")
        
        # 模拟任务处理时间
        print("⏳ 模拟任务执行中...")
        await asyncio.sleep(2)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": {
                "message": "任务执行成功",
                "execution_time": 2.0,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        await websocket.send(json.dumps(completion_msg))
        print(f"→ 发送任务完成: {completion_msg}")
        
        # 等待确认
        try:
            ack = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"← 收到确认: {ack}")
        except asyncio.TimeoutError:
            print("⚠️ 任务完成确认超时")

class TaskSender:
    """任务发送器 - 用于测试发送任务到设备"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        
    async def send_test_task(self):
        """发送测试任务"""
        import requests
        
        print("📤 发送测试任务...")
        
        # 创建测试任务
        task_data = {
            "task_type": "page_sign",
            "parameters": {
                "like_id": "test_like_123",
                "blogger_id": "test_blogger_456"
            },
            "target_scope": "single",
            "target_id": 1,  # 假设debug_device的ID是1
            "delay_group": 1000,
            "delay_like": 500
        }
        
        try:
            response = requests.post(f"{self.base_url}/tasks/", json=task_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 任务创建成功: {result}")
                return result.get('id')
            else:
                print(f"❌ 任务创建失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
            return None

async def main():
    """主函数"""
    print("🔧 WebSocket通信调试工具")
    print("="*50)
    
    # 选择测试模式
    print("选择测试模式:")
    print("1. 模拟设备端 (接收和处理任务)")
    print("2. 发送测试任务")
    print("3. 同时运行 (推荐)")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 只运行设备端
        debugger = WebSocketDebugger()
        await debugger.connect_and_test()
        
    elif choice == "2":
        # 只发送任务
        sender = TaskSender()
        await sender.send_test_task()
        
    elif choice == "3":
        # 同时运行
        print("\n🚀 启动完整测试...")
        
        # 启动设备端
        debugger = WebSocketDebugger()
        device_task = asyncio.create_task(debugger.connect_and_test())
        
        # 等待设备连接
        await asyncio.sleep(2)
        
        # 发送测试任务
        sender = TaskSender()
        task_id = await sender.send_test_task()
        
        if task_id:
            print(f"✅ 测试任务已发送 (ID: {task_id})")
            print("📱 观察设备端是否收到任务...")
            
        # 等待设备端完成
        await device_task
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 调试工具已停止")
    except Exception as e:
        print(f"\n❌ 调试工具异常: {e}")
        import traceback
        traceback.print_exc()
