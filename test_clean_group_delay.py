#!/usr/bin/env python3
"""
清洁的分组延迟测试
暂停旧任务，只测试新的分组延迟任务
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class CleanTestDevice:
    """清洁测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                task_id = data.get('task_id')
                task_type = data.get('type')
                log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id} ({task_type})")
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                pass  # 忽略ack消息
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_clean_task(task_name, group_id, delay_group_sec=5):
    """创建清洁的测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": f"clean_test_{int(time.time())}"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 秒转毫秒
        "delay_like": 500  # 0.5秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            log_with_time(f"   分组延迟: {delay_group_sec}秒")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_clean_group_delay():
    """清洁的分组延迟测试"""
    log_with_time("=== 清洁分组延迟测试 ===")
    
    # 1. 暂停所有任务
    log_with_time("⏸️ 暂停所有旧任务...")
    try:
        response = requests.post(f"{API_BASE_URL}/tasks/pause-all")
        if response.status_code == 200:
            log_with_time("✅ 所有旧任务已暂停")
        else:
            log_with_time("⚠️ 暂停任务可能失败")
    except:
        log_with_time("⚠️ 暂停任务失败")
    
    # 创建测试设备
    device = CleanTestDevice("ceshi212")  # 设备12，属于分组5
    
    try:
        # 2. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 3. 恢复任务处理
        log_with_time("▶️ 恢复任务处理...")
        try:
            response = requests.post(f"{API_BASE_URL}/tasks/resume-all")
            if response.status_code == 200:
                log_with_time("✅ 任务处理已恢复")
            else:
                log_with_time("⚠️ 恢复任务可能失败")
        except:
            log_with_time("⚠️ 恢复任务失败")
        
        await asyncio.sleep(2)
        
        # 4. 创建新的分组延迟测试任务
        delay_seconds = 5  # 5秒分组延迟（较短，便于测试）
        log_with_time(f"🚀 创建新的分组延迟测试任务（{delay_seconds}秒延迟）")
        
        test_start_time = time.time()
        
        # 创建3个连续任务
        tasks = []
        for i in range(3):
            task_name = f"清洁延迟测试{i+1}"
            task = create_clean_task(task_name, 5, delay_seconds)
            if task:
                tasks.append(task)
            await asyncio.sleep(1)  # 任务创建间隔1秒
        
        if len(tasks) < 3:
            log_with_time("❌ 任务创建不完整，测试终止")
            return
        
        # 5. 等待任务执行
        expected_time = len(tasks) * (1 + delay_seconds)  # 每个任务1秒执行 + 5秒延迟
        log_with_time(f"⏳ 等待任务执行（预计需要约{expected_time}秒）...")
        await asyncio.sleep(expected_time + 5)  # 额外等待5秒
        
        # 6. 分析结果
        log_with_time("📊 分析分组延迟效果...")
        
        if len(device.task_times) >= 2:
            log_with_time("任务接收时间间隔分析:")
            
            intervals = []
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                intervals.append(interval)
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期（任务完成1秒 + 分组延迟5秒 = 约6秒间隔）
                expected_min = delay_seconds + 0.5  # 5 + 0.5 = 5.5秒
                expected_max = delay_seconds + 2.0  # 5 + 2.0 = 7秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
            
            # 总体分析
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                log_with_time(f"📊 平均间隔: {avg_interval:.1f}秒")
                
                if delay_seconds + 0.5 <= avg_interval <= delay_seconds + 2.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                else:
                    log_with_time("⚠️ 分组延迟功能可能有问题")
        else:
            log_with_time("❌ 收到的任务数量不足，无法分析间隔")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: {len(tasks)}")
        
        # 显示收到的任务详情
        if device.received_tasks:
            log_with_time("📋 收到的任务详情:")
            for i, (task_data, receive_time) in enumerate(zip(device.received_tasks, device.task_times)):
                relative_time = receive_time - test_start_time
                task_id = task_data.get('task_id')
                task_type = task_data.get('type')
                log_with_time(f"   任务{task_id}({task_type}): 第{relative_time:.1f}秒接收")
        
        if len(device.received_tasks) == len(tasks):
            log_with_time("✅ 所有任务都被正确接收和处理")
        else:
            log_with_time(f"⚠️ 任务处理不完整: 预期{len(tasks)}个，实际{len(device.received_tasks)}个")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 清洁分组延迟测试")
    print("💡 暂停旧任务，专门测试新的分组延迟功能")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 50)
    print("🚀 开始测试")
    print("=" * 50)
    
    # 清洁分组延迟测试
    await test_clean_group_delay()
    
    print("\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 测试要点:")
    print("   1. 暂停所有旧任务，避免干扰")
    print("   2. 创建新的分组延迟任务")
    print("   3. 验证任务按顺序执行，有延迟间隔")
    print("   4. 分组延迟功能应该正常工作")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
