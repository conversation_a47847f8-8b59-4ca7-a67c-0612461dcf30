#!/usr/bin/env python3
"""
测试组任务执行机制
"""

import requests
import json
import time

def test_group_task_execution():
    """测试组任务执行机制"""
    print("🔍 测试组任务执行机制...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 1. 获取组信息
        print("\n📋 获取组信息:")
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            print(f"   总组数: {len(groups)}")
            
            for group in groups[:3]:
                group_id = group.get('id')
                group_name = group.get('name')
                device_count = group.get('device_count', 0)
                print(f"   - 组{group_id}: {group_name} ({device_count}个设备)")
        else:
            print(f"   ❌ 获取组信息失败: {response.status_code}")
            return False
        
        # 2. 选择一个有设备的组进行测试
        test_group = None
        for group in groups:
            if group.get('device_count', 0) > 1:  # 选择有多个设备的组
                test_group = group
                break
        
        if not test_group:
            print("   ⚠️ 没有找到有多个设备的组，创建单设备组测试")
            # 使用第一个组或创建测试组
            test_group = groups[0] if groups else None
        
        if not test_group:
            print("   ❌ 没有可用的组")
            return False
        
        group_id = test_group.get('id')
        group_name = test_group.get('name')
        device_count = test_group.get('device_count', 0)
        
        print(f"\n🎯 使用测试组: {group_name} (ID: {group_id}, {device_count}个设备)")
        
        # 3. 获取组内设备详情
        print("\n📱 获取组内设备详情:")
        response = requests.get(f"{base_url}/groups/{group_id}/devices", timeout=10)
        if response.status_code == 200:
            group_devices = response.json()
            print(f"   组内设备数: {len(group_devices)}")
            
            for device in group_devices:
                device_id = device.get('id')
                device_number = device.get('device_number')
                online_status = device.get('online_status')
                print(f"     - 设备{device_id}: {device_number} ({online_status})")
        else:
            print(f"   ❌ 获取组内设备失败: {response.status_code}")
            return False
        
        # 4. 创建组任务
        print(f"\n🚀 创建组任务...")
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "group_test_blogger",
                "like_id": "group_test_like",
                "delay_click": 500
            },
            "target_scope": "group",
            "target_id": group_id,
            "delay_like": 2000  # 2秒间隔
        }
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            print(f"   ✅ 组任务创建成功: 任务ID {task_id}")
        else:
            print(f"   ❌ 组任务创建失败: {response.status_code}")
            return False
        
        # 5. 监控任务分发和执行
        print(f"\n🔍 监控任务分发和执行...")
        
        for check_round in range(10):
            time.sleep(3)
            
            print(f"\n   === 第{check_round+1}次检查 ===")
            
            # 检查主任务状态
            response = requests.get(f"{base_url}/tasks/{task_id}", timeout=10)
            if response.status_code == 200:
                task_info = response.json()
                task_status = task_info.get('status')
                print(f"   主任务状态: {task_status}")
            
            # 检查TaskQueue中的设备任务状态
            response = requests.get(f"{base_url}/tasks/", timeout=10)
            if response.status_code == 200:
                all_tasks = response.json()
                
                # 找到我们的任务
                our_task = None
                for task in all_tasks:
                    if task.get('id') == task_id:
                        our_task = task
                        break
                
                if our_task:
                    print(f"   任务详情: {our_task.get('task_type')} -> {our_task.get('target_scope')}({our_task.get('target_id')})")
            
            # 检查运行中任务
            response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
            if response.status_code == 200:
                running_info = response.json()
                running_tasks = running_info.get('tasks', [])
                
                # 找到与我们任务相关的运行中任务
                related_tasks = [t for t in running_tasks if t.get('task_id') == task_id]
                
                print(f"   运行中的设备任务: {len(related_tasks)}个")
                for task in related_tasks:
                    device_id = task.get('device_id')
                    runtime = task.get('runtime_minutes', 0)
                    remaining = task.get('remaining_minutes', 0)
                    print(f"     - 设备{device_id}: 运行{runtime}分钟, 剩余{remaining:.1f}分钟")
            
            # 如果没有运行中任务，可能已经完成
            if not related_tasks and check_round > 2:
                print("   ℹ️ 没有运行中任务，可能已经完成")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 组任务执行测试异常: {e}")
        return False

def analyze_group_task_mechanism():
    """分析组任务执行机制"""
    print("\n📊 组任务执行机制分析:")
    
    print("\n🔍 根据代码分析:")
    print("1. 📋 组任务分发机制:")
    print("   - 当创建组任务时，调用 _dispatch_to_group() 方法")
    print("   - 为组内每个在线设备创建独立的 TaskQueue 记录")
    print("   - 每个设备都会收到相同的任务")
    
    print("\n2. 🔄 设备执行机制:")
    print("   - 每个设备有独立的任务队列 (device_queues)")
    print("   - 每个设备有独立的处理线程 (_process_device_queue)")
    print("   - 设备按照自己队列中的任务顺序执行")
    
    print("\n3. ⏰ 执行时序:")
    print("   - 同一组的设备会并行执行相同任务")
    print("   - 不是排队执行，而是各自独立执行")
    print("   - 每个设备都有自己的任务执行时间线")
    
    print("\n4. 🎯 关键特点:")
    print("   - ✅ 并行执行: 组内设备同时执行任务")
    print("   - ✅ 独立队列: 每个设备维护自己的任务队列")
    print("   - ✅ 状态独立: 每个设备的任务状态独立管理")
    print("   - ✅ 超时独立: 每个设备的超时监控独立")
    
    print("\n5. 📝 代码证据:")
    print("   - _dispatch_to_group() 中的循环:")
    print("     for device in online_devices:")
    print("         await self._add_to_device_queue(device.id, task)")
    print("   - 每个设备都会调用 _add_to_device_queue()")
    print("   - 创建独立的 TaskQueue 记录")

def main():
    """主函数"""
    print("🧪 组任务执行机制测试")
    print("="*60)
    
    # 分析机制
    analyze_group_task_mechanism()
    
    # 实际测试
    test_result = test_group_task_execution()
    
    print("\n" + "="*60)
    print("📊 测试结果:")
    
    if test_result:
        print("✅ 组任务执行测试通过")
    else:
        print("❌ 组任务执行测试失败")
    
    print("\n🎯 结论:")
    print("同一个组的设备拿到任务后是 **各自执行自己的**，不是排队执行！")
    
    print("\n💡 具体说明:")
    print("• 🔄 **并行执行**: 组内所有设备同时开始执行相同任务")
    print("• ⏰ **独立时序**: 每个设备按自己的节奏执行，不等待其他设备")
    print("• 📋 **独立队列**: 每个设备维护自己的任务队列")
    print("• 🎯 **独立状态**: 每个设备的任务完成状态独立管理")
    print("• ⚡ **高效处理**: 不会因为某个设备慢而影响其他设备")
    
    print("\n🌟 优势:")
    print("• 🚀 **高并发**: 多设备同时工作，效率高")
    print("• 🛡️ **容错性**: 单个设备故障不影响其他设备")
    print("• ⏰ **灵活性**: 每个设备可以有不同的执行速度")
    print("• 📊 **可监控**: 可以独立监控每个设备的执行状态")

if __name__ == "__main__":
    main()
