#!/usr/bin/env python3
"""
最终修复测试
验证调度器引用修复和分组延迟功能
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class FixedMockDevice:
    """修复后的模拟设备 - 正确发送完成消息"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            log_with_time(f"📨 设备 {self.device_number} 收到: {data}")
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                await self._handle_task(data)
            elif data.get('type') == 'error':
                log_with_time(f"⚠️ 设备 {self.device_number} 收到错误: {data.get('message')}")
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务 - 正确发送完成消息"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        
        # 模拟任务执行时间（2秒）
        execution_time = 2.0
        await asyncio.sleep(execution_time)
        
        # 🔥 关键修复：发送正确的任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成，发送完成消息: {completion_msg}")

def create_like_task(task_name, group_id, delay_group_sec=5, delay_like_sec=1):
    """创建点赞任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "5139005100786544",
            "blogger_id": "5136084399294965"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_final_fix():
    """测试最终修复效果"""
    log_with_time("=== 最终修复测试 ===")
    
    # 创建模拟设备（使用实际存在的设备号）
    device = FixedMockDevice("ceshi2")  # 使用设备ID 9对应的设备号
    
    try:
        # 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        group_id = 1
        delay_group = 5  # 5秒分组延迟
        delay_like = 1   # 1秒操作延迟
        
        log_with_time(f"📱 使用分组ID: {group_id}")
        log_with_time(f"⏰ 设置: 分组延迟={delay_group}秒, 操作延迟={delay_like}秒")
        
        test_start_time = time.time()
        
        # 创建第一个任务
        log_with_time("🚀 创建第一个任务...")
        task1 = create_like_task("最终修复测试1", group_id, delay_group, delay_like)
        if not task1:
            return
        
        # 等待第一个任务开始执行
        await asyncio.sleep(3)
        
        # 创建第二个任务
        log_with_time("🚀 创建第二个任务...")
        task2 = create_like_task("最终修复测试2", group_id, delay_group, delay_like)
        if not task2:
            return
        
        # 等待足够时间让两个任务都完成
        log_with_time("⏳ 等待任务执行完成...")
        await asyncio.sleep(20)  # 等待20秒
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        
        # 检查数据库中的实际执行时间
        await check_task_execution_times([task1.get('id'), task2.get('id')])
        
    finally:
        await device.disconnect()

async def check_task_execution_times(task_ids):
    """检查任务执行时间"""
    log_with_time("=== 检查任务执行时间 ===")
    
    import sys
    sys.path.append('.')
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    
    db = next(get_db())
    try:
        completion_times = []
        
        for task_id in task_ids:
            task = db.query(Task).filter(Task.id == task_id).first()
            task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
            
            if task and task_queue:
                log_with_time(f"任务{task_id}:")
                log_with_time(f"  创建时间: {task.create_time}")
                if task_queue.dispatch_time:
                    log_with_time(f"  分发时间: {task_queue.dispatch_time}")
                if task_queue.finish_time:
                    log_with_time(f"  完成时间: {task_queue.finish_time}")
                    completion_times.append((task_id, task_queue.finish_time))
                log_with_time(f"  分组延迟设置: {task.delay_group}ms")
                log_with_time(f"  状态: {task_queue.status}")
        
        # 计算任务间隔
        if len(completion_times) >= 2:
            completion_times.sort(key=lambda x: x[1])  # 按完成时间排序
            
            for i in range(len(completion_times) - 1):
                task1_id, time1 = completion_times[i]
                task2_id, time2 = completion_times[i + 1]
                interval = (time2 - time1).total_seconds()
                
                log_with_time(f"📊 任务间隔分析:")
                log_with_time(f"任务{task1_id}完成 → 任务{task2_id}完成: {interval:.1f}秒")
                
                expected_min = 4.0  # 至少5秒分组延迟，允许一些误差
                expected_max = 8.0  # 最多8秒（包含执行时间）
                
                if expected_min <= interval <= expected_max:
                    log_with_time("✅ 分组延迟工作正常！")
                    log_with_time("✅ 调度器引用问题已修复！")
                    log_with_time("✅ 设备完成消息发送正常！")
                else:
                    log_with_time(f"⚠️ 分组延迟可能异常 (预期{expected_min}-{expected_max}秒)")
        
    finally:
        db.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("🔧 最终修复测试")
    print("💡 验证调度器引用修复和分组延迟功能")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 测试最终修复效果
    await test_final_fix()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 修复要点:")
    print("   1. 修复了WebSocket中的调度器引用")
    print("   2. 移除了旧调度器的所有引用")
    print("   3. 设备正确发送task_completion消息")
    print("   4. 分组延迟在WebSocket层正确处理")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
