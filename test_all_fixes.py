#!/usr/bin/env python3
"""
测试所有修复的功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from widgets.task_manager import CreateTaskDialog
from widgets.group_manager import GroupManagerFrame
from utils.api_client import APIClient
from config import Config

class TestAllFixes:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("功能修复测试")
        self.root.geometry("600x500")
        
        # 创建API客户端
        self.api_client = APIClient(Config.API_BASE_URL)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建测试界面"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="功能修复测试", font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # 连接状态
        self.status_label = ttk.Label(main_frame, text="检查API连接中...")
        self.status_label.pack(pady=5)
        
        # 测试按钮组
        test_frame = ttk.LabelFrame(main_frame, text="测试功能")
        test_frame.pack(fill=tk.X, pady=10)
        
        # 任务创建测试
        task_frame = ttk.Frame(test_frame)
        task_frame.pack(fill=tk.X, pady=5, padx=10)
        ttk.Label(task_frame, text="任务创建测试:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        task_btn_frame = ttk.Frame(task_frame)
        task_btn_frame.pack(fill=tk.X, pady=2)
        ttk.Button(task_btn_frame, text="测试创建任务对话框", command=self.test_create_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(task_btn_frame, text="测试参数输入框", command=self.test_parameter_inputs).pack(side=tk.LEFT, padx=2)
        
        # 分组管理测试
        group_frame = ttk.Frame(test_frame)
        group_frame.pack(fill=tk.X, pady=5, padx=10)
        ttk.Label(group_frame, text="分组管理测试:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        group_btn_frame = ttk.Frame(group_frame)
        group_btn_frame.pack(fill=tk.X, pady=2)
        ttk.Button(group_btn_frame, text="测试分组管理器", command=self.test_group_manager).pack(side=tk.LEFT, padx=2)
        ttk.Button(group_btn_frame, text="测试创建分组", command=self.test_create_group).pack(side=tk.LEFT, padx=2)
        
        # API测试
        api_frame = ttk.Frame(test_frame)
        api_frame.pack(fill=tk.X, pady=5, padx=10)
        ttk.Label(api_frame, text="API功能测试:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        
        api_btn_frame = ttk.Frame(api_frame)
        api_btn_frame.pack(fill=tk.X, pady=2)
        ttk.Button(api_btn_frame, text="测试API连接", command=self.test_api_connection).pack(side=tk.LEFT, padx=2)
        ttk.Button(api_btn_frame, text="测试inex任务", command=self.test_inex_task).pack(side=tk.LEFT, padx=2)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="测试结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.result_text = tk.Text(result_frame, height=15, width=70)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 初始检查连接
        self.root.after(1000, self.test_api_connection)
        
    def log(self, message):
        """记录日志"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
        
    def test_api_connection(self):
        """测试API连接"""
        self.log("🔗 测试API连接...")
        
        connected = self.api_client.check_connection()
        if connected:
            self.status_label.config(text="✅ API连接正常", foreground="green")
            self.log("✅ API连接成功")
            
            # 测试基本数据获取
            devices = self.api_client.get_devices()
            groups = self.api_client.get_groups()
            tasks = self.api_client.get_tasks()
            
            self.log(f"   设备数量: {len(devices) if devices else 0}")
            self.log(f"   分组数量: {len(groups) if groups else 0}")
            self.log(f"   任务数量: {len(tasks) if tasks else 0}")
        else:
            self.status_label.config(text="❌ API连接失败", foreground="red")
            self.log("❌ API连接失败，请检查后端服务器")
            
    def test_create_task(self):
        """测试创建任务对话框"""
        self.log("📋 测试创建任务对话框...")
        
        try:
            def refresh_callback():
                self.log("   任务创建回调被调用")
                
            dialog = CreateTaskDialog(self.root, self.api_client, refresh_callback)
            self.log("✅ 创建任务对话框打开成功")
            self.log("   请在对话框中测试:")
            self.log("   1. 选择任务类型，检查参数输入框是否显示")
            self.log("   2. 选择目标范围，检查设备/分组选择框")
            self.log("   3. 检查延迟设置是否为秒单位")
            
        except Exception as e:
            self.log(f"❌ 创建对话框失败: {e}")
            
    def test_parameter_inputs(self):
        """测试参数输入框显示"""
        self.log("🔧 测试参数输入框...")
        self.log("   修复内容:")
        self.log("   ✅ 任务类型匹配修复: 使用中文名称匹配")
        self.log("   ✅ 延迟单位修复: 改为秒单位，自动转换为毫秒")
        self.log("   ✅ 参数输入框: 根据任务类型动态显示")
        self.log("   ✅ 目标选择: 设备和分组选择下拉框")
        
    def test_group_manager(self):
        """测试分组管理器"""
        self.log("👥 测试分组管理器...")
        
        try:
            # 创建分组管理器窗口
            group_window = tk.Toplevel(self.root)
            group_window.title("分组管理器测试")
            group_window.geometry("800x600")
            
            group_manager = GroupManagerFrame(group_window, self.api_client)
            group_manager.pack(fill=tk.BOTH, expand=True)
            
            self.log("✅ 分组管理器创建成功")
            self.log("   请测试:")
            self.log("   1. 分组名称是否正确显示")
            self.log("   2. 设备数量是否正确显示")
            self.log("   3. 创建分组功能")
            self.log("   4. 添加设备到分组功能")
            self.log("   5. 删除分组功能")
            
        except Exception as e:
            self.log(f"❌ 分组管理器创建失败: {e}")
            
    def test_create_group(self):
        """测试创建分组"""
        self.log("➕ 测试创建分组...")
        
        try:
            group_data = {
                "group_name": "测试分组_修复",
                "description": "测试分组功能修复"
            }
            
            result = self.api_client.create_group(group_data)
            if result:
                self.log("✅ 分组创建成功:")
                self.log(f"   分组ID: {result.get('id')}")
                self.log(f"   分组名称: {result.get('group_name')}")
                self.log(f"   描述: {result.get('description')}")
            else:
                self.log("❌ 分组创建失败")
                
        except Exception as e:
            self.log(f"❌ 分组创建异常: {e}")
            
    def test_inex_task(self):
        """测试inex任务创建"""
        self.log("🎯 测试inex任务创建...")
        
        try:
            task_data = {
                "task_type": "inex",
                "parameters": {
                    "user_id": "test_user_fix",
                    "count": 1
                },
                "target_scope": "single",
                "target_id": 1,
                "delay_group": 2000,  # 2秒转换为毫秒
                "delay_like": 1000    # 1秒转换为毫秒
            }
            
            result = self.api_client.create_task(task_data)
            if result:
                self.log("✅ inex任务创建成功:")
                self.log(f"   任务ID: {result.get('id')}")
                self.log(f"   任务类型: {result.get('task_type')}")
                self.log(f"   参数: {result.get('parameters')}")
                self.log(f"   延迟设置: 分组{task_data['delay_group']}ms, 点赞{task_data['delay_like']}ms")
            else:
                self.log("❌ inex任务创建失败")
                
        except Exception as e:
            self.log(f"❌ inex任务创建异常: {e}")
            
    def run(self):
        """运行测试"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动功能修复测试...")
    
    try:
        app = TestAllFixes()
        app.run()
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
