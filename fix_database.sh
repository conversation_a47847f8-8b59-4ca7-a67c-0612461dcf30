#!/bin/bash

# 数据库修复脚本

echo "=== 数据库修复脚本 ==="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 1. 检查容器状态
log_info "检查MySQL容器状态..."
if ! docker ps | grep -q wb_mysql; then
    log_error "MySQL容器未运行，尝试启动..."
    if [ -f "docker-compose-db.yml" ]; then
        docker-compose -f docker-compose-db.yml up -d
        sleep 10
    else
        log_error "未找到docker-compose-db.yml文件"
        exit 1
    fi
fi

# 2. 等待MySQL就绪
log_info "等待MySQL就绪..."
for i in {1..30}; do
    if docker exec wb_mysql mysql -u root -p123456 -e "SELECT 1;" >/dev/null 2>&1; then
        log_info "✅ MySQL连接成功"
        break
    fi
    echo "等待MySQL就绪... ($i/30)"
    sleep 2
    if [ $i -eq 30 ]; then
        log_error "MySQL连接超时"
        exit 1
    fi
done

# 3. 检查数据库是否存在
log_info "检查数据库wb是否存在..."
if docker exec wb_mysql mysql -u root -p123456 -e "USE wb;" >/dev/null 2>&1; then
    log_info "✅ 数据库wb已存在"
else
    log_warn "数据库wb不存在，正在创建..."
    
    # 创建数据库
    docker exec wb_mysql mysql -u root -p123456 -e "
    CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    "
    
    if [ $? -eq 0 ]; then
        log_info "✅ 数据库wb创建成功"
    else
        log_error "❌ 数据库wb创建失败"
        exit 1
    fi
fi

# 4. 显示数据库列表
log_info "当前数据库列表:"
docker exec wb_mysql mysql -u root -p123456 -e "SHOW DATABASES;"

# 5. 测试应用连接
log_info "测试应用数据库连接..."
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    
    # 创建测试脚本
    cat > test_db_connection.py << 'EOF'
import sys
sys.path.append('.')

try:
    from app.db import engine
    from sqlalchemy import text
    
    # 测试连接
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("✅ 应用数据库连接成功")
        
    # 创建表结构
    from app.db import Base
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表结构创建成功")
    
except Exception as e:
    print(f"❌ 应用数据库连接失败: {e}")
    sys.exit(1)
EOF
    
    python test_db_connection.py
    rm test_db_connection.py
    
    if [ $? -eq 0 ]; then
        log_info "✅ 应用数据库连接测试通过"
    else
        log_error "❌ 应用数据库连接测试失败"
    fi
else
    log_warn "虚拟环境不存在，跳过应用连接测试"
fi

echo ""
echo "🎉 数据库修复完成！"
echo ""
echo "📊 数据库信息:"
echo "   容器名: wb_mysql"
echo "   主机: localhost:3306"
echo "   用户: root"
echo "   密码: 123456"
echo "   数据库: wb"
echo ""
echo "🔧 管理命令:"
echo "   连接数据库: docker exec -it wb_mysql mysql -u root -p123456 wb"
echo "   查看日志: docker logs wb_mysql"
echo "   重启容器: docker restart wb_mysql"
echo ""
echo "🚀 现在可以启动后端服务:"
echo "   ./start_backend.sh"
echo ""
