from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from datetime import datetime

class TaskBase(BaseModel):
    task_type: str
    parameters: Dict
    target_scope: str
    target_id: Optional[int] = None
    delay_group: Optional[int] = 0
    delay_like: Optional[int] = 0

class TaskCreate(TaskBase):
    pass

class TaskOut(TaskBase):
    id: int
    status: str
    create_time: Optional[datetime] = None

    class Config:
        from_attributes = True  # 替换原来的orm_mode

class TaskCompletionResponse(BaseModel):
    """任务完成响应模型"""
    success: bool
    task_id: int
    device_id: int
    completion_time: datetime
    result: Optional[Dict] = None
    error: Optional[str] = None
    metrics: Optional[Dict] = None

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "success": True,
                "task_id": 1,
                "device_id": 1,
                "completion_time": "2025-06-04T00:00:00Z",
                "result": {"output": "success"},
                "error": None,
                "metrics": {"duration": 2.5}
            }
        }

class WSTaskCompletionMessage(BaseModel):
    """WebSocket任务完成消息格式"""
    msg_type: str = "task_completion"
    task_id: int
    device_id: int
    status: str  # success/failed/running
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    progress: Optional[float] = None
    result: Optional[Dict] = None
    error: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "msg_type": "task_completion",
                "task_id": 123,
                "device_id": 456,
                "status": "success",
                "timestamp": "2025-06-04T12:00:00Z",
                "progress": 100.0,
                "result": {
                    "output": "点赞完成",
                    "processed": 50,
                    "success_rate": 100
                },
                "error": None
            }
        }
