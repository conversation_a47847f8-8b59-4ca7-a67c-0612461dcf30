#!/usr/bin/env python3
"""
设备连接测试
模拟设备连接并测试任务分发
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class TestDevice:
    """测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            log_with_time(f"📨 设备 {self.device_number} 收到: {data}")
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                self.received_tasks.append(data)
                await self._handle_task(data)
            elif data.get('type') == 'error':
                log_with_time(f"⚠️ 设备 {self.device_number} 收到错误: {data.get('message')}")
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        
        # 模拟任务执行时间（2秒）
        execution_time = 2.0
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成，发送完成消息")

def create_test_task(task_name, group_id, delay_group_sec=5):
    """创建测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "test123"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),
        "delay_like": 1000
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_device_connection_and_task():
    """测试设备连接和任务分发"""
    log_with_time("=== 设备连接和任务分发测试 ===")
    
    # 创建测试设备（使用现有的设备号）
    device = TestDevice("ceshi212")  # 使用在线的设备号
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 2. 创建测试任务（分组5，设备12在分组5中）
        log_with_time("🚀 创建测试任务...")
        task = create_test_task("设备连接测试", 5, 3)  # 分组5，3秒延迟
        
        if not task:
            log_with_time("❌ 任务创建失败")
            return
        
        # 3. 等待任务分发
        log_with_time("⏳ 等待任务分发...")
        await asyncio.sleep(10)
        
        # 4. 检查结果
        if device.received_tasks:
            log_with_time(f"✅ 设备收到 {len(device.received_tasks)} 个任务")
            for i, task_data in enumerate(device.received_tasks):
                log_with_time(f"   任务{i+1}: ID={task_data.get('task_id')}, 类型={task_data.get('type')}")
        else:
            log_with_time("❌ 设备没有收到任何任务")
        
        # 5. 再等待一段时间观察分组延迟
        log_with_time("⏳ 等待观察分组延迟...")
        await asyncio.sleep(10)
        
    finally:
        await device.disconnect()

async def check_system_after_connection():
    """检查连接后的系统状态"""
    log_with_time("=== 检查连接后系统状态 ===")
    
    try:
        # 检查WebSocket连接
        from app.websocket.ws_manager import manager as ws_manager
        log_with_time(f"WebSocket活跃连接: {len(ws_manager.active_connections)}")
        
        # 检查调度器状态
        from app.services.optimized_scheduler import optimized_scheduler
        log_with_time(f"调度器运行状态: {optimized_scheduler.is_running}")
        log_with_time(f"设备在线缓存: {len(optimized_scheduler.device_online_cache)}")
        
    except Exception as e:
        log_with_time(f"检查系统状态失败: {e}")

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 设备连接和任务分发测试")
    print("💡 验证设备连接后能否正常收到任务")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 1. 测试设备连接和任务分发
    await test_device_connection_and_task()
    
    print("\n" + "-" * 40)
    
    # 2. 检查连接后系统状态
    await check_system_after_connection()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果设备没有收到任务，可能的原因:")
    print("   1. 调度器没有正确启动")
    print("   2. 设备分组配置错误")
    print("   3. 任务分发逻辑有问题")
    print("   4. WebSocket连接状态同步问题")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
