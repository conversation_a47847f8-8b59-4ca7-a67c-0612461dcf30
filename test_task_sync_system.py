#!/usr/bin/env python3
"""
测试任务同步系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime

def test_task_sync_apis():
    """测试任务同步API"""
    print("🔄 测试任务同步API...")
    
    base_url = "http://localhost:8001"
    
    try:
        # 1. 测试获取仪表板统计
        print("\n1. 测试仪表板统计...")
        response = requests.get(f"{base_url}/task-sync/dashboard-stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 仪表板统计获取成功:")
            print(f"   任务统计: {stats.get('tasks')}")
            print(f"   设备统计: {stats.get('devices')}")
            print(f"   分组数量: {stats.get('groups')}")
            print(f"   执行中任务: {stats.get('running_tasks')}")
        else:
            print(f"❌ 仪表板统计获取失败: {response.status_code}")
            
        # 2. 测试同步所有设备当前任务
        print("\n2. 测试同步所有设备当前任务...")
        response = requests.post(f"{base_url}/task-sync/sync-all-devices", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("✅ 设备任务同步成功:")
            print(f"   结果: {result.get('result')}")
        else:
            print(f"❌ 设备任务同步失败: {response.status_code}")
            
        # 3. 测试同步所有任务状态
        print("\n3. 测试同步所有任务状态...")
        response = requests.post(f"{base_url}/task-sync/sync-all-tasks", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("✅ 任务状态同步成功:")
            print(f"   结果: {result.get('result')}")
        else:
            print(f"❌ 任务状态同步失败: {response.status_code}")
            
        # 4. 测试获取设备任务队列
        print("\n4. 测试获取设备任务队列...")
        # 先获取设备列表
        devices_response = requests.get(f"{base_url}/devices/", timeout=10)
        if devices_response.status_code == 200:
            devices = devices_response.json()
            if devices:
                device_id = devices[0].get('id')
                response = requests.get(f"{base_url}/task-sync/device-queue/{device_id}", timeout=10)
                if response.status_code == 200:
                    queue_data = response.json()
                    print(f"✅ 设备 {device_id} 任务队列获取成功:")
                    print(f"   队列长度: {queue_data.get('count')}")
                    
                    queue = queue_data.get('queue', [])
                    for i, item in enumerate(queue[:3]):  # 只显示前3个
                        print(f"   任务{i+1}: {item.get('task_type')} - {item.get('status')}")
                else:
                    print(f"❌ 设备任务队列获取失败: {response.status_code}")
            else:
                print("❌ 没有设备数据")
        else:
            print(f"❌ 获取设备列表失败: {devices_response.status_code}")
            
        # 5. 测试获取分组任务摘要
        print("\n5. 测试获取分组任务摘要...")
        groups_response = requests.get(f"{base_url}/groups/", timeout=10)
        if groups_response.status_code == 200:
            groups = groups_response.json()
            if groups:
                group_id = groups[0].get('id')
                response = requests.get(f"{base_url}/task-sync/group-summary/{group_id}", timeout=10)
                if response.status_code == 200:
                    summary = response.json()
                    print(f"✅ 分组 {group_id} 任务摘要获取成功:")
                    print(f"   分组名称: {summary.get('group_name')}")
                    print(f"   设备数量: {summary.get('device_count')}")
                    print(f"   任务摘要: {summary.get('task_summary')}")
                else:
                    print(f"❌ 分组任务摘要获取失败: {response.status_code}")
            else:
                print("❌ 没有分组数据")
        else:
            print(f"❌ 获取分组列表失败: {groups_response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ 任务同步API测试异常: {e}")
        return False

def test_device_schema_update():
    """测试设备Schema更新"""
    print("\n📱 测试设备Schema更新...")
    
    try:
        response = requests.get("http://localhost:8000/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            if devices:
                device = devices[0]
                print("✅ 设备数据获取成功:")
                print(f"   设备编号: {device.get('device_number')}")
                print(f"   分组ID: {device.get('group_id')}")
                print(f"   当前任务ID: {device.get('current_task_id')}")
                print(f"   当前任务类型: {device.get('current_task_type')}")
                
                # 检查是否包含新字段
                has_group_id = 'group_id' in device
                has_current_task_id = 'current_task_id' in device
                has_current_task_type = 'current_task_type' in device
                
                if has_group_id and has_current_task_id and has_current_task_type:
                    print("✅ 设备Schema包含所有新字段")
                    return True
                else:
                    print("❌ 设备Schema缺少新字段:")
                    if not has_group_id:
                        print("   - 缺少 group_id")
                    if not has_current_task_id:
                        print("   - 缺少 current_task_id")
                    if not has_current_task_type:
                        print("   - 缺少 current_task_type")
                    return False
            else:
                print("❌ 没有设备数据")
                return False
        else:
            print(f"❌ 获取设备数据失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 设备Schema测试异常: {e}")
        return False

def test_frontend_integration():
    """测试前端集成"""
    print("\n🖥️ 测试前端集成...")
    
    try:
        # 测试前端API客户端
        sys.path.append('frontend')
        from utils.api_client import APIClient
        from config import Config
        
        api_client = APIClient(Config.API_BASE_URL)
        
        # 测试连接
        if not api_client.check_connection():
            print("❌ 前端API连接失败")
            return False
            
        print("✅ 前端API连接成功")
        
        # 测试获取设备（包含新字段）
        devices = api_client.get_devices()
        if devices:
            device = devices[0]
            print(f"✅ 前端获取设备成功，包含字段:")
            for key in ['device_number', 'group_id', 'current_task_id', 'current_task_type']:
                if key in device:
                    print(f"   ✅ {key}: {device.get(key)}")
                else:
                    print(f"   ❌ 缺少 {key}")
                    
        return True
        
    except Exception as e:
        print(f"❌ 前端集成测试异常: {e}")
        return False

def create_test_scenario():
    """创建测试场景"""
    print("\n🎭 创建测试场景...")
    
    try:
        # 1. 创建一个测试任务
        task_data = {
            "task_type": "inex",
            "parameters": {
                "user_id": "test_sync_user",
                "count": 1
            },
            "target_scope": "single",
            "target_id": 1,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        response = requests.post("http://localhost:8000/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            print(f"✅ 测试任务创建成功 (ID: {task_id})")
            
            # 2. 同步任务状态
            sync_response = requests.post(f"http://localhost:8000/task-sync/sync-task/{task_id}", timeout=10)
            if sync_response.status_code == 200:
                print("✅ 任务状态同步成功")
            else:
                print(f"❌ 任务状态同步失败: {sync_response.status_code}")
                
            return task_id
        else:
            print(f"❌ 测试任务创建失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试场景异常: {e}")
        return None

def main():
    """主函数"""
    print("🧪 测试任务同步系统...")
    print("="*60)
    
    tests = [
        ("设备Schema更新", test_device_schema_update),
        ("任务同步API", test_task_sync_apis),
        ("前端集成", test_frontend_integration)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
            
    # 创建测试场景
    test_task_id = create_test_scenario()
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 任务同步系统测试通过！")
        print("\n✅ 新功能验证:")
        print("1. 设备当前任务显示 - 正常")
        print("2. 任务状态同步 - 正常")
        print("3. 设备任务队列 - 正常")
        print("4. 分组任务摘要 - 正常")
        print("5. 前端日志功能 - 已添加")
        
        print("\n🚀 系统架构优化:")
        print("- tasks表显示任务概览")
        print("- task_queue表控制实际执行")
        print("- 设备显示当前执行任务")
        print("- 实时状态同步机制")
        print("- 完整的日志记录")
        
        if test_task_id:
            print(f"\n💡 测试任务ID: {test_task_id}")
            print("可以在前端查看任务执行状态")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        
    print("\n📋 使用说明:")
    print("1. 重启后端服务器以加载新的API和Schema")
    print("2. 前端设备管理页面现在显示当前任务")
    print("3. 可以查看设备任务队列和同步状态")
    print("4. 日志框记录所有操作")

if __name__ == "__main__":
    main()
