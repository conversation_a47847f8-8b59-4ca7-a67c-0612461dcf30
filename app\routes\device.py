from fastapi import APIRouter, Depends, HTTPException, WebSocket
from sqlalchemy.orm import Session
from typing import List

from starlette.websockets import WebSocketDisconnect

from app.db import get_db
from app.schemas.device import DeviceCreate, DeviceRead, DeviceUpdate
from app.services.device import get_all_devices, create_device, get_device_by_number, update_device
from app.websocket.ws_manager import manager
from app.models import Device
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/devices", tags=["Device"])

@router.get("/", response_model=List[DeviceRead])
def list_devices(db: Session = Depends(get_db)):
    logger.info("Accessing /devices/ endpoint")
    devices = get_all_devices(db)
    logger.info(f"Found {len(devices)} devices")
    return devices

class DeviceResponse(DeviceRead):
    status: str
    message: str

@router.post("/", response_model=DeviceResponse)
def add_device(device: DeviceCreate, db: Session = Depends(get_db)):
    result = create_device(db, device)
    return result

@router.get("/{device_id}", response_model=DeviceRead)
def get_device(device_id: int, db: Session = Depends(get_db)):
    # 先尝试按ID查找
    device = db.query(Device).filter(Device.id == device_id).first()
    if not device:
        raise HTTPException(status_code=404, detail="Device not found")
    return device

@router.websocket("/ws/{device_number}")
async def device_ws(websocket: WebSocket, device_number: str, db: Session = Depends(get_db)):
    await websocket.accept()
    await manager.connect(device_number, websocket)

    device = get_device_by_number(db, device_number)
    if device:
        update_device(db, device.id, DeviceUpdate(online_status="online"))

    try:
        while True:
            data = await websocket.receive_json()
            print(f"[WS][{device_number}] received: {data}")

    except WebSocketDisconnect:
        manager.disconnect(device_number)
        if device:
            update_device(db, device.id, DeviceUpdate(online_status="offline"))
