#!/usr/bin/env python3
"""
诊断任务发送问题
检查任务是否真正到达客户端
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TaskDeliveryDiagnoser:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        self.received_messages = []
        self.task_messages = []
        self.heartbeat_messages = []
        self.other_messages = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} 连接成功")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听所有服务器消息"""
        try:
            while self.websocket and not self.websocket.closed:
                message = await self.websocket.recv()
                timestamp = datetime.now().isoformat()
                
                # 记录原始消息
                message_record = {
                    'timestamp': timestamp,
                    'raw_message': message,
                    'message_type': 'unknown'
                }
                
                try:
                    # 尝试解析JSON
                    data = json.loads(message)
                    message_record['parsed_data'] = data
                    message_record['message_type'] = 'json'
                    
                    # 分类消息
                    if self._is_task_message(data):
                        message_record['category'] = 'task'
                        self.task_messages.append(message_record)
                        print(f"🎯 收到任务消息: {data}")
                        
                        # 自动回复任务完成（用于测试）
                        await self._auto_complete_task(data)
                        
                    elif self._is_heartbeat_related(data):
                        message_record['category'] = 'heartbeat'
                        self.heartbeat_messages.append(message_record)
                        print(f"💓 收到心跳相关消息: {data}")
                        
                    else:
                        message_record['category'] = 'other'
                        self.other_messages.append(message_record)
                        print(f"📨 收到其他消息: {data}")
                        
                except json.JSONDecodeError:
                    # 非JSON消息
                    message_record['message_type'] = 'text'
                    message_record['category'] = 'text'
                    self.other_messages.append(message_record)
                    print(f"📝 收到文本消息: {message}")
                
                self.received_messages.append(message_record)
                
        except Exception as e:
            print(f"📨 消息监听异常: {e}")
            
    def _is_task_message(self, data):
        """检查是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            'parameters' in data
        )
        
    def _is_heartbeat_related(self, data):
        """检查是否是心跳相关消息"""
        if not isinstance(data, dict):
            return False
        msg_type = data.get('type', '')
        return msg_type in ['heartbeat_ack', 'heartbeat']
        
    async def _auto_complete_task(self, task_data):
        """自动完成任务（用于测试）"""
        try:
            task_id = task_data['task_id']
            
            # 模拟任务执行时间
            await asyncio.sleep(2)
            
            # 发送任务完成消息
            completion = {
                'type': 'task_completion',
                'task_id': task_id,
                'status': 'success',
                'timestamp': datetime.utcnow().isoformat(),
                'result': {
                    'message': '自动测试完成',
                    'task_type': task_data.get('type'),
                    'parameters': task_data.get('parameters')
                }
            }
            
            await self.websocket.send(json.dumps(completion))
            print(f"📤 自动发送任务完成: {completion}")
            
        except Exception as e:
            print(f"❌ 自动完成任务失败: {e}")
            
    async def send_heartbeat(self):
        """发送心跳消息"""
        heartbeat = {
            'type': 'heartbeat',
            'timestamp': datetime.utcnow().isoformat(),
            'device_number': self.device_number
        }
        
        try:
            await self.websocket.send(json.dumps(heartbeat))
            print(f"💓 发送心跳: {heartbeat}")
        except Exception as e:
            print(f"❌ 心跳发送失败: {e}")
            
    def get_statistics(self):
        """获取统计信息"""
        return {
            'total_messages': len(self.received_messages),
            'task_messages': len(self.task_messages),
            'heartbeat_messages': len(self.heartbeat_messages),
            'other_messages': len(self.other_messages),
            'json_messages': len([m for m in self.received_messages if m['message_type'] == 'json']),
            'text_messages': len([m for m in self.received_messages if m['message_type'] == 'text'])
        }
        
    def print_detailed_report(self):
        """打印详细报告"""
        stats = self.get_statistics()
        
        print(f"\n📊 消息接收统计报告")
        print(f"{'='*50}")
        print(f"总消息数: {stats['total_messages']}")
        print(f"任务消息: {stats['task_messages']}")
        print(f"心跳消息: {stats['heartbeat_messages']}")
        print(f"其他消息: {stats['other_messages']}")
        print(f"JSON消息: {stats['json_messages']}")
        print(f"文本消息: {stats['text_messages']}")
        
        if self.task_messages:
            print(f"\n🎯 任务消息详情:")
            for i, task_msg in enumerate(self.task_messages, 1):
                data = task_msg['parsed_data']
                print(f"  任务 {i}: ID={data.get('task_id')}, 类型={data.get('type')}, 参数={data.get('parameters')}")
                print(f"    时间: {task_msg['timestamp']}")
        else:
            print(f"\n❌ 没有收到任何任务消息！")
            
        if self.other_messages:
            print(f"\n📨 其他消息详情:")
            for i, msg in enumerate(self.other_messages[-5:], 1):  # 只显示最后5条
                if msg['message_type'] == 'json':
                    print(f"  消息 {i}: {msg['parsed_data']}")
                else:
                    print(f"  消息 {i}: {msg['raw_message'][:100]}...")

async def diagnose_task_delivery():
    """诊断任务发送问题"""
    print("🔍 开始诊断任务发送问题...")
    
    # 使用真实的设备号
    diagnoser = TaskDeliveryDiagnoser("devi201")
    
    try:
        # 连接到服务器
        if not await diagnoser.connect():
            return False
            
        print("✅ 连接成功，开始监听消息...")
        
        # 发送一个心跳确保连接正常
        await diagnoser.send_heartbeat()
        
        # 监听60秒
        print("⏳ 监听60秒，等待任务消息...")
        print("💡 提示：现在可以通过API创建任务来测试")
        print("   例如：POST /tasks/ 创建一个任务")
        
        start_time = time.time()
        while time.time() - start_time < 60:
            await asyncio.sleep(5)
            
            # 每5秒发送一次心跳
            await diagnoser.send_heartbeat()
            
            # 显示当前统计
            stats = diagnoser.get_statistics()
            print(f"📊 当前统计: 总消息={stats['total_messages']}, 任务={stats['task_messages']}, 心跳={stats['heartbeat_messages']}")
        
        # 打印详细报告
        diagnoser.print_detailed_report()
        
        return stats['task_messages'] > 0
        
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        return False
    finally:
        await diagnoser.disconnect()

async def test_manual_task_send():
    """手动测试任务发送"""
    print("\n🧪 手动测试任务发送...")
    
    diagnoser = TaskDeliveryDiagnoser("test_manual_device")
    
    try:
        if not await diagnoser.connect():
            return False
            
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 模拟服务端发送任务消息
        print("📤 模拟发送任务消息...")
        
        mock_task = {
            'task_id': 9999,
            'type': 'test_task',
            'parameters': {
                'count': 1,
                'test': True
            }
        }
        
        # 直接通过WebSocket发送（模拟服务端行为）
        await diagnoser.websocket.send(json.dumps(mock_task))
        print(f"📤 发送模拟任务: {mock_task}")
        
        # 等待处理
        await asyncio.sleep(5)
        
        # 检查是否收到
        stats = diagnoser.get_statistics()
        diagnoser.print_detailed_report()
        
        return stats['task_messages'] > 0
        
    except Exception as e:
        print(f"❌ 手动测试异常: {e}")
        return False
    finally:
        await diagnoser.disconnect()

async def main():
    """主诊断函数"""
    print("🚀 任务发送诊断开始...\n")
    
    tests = [
        ("实际任务发送诊断", diagnose_task_delivery),
        ("手动任务发送测试", test_manual_task_send)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🔍 {test_name}")
        print('='*60)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} - 收到任务消息")
            else:
                print(f"❌ {test_name} - 未收到任务消息")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待5秒后继续...")
        await asyncio.sleep(5)
    
    print(f"\n📊 诊断结果: {passed}/{total} 测试收到任务")
    
    if passed == 0:
        print("\n🔍 可能的问题:")
        print("1. 任务没有被正确分发到设备")
        print("2. WebSocket连接有问题")
        print("3. 任务发送逻辑有bug")
        print("4. 客户端消息处理有问题")
        
        print("\n🔧 建议检查:")
        print("1. 检查调度器是否正常运行")
        print("2. 检查设备是否在正确的组中")
        print("3. 检查任务创建API是否正常")
        print("4. 检查WebSocket连接状态")
    else:
        print("\n✅ 任务发送机制正常工作")
        
    return passed > 0

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 诊断被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 诊断异常: {e}")
        sys.exit(1)
