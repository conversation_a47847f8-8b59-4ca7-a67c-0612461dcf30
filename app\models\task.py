from sqlalchemy import Column, Integer, String, Enum, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from app.db import Base

class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_type = Column(Enum("sign", "like", "page_sign", "inex", name="task_type_enum"), nullable=False)
    parameters = Column(JSON, nullable=False)
    target_scope = Column(Enum("single", "group", "all", name="target_scope_enum"), nullable=False)
    target_id = Column(Integer, nullable=True)
    delay_group = Column(Integer, default=0)
    delay_like = Column(Integer, default=0)
    status = Column(Enum("pending", "running", "paused", "done", "failed", "cancelled", name="task_status_enum"), default="pending")
    create_time = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(hours=8))

    queues = relationship("TaskQueue", back_populates="task", foreign_keys="[TaskQueue.task_id]")

class TaskQueue(Base):
    __tablename__ = "task_queue"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"))
    device_id = Column(Integer, ForeignKey("devices.id"))
    group_id = Column(Integer, ForeignKey("groups.id"), nullable=True)  # 新增组ID字段
    status = Column(Enum("pending", "running", "done", "failed", name="task_queue_status_enum"), default="pending")
    result_summary = Column(String(255), nullable=True)
    create_time = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(hours=8))
    dispatch_time = Column(DateTime, nullable=True)
    finish_time = Column(DateTime, nullable=True)
    next_task_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)  # 下一任务ID

    device = relationship("Device", back_populates="tasks")
    task = relationship("Task", back_populates="queues", foreign_keys=[task_id])
    next_task = relationship("Task", foreign_keys=[next_task_id])
    group = relationship("Group")  # 新增组关联

class TaskStateLog(Base):
    __tablename__ = "task_state_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"))
    device_id = Column(Integer, ForeignKey("devices.id"))
    from_state = Column(String(50))
    to_state = Column(String(50))
    change_time = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(hours=8))
    
    task = relationship("Task", back_populates="state_logs")
    device = relationship("Device")

# 在Task模型中添加反向关系
Task.state_logs = relationship("TaskStateLog", back_populates="task")


    

