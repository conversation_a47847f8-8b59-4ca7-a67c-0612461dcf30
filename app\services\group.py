from sqlalchemy.orm import Session
from app.models.group import Group, DeviceGroup
from app.schemas.group import GroupCreate, GroupUpdate, GroupDevice

def create_group(db: Session, group_data: GroupCreate):
    db_group = Group(**group_data.model_dump())
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    return db_group

def get_group(db: Session, group_id: int):
    return db.query(Group).filter(Group.id == group_id).first()

def get_all_groups(db: Session):
    return db.query(Group).all()

def update_group(db: Session, group_id: int, group_data: GroupUpdate):
    db_group = db.query(Group).filter(Group.id == group_id).first()
    if db_group:
        for key, value in group_data.model_dump(exclude_unset=True).items():
            setattr(db_group, key, value)
        db.commit()
        db.refresh(db_group)
    return db_group

def delete_group(db: Session, group_id: int):
    from app.models.device import Device

    db_group = db.query(Group).filter(Group.id == group_id).first()
    if db_group:
        # 先将该分组下的所有设备的group_id设置为None，让设备回归未分组状态
        db.query(Device).filter(Device.group_id == group_id).update({"group_id": None})

        # 然后删除分组
        db.delete(db_group)
        db.commit()

        print(f"删除分组 {group_id}，相关设备已回归未分组状态")
    return db_group

def add_device_to_group(db: Session, group_device: GroupDevice):
    from app.models.device import Device

    # 检查设备是否存在
    device = db.query(Device).filter(Device.id == group_device.device_id).first()
    if not device:
        return None

    # 检查设备是否已经在目标分组中
    if device.group_id == group_device.group_id:
        # 设备已经在目标分组中，检查DeviceGroup表
        existing = db.query(DeviceGroup).filter(
            DeviceGroup.device_id == group_device.device_id,
            DeviceGroup.group_id == group_device.group_id
        ).first()

        if existing:
            return existing
        else:
            # 创建DeviceGroup记录
            db_group_device = DeviceGroup(**group_device.model_dump())
            db.add(db_group_device)
            db.commit()
            db.refresh(db_group_device)
            return db_group_device

    # 更新设备的分组ID
    device.group_id = group_device.group_id
    db.commit()
    db.refresh(device)

    # 在DeviceGroup表中创建记录
    existing = db.query(DeviceGroup).filter(
        DeviceGroup.device_id == group_device.device_id,
        DeviceGroup.group_id == group_device.group_id
    ).first()

    if not existing:
        db_group_device = DeviceGroup(**group_device.model_dump())
        db.add(db_group_device)
        db.commit()
        db.refresh(db_group_device)
        return db_group_device

    return existing

def remove_device_from_group(db: Session, group_device: GroupDevice):
    from app.models.device import Device

    # 更新设备的group_id为None
    device = db.query(Device).filter(Device.id == group_device.device_id).first()
    if device and device.group_id == group_device.group_id:
        device.group_id = None
        db.commit()
        db.refresh(device)

    # 同时删除DeviceGroup表中的记录
    db_group_device = db.query(DeviceGroup).filter(
        DeviceGroup.device_id == group_device.device_id,
        DeviceGroup.group_id == group_device.group_id
    ).first()
    if db_group_device:
        db.delete(db_group_device)
        db.commit()
    return db_group_device

def get_group_devices(db: Session, group_id: int):
    return db.query(DeviceGroup).filter(DeviceGroup.group_id == group_id).all()
