#!/usr/bin/env python3
"""
测试设备ID自动填充功能
验证客户端不需要提供device_id，服务端自动填充
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DeviceIdAutoFillTester:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        self.received_tasks = []
        self.sent_completions = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} 连接成功")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.websocket and not self.websocket.closed:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            print(f"📨 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            print(f"📨 收到服务器消息: {data}")
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                self.received_tasks.append(data)
                await self._handle_task_without_device_id(data)
            elif data.get('type') == 'ack':
                print(f"✅ 收到任务确认: {data}")
            elif data.get('type') == 'error':
                print(f"⚠️ 收到错误: {data}")
            else:
                print(f"ℹ️ 其他消息: {data}")
                
        except json.JSONDecodeError:
            print(f"❌ 消息解析失败: {message}")
        except Exception as e:
            print(f"❌ 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """检查是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            'parameters' in data
        )
        
    async def _handle_task_without_device_id(self, task_data):
        """处理任务 - 不提供device_id"""
        task_id = task_data['task_id']
        task_type = task_data['type']
        parameters = task_data['parameters']
        
        print(f"🎯 处理任务: ID={task_id}, 类型={task_type}, 参数={parameters}")
        
        # 模拟任务执行
        await asyncio.sleep(1)
        
        # 发送不包含device_id的完成消息
        completion_message = {
            'type': 'task_completion',
            'task_id': task_id,
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat(),
            'result': {
                'message': f'{task_type}任务完成',
                'processed': parameters.get('count', 1)
            }
        }
        
        try:
            await self.websocket.send(json.dumps(completion_message))
            print(f"📤 发送任务完成消息（无device_id）: {completion_message}")
            self.sent_completions.append(completion_message)
            
        except Exception as e:
            print(f"❌ 发送任务完成消息失败: {e}")
            
    async def send_test_completions(self):
        """发送测试任务完成消息"""
        print("\n📤 发送测试任务完成消息...")
        
        test_completions = [
            {
                'type': 'task_completion',
                'task_id': 999,
                'status': 'success'
            },
            {
                'type': 'task_completion',
                'task_id': 998,
                'status': 'failed',
                'error': '测试失败'
            },
            {
                'type': 'task_completion',
                'task_id': 997,
                'status': 'success',
                'result': {
                    'processed': 5,
                    'success_count': 5
                }
            }
        ]
        
        for completion in test_completions:
            try:
                await self.websocket.send(json.dumps(completion))
                print(f"📤 发送测试完成消息: {completion}")
                self.sent_completions.append(completion)
                
                # 等待服务器响应
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ 发送测试完成消息失败: {e}")
                
    def get_statistics(self):
        """获取统计信息"""
        return {
            'received_tasks': len(self.received_tasks),
            'sent_completions': len(self.sent_completions)
        }

async def test_device_id_auto_fill():
    """测试设备ID自动填充功能"""
    print("🧪 测试设备ID自动填充功能...")
    
    # 使用真实的设备号进行测试
    tester = DeviceIdAutoFillTester("devi201")
    
    try:
        # 连接到服务器
        if not await tester.connect():
            return False
            
        print("✅ 连接成功，开始测试...")
        
        # 发送测试任务完成消息
        await tester.send_test_completions()
        
        # 等待一段时间接收可能的任务
        print("\n⏳ 等待可能的任务分发...")
        await asyncio.sleep(10)
        
        # 显示统计信息
        stats = tester.get_statistics()
        print(f"\n📊 测试统计:")
        print(f"   接收任务数: {stats['received_tasks']}")
        print(f"   发送完成消息数: {stats['sent_completions']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def test_multiple_devices():
    """测试多个设备的ID自动填充"""
    print("\n🧪 测试多个设备ID自动填充...")
    
    devices = ["test_device_1", "test_device_2", "test_device_3"]
    testers = []
    
    try:
        # 连接所有设备
        for device in devices:
            tester = DeviceIdAutoFillTester(device)
            if await tester.connect():
                testers.append(tester)
                print(f"✅ 设备 {device} 连接成功")
            else:
                print(f"❌ 设备 {device} 连接失败")
        
        if not testers:
            print("❌ 没有设备连接成功")
            return False
            
        # 每个设备发送测试完成消息
        for i, tester in enumerate(testers):
            completion = {
                'type': 'task_completion',
                'task_id': 1000 + i,
                'status': 'success',
                'result': {
                    'device': tester.device_number,
                    'test_id': i
                }
            }
            
            await tester.websocket.send(json.dumps(completion))
            print(f"📤 设备 {tester.device_number} 发送完成消息")
            
            await asyncio.sleep(0.5)
        
        # 等待处理
        await asyncio.sleep(3)
        
        print(f"✅ 多设备测试完成，测试了 {len(testers)} 个设备")
        return True
        
    except Exception as e:
        print(f"❌ 多设备测试异常: {e}")
        return False
    finally:
        # 断开所有连接
        for tester in testers:
            await tester.disconnect()

async def test_error_cases():
    """测试错误情况"""
    print("\n🧪 测试错误情况处理...")
    
    tester = DeviceIdAutoFillTester("nonexistent_device")
    
    try:
        if not await tester.connect():
            return False
            
        # 发送任务完成消息给不存在的设备
        error_completion = {
            'type': 'task_completion',
            'task_id': 9999,
            'status': 'success'
        }
        
        await tester.websocket.send(json.dumps(error_completion))
        print(f"📤 发送完成消息给不存在的设备")
        
        # 等待错误响应
        await asyncio.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 错误情况测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def main():
    """主测试函数"""
    print("🚀 设备ID自动填充测试开始...\n")
    
    tests = [
        ("设备ID自动填充测试", test_device_id_auto_fill),
        ("多设备ID自动填充测试", test_multiple_devices),
        ("错误情况处理测试", test_error_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待3秒后继续...")
        await asyncio.sleep(3)
    
    print(f"\n📊 总体测试结果: {passed}/{total} 通过")
    
    if passed > 0:
        print("🎉 设备ID自动填充功能测试完成！")
        print("\n📋 功能特点:")
        print("1. ✅ 客户端不需要提供device_id")
        print("2. ✅ 服务端自动根据设备号查找设备ID")
        print("3. ✅ 简化了客户端实现")
        print("4. ✅ 保持向后兼容")
        
        print("\n🔧 客户端现在只需发送:")
        print('   {"type": "task_completion", "task_id": 123, "status": "success"}')
        
        return True
    else:
        print("❌ 测试失败，请检查实现")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
