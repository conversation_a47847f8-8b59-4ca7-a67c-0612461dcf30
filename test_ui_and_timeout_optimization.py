#!/usr/bin/env python3
"""
测试UI优化和超时机制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import time

def test_modern_ui_components():
    """测试现代化UI组件"""
    print("🎨 测试现代化UI组件...")
    
    try:
        # 检查简化任务创建器文件
        ui_file = "frontend/widgets/simple_task_creator.py"
        if os.path.exists(ui_file):
            with open(ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查现代化UI特性
            modern_features = [
                "configure_styles",
                "Card.TFrame",
                "Modern.TEntry",
                "Primary.TButton",
                "progress_bar",
                "status_label"
            ]
            
            found_features = []
            for feature in modern_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 现代化UI特性: {len(found_features)}/{len(modern_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(modern_features) * 0.8  # 80%通过率
        else:
            print("❌ UI文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ UI组件测试异常: {e}")
        return False

def test_timeout_monitor_service():
    """测试超时监控服务"""
    print("\n⏰ 测试超时监控服务...")
    
    try:
        # 检查超时监控文件
        timeout_file = "app/services/task_timeout_monitor.py"
        if os.path.exists(timeout_file):
            with open(timeout_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查超时监控特性
            timeout_features = [
                "TaskTimeoutMonitor",
                "check_timeout_tasks",
                "handle_timeout_task",
                "start_next_task_if_needed",
                "timeout_minutes=5"
            ]
            
            found_features = []
            for feature in timeout_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 超时监控特性: {len(found_features)}/{len(timeout_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(timeout_features) * 0.8
        else:
            print("❌ 超时监控文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 超时监控测试异常: {e}")
        return False

def test_timeout_api():
    """测试超时统计API"""
    print("\n📊 测试超时统计API...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试超时统计API
        response = requests.get(f"{base_url}/task-sync/timeout-stats", timeout=10)
        if response.status_code == 200:
            data = response.json()
            stats = data.get('data', {})
            
            print("✅ 超时统计API正常")
            print(f"   超时阈值: {stats.get('timeout_threshold_minutes', 0)}分钟")
            print(f"   24小时超时任务: {stats.get('timeout_count_24h', 0)}个")
            print(f"   24小时总任务: {stats.get('total_tasks_24h', 0)}个")
            print(f"   超时率: {stats.get('timeout_rate_24h', 0)}%")
            print(f"   监控状态: {'运行中' if stats.get('is_monitoring') else '未运行'}")
            
            return True
        else:
            print(f"❌ 超时统计API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 超时统计API测试异常: {e}")
        return False

def test_task_device_logs_api():
    """测试任务设备日志API"""
    print("\n📝 测试任务设备日志API...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 先获取任务列表
        response = requests.get(f"{base_url}/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            if tasks:
                task_id = tasks[0].get('id')
                
                # 测试任务设备日志API
                response = requests.get(f"{base_url}/task-sync/task-device-logs/{task_id}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    print("✅ 任务设备日志API正常")
                    print(f"   任务ID: {data.get('task_id')}")
                    print(f"   总设备数: {data.get('total_devices', 0)}")
                    print(f"   已完成设备: {data.get('completed_devices', 0)}")
                    print(f"   超时设备: {data.get('timeout_devices', 0)}")
                    
                    logs = data.get('logs', [])
                    if logs:
                        print(f"   日志示例: {logs[0].get('device_number')} - {logs[0].get('status')}")
                    
                    return True
                else:
                    print(f"❌ 任务设备日志API失败: {response.status_code}")
                    return False
            else:
                print("⚠️ 没有任务数据")
                return True
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务设备日志API测试异常: {e}")
        return False

def test_ui_interaction_logic():
    """测试UI交互逻辑"""
    print("\n🖱️ 测试UI交互逻辑...")
    
    try:
        ui_file = "frontend/widgets/simple_task_creator.py"
        if os.path.exists(ui_file):
            with open(ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查交互逻辑特性
            interaction_features = [
                "validate_inputs",
                "show_progress",
                "hide_progress",
                "update_status",
                "create_tasks_async",
                "show_creation_result",
                "show_error_tooltip"
            ]
            
            found_features = []
            for feature in interaction_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 交互逻辑特性: {len(found_features)}/{len(interaction_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(interaction_features) * 0.8
        else:
            print("❌ UI文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ UI交互逻辑测试异常: {e}")
        return False

def test_backend_integration():
    """测试后端集成"""
    print("\n🔗 测试后端集成...")
    
    try:
        # 检查main.py中的超时监控集成
        main_file = "app/main.py"
        if os.path.exists(main_file):
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            integration_features = [
                "task_timeout_monitor",
                "start_timeout_monitoring",
                "Task timeout monitoring started"
            ]
            
            found_features = []
            for feature in integration_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 后端集成特性: {len(found_features)}/{len(integration_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(integration_features) * 0.8
        else:
            print("❌ main.py文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 后端集成测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 UI优化和超时机制测试")
    print("="*60)
    
    tests = [
        ("现代化UI组件", test_modern_ui_components),
        ("超时监控服务", test_timeout_monitor_service),
        ("超时统计API", test_timeout_api),
        ("任务设备日志API", test_task_device_logs_api),
        ("UI交互逻辑", test_ui_interaction_logic),
        ("后端集成", test_backend_integration)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:  # 80%通过率
        print("🎉 优化基本完成！")
        
        print("\n✅ UI优化成果:")
        print("1. 🎨 现代化设计 - 卡片式布局，现代色彩搭配")
        print("2. 📱 响应式界面 - 网格布局，自适应尺寸")
        print("3. 🔄 进度指示 - 实时进度条和状态反馈")
        print("4. ✨ 交互优化 - 输入验证，错误提示，异步操作")
        print("5. 📊 状态可视化 - 设备在线状态，分组统计信息")
        
        print("\n✅ 超时机制成果:")
        print("1. ⏰ 5分钟超时 - 任务超过5分钟自动标记失败")
        print("2. 🔄 自动切换 - 超时后自动执行下一个任务")
        print("3. 📊 统计监控 - 实时超时率统计和监控")
        print("4. 📝 日志记录 - 详细的超时日志和错误信息")
        print("5. 🚀 后台服务 - 独立的超时监控服务")
        
        print("\n💡 使用说明:")
        print("1. UI优化:")
        print("   • 现代化的任务创建界面")
        print("   • 实时进度反馈和状态指示")
        print("   • 分组卡片式显示，支持在线状态")
        print("   • 异步任务创建，不阻塞界面")
        
        print("\n2. 超时机制:")
        print("   • 任务发送给设备后5分钟内未完成自动失败")
        print("   • 失败任务写入数据库，记录超时原因")
        print("   • 自动启动下一个排队任务")
        print("   • 提供超时统计API和设备日志API")
        
        print("\n🔧 技术特性:")
        print("• 🎨 现代化UI设计语言")
        print("• 🔄 异步任务处理")
        print("• ⏰ 智能超时监控")
        print("• 📊 实时状态反馈")
        print("• 📝 完整日志记录")
        print("• 🚀 高性能后台服务")
        
    else:
        print("⚠️ 部分优化未完成，请检查相关功能")

if __name__ == "__main__":
    main()
