# app/services/device.py
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.models.device import Device
from app.schemas.device import DeviceCreate, DeviceUpdate

def get_all_devices(db: Session):
    return db.query(Device).all()

def get_device_by_number(db: Session, device_number: str):
    return db.query(Device).filter(Device.device_number == device_number).first()

async def update_device_status(db: Session, device_number: str, update_data: dict):
    print(f"处理设备连接: {device_number}")  # 调试日志
    device = db.query(Device).filter(Device.device_number == device_number).first()
    
    if device:
        print(f"更新现有设备: {device_number}")  # 调试日志
        # 更新现有设备
        for key, value in update_data.items():
            setattr(device, key, value)
    else:
        print(f"创建新设备: {device_number}")  # 调试日志
        # 创建新设备记录
        device = Device(
            device_number=device_number,
            online_status=update_data.get('online_status', 'online'),  # 新设备默认在线
            account_status=update_data.get('account_status', 'normal'),
            last_heartbeat=datetime.utcnow(),
            device_ip=update_data.get('device_ip')
        )
        db.add(device)
        print(f"新设备已添加: {device_number}")  # 调试日志
    
    try:
        db.commit()
        db.refresh(device)
        print(f"设备状态更新完成: {device_number}")  # 调试日志
        return device
    except Exception as e:
        db.rollback()
        print(f"设备状态更新失败: {str(e)}")  # 调试日志
        raise HTTPException(status_code=400, detail=str(e))

def get_device(db: Session, device_id: int):
    return db.query(Device).filter(Device.id == device_id).first()

def create_device(db: Session, device_data: DeviceCreate):
    # 检查设备是否已存在
    existing = get_device_by_number(db, device_data.device_number)
    if existing:
        # 设备存在则处理更新
        update_needed = False
        current_time = datetime.utcnow()
        
        # 检查IP是否变化
        if device_data.device_ip and device_data.device_ip != existing.device_ip:
            existing.device_ip = device_data.device_ip
            update_needed = True
            
        # 总是更新时间戳
        existing.last_heartbeat = current_time
        update_needed = True
        
        if update_needed:
            db.commit()
            db.refresh(existing)
                
        db.commit()
        db.refresh(existing)
        from app.schemas.device import DeviceRead
        from app.routes.device import DeviceResponse
        return DeviceResponse(
            status="updated",
            message="Device information updated",
            **DeviceRead.from_orm(existing).dict()
        )
    
    # 设备不存在则创建
    db_device = Device(
        device_number=device_data.device_number,
        device_ip=device_data.device_ip,
        online_status="offline",
        account_status="not_logged_in",
        last_heartbeat=datetime.utcnow()
    )
    db.add(db_device)
    db.commit()
    db.refresh(db_device)
    from app.schemas.device import DeviceRead
    from app.routes.device import DeviceResponse
    return DeviceResponse(
        status="created",
        message="New device created",
        **DeviceRead.from_orm(db_device).dict()
    )

def update_device(db: Session, device_id: int, device_data: DeviceUpdate):
    db_device = db.query(Device).filter(Device.id == device_id).first()
    if not db_device:
        return None
    
    update_data = device_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_device, key, value)
    
    # 每次更新都记录当前时间作为最后活跃时间
    db_device.last_heartbeat = datetime.utcnow()
    
    db.commit()
    db.refresh(db_device)
    return db_device
