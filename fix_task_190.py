#!/usr/bin/env python3
"""
修复任务190 - 手动将其加入设备队列
"""

import sys
import asyncio
sys.path.append('.')

from app.db import get_db
from app.models.task import Task, TaskQueue
from app.models.device import Device

async def fix_task_190():
    """修复任务190"""
    print("=== 修复任务190 ===")
    
    db = next(get_db())
    try:
        # 1. 检查任务190
        task = db.query(Task).filter(Task.id == 190).first()
        if not task:
            print("❌ 任务190不存在")
            return
        
        print(f"✅ 找到任务190: {task.task_type}, 状态: {task.status}")
        
        # 2. 检查TaskQueue记录
        task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == 190).first()
        if not task_queue:
            print("❌ 任务190没有队列记录")
            return
        
        print(f"✅ 找到队列记录: 设备ID={task_queue.device_id}, 状态={task_queue.status}")
        
        # 3. 检查设备9
        device = db.query(Device).filter(Device.id == 9).first()
        if not device:
            print("❌ 设备9不存在")
            return
        
        print(f"✅ 设备9状态: {device.device_number}, 在线={device.online_status}")
        
        # 4. 手动触发调度器处理
        print("🔧 手动触发调度器处理...")
        
        # 导入调度器
        from app.services.optimized_scheduler import optimized_scheduler
        
        # 检查调度器状态
        print(f"📊 调度器运行状态: {optimized_scheduler.is_running}")
        print(f"📊 设备队列数量: {len(optimized_scheduler.device_queues)}")
        
        if 9 in optimized_scheduler.device_queues:
            queue_size = optimized_scheduler.device_queues[9].qsize()
            print(f"📊 设备9队列大小: {queue_size}")
            
            # 手动将任务加入队列
            if queue_size == 0:
                print("🚀 手动将任务190加入设备9队列...")
                await optimized_scheduler.device_queues[9].put(task)
                print("✅ 任务190已加入设备9队列")
                
                # 再次检查队列大小
                new_queue_size = optimized_scheduler.device_queues[9].qsize()
                print(f"📊 设备9队列新大小: {new_queue_size}")
            else:
                print(f"⚠️ 设备9队列不为空，大小: {queue_size}")
        else:
            print("❌ 设备9没有队列")
        
    finally:
        db.close()

async def check_all_pending_tasks():
    """检查所有pending任务的状态"""
    print("\n=== 检查所有pending任务 ===")
    
    db = next(get_db())
    try:
        # 查找所有pending的TaskQueue记录
        pending_queues = db.query(TaskQueue).filter(TaskQueue.status == 'pending').all()
        
        print(f"📊 总共有 {len(pending_queues)} 个pending任务队列")
        
        # 按设备分组统计
        device_stats = {}
        for tq in pending_queues:
            device_id = tq.device_id
            if device_id not in device_stats:
                device_stats[device_id] = []
            device_stats[device_id].append(tq.task_id)
        
        print("📊 按设备分组的pending任务:")
        for device_id, task_ids in device_stats.items():
            device = db.query(Device).filter(Device.id == device_id).first()
            device_name = device.device_number if device else f"设备{device_id}"
            online_status = device.online_status if device else "unknown"
            print(f"   设备{device_id}({device_name}, {online_status}): {len(task_ids)}个任务 {task_ids[:5]}{'...' if len(task_ids) > 5 else ''}")
        
    finally:
        db.close()

async def trigger_scheduler_redistribution():
    """触发调度器重新分发"""
    print("\n=== 触发调度器重新分发 ===")
    
    try:
        from app.services.optimized_scheduler import optimized_scheduler
        
        print("🔄 手动触发调度器重新分发...")
        await optimized_scheduler._redistribute_pending_tasks_optimized()
        print("✅ 重新分发完成")
        
    except Exception as e:
        print(f"❌ 触发重新分发失败: {e}")

async def main():
    """主函数"""
    print("🔧 任务190修复工具")
    print("=" * 50)
    
    # 1. 修复任务190
    await fix_task_190()
    
    # 2. 检查所有pending任务
    await check_all_pending_tasks()
    
    # 3. 触发调度器重新分发
    await trigger_scheduler_redistribution()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成")
    print("💡 现在可以检查任务190是否开始执行")
    print("💡 监控命令: python debug_scheduler.py")

if __name__ == "__main__":
    asyncio.run(main())
