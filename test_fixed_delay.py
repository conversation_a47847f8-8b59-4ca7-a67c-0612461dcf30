#!/usr/bin/env python3
"""
测试修复后的分组延迟逻辑
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_simple_task(task_name, device_id, delay_group_sec=2, delay_like_sec=0.5):
    """创建简单任务"""
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test_name": task_name},
        "target_scope": "single",
        "target_id": device_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def monitor_task_simple(task_id, task_name, max_wait=20):
    """简单监控任务状态"""
    log_with_time(f"👀 监控 {task_name} (ID: {task_id})")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status != last_status:
                    elapsed = time.time() - start_time
                    log_with_time(f"📊 {task_name} 状态: {last_status} → {current_status} (耗时: {elapsed:.1f}s)")
                    last_status = current_status
                
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 完成，状态: {current_status}, 总耗时: {total_time:.1f}s")
                    return completion_time, current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 监控 {task_name} 出错: {e}")
        
        time.sleep(1)  # 每秒检查一次
    
    log_with_time(f"⏰ {task_name} 监控超时")
    return None, 'timeout'

def test_simple_delay():
    """简单的延迟测试"""
    log_with_time("=== 简单分组延迟测试 ===")
    
    # 使用设备9
    device_id = 9
    log_with_time(f"📱 使用设备ID: {device_id}")
    log_with_time(f"⏰ 设置: 分组延迟=2秒, 操作延迟=0.5秒")
    
    test_start_time = time.time()
    
    # 创建两个任务
    log_with_time("🚀 创建第一个任务...")
    task1 = create_simple_task("简单测试1", device_id, 2.0, 0.5)
    if not task1:
        return
    
    time.sleep(0.5)  # 短暂等待
    
    log_with_time("🚀 创建第二个任务...")
    task2 = create_simple_task("简单测试2", device_id, 2.0, 0.5)
    if not task2:
        return
    
    log_with_time("📋 开始监控任务执行...")
    
    # 监控任务完成
    completion1, status1 = monitor_task_simple(task1.get('id'), "简单测试1", 30)
    completion2, status2 = monitor_task_simple(task2.get('id'), "简单测试2", 30)
    
    # 分析结果
    if completion1 and completion2:
        delay = completion2 - completion1
        log_with_time(f"📊 任务间隔: {delay:.1f}秒")
        
        # 预期：任务1完成 + 2秒分组延迟 + 0.5秒操作延迟 = 2.5秒间隔
        if 2.0 <= delay <= 4.0:
            log_with_time(f"✅ 分组延迟工作正常 ({delay:.1f}秒)")
        else:
            log_with_time(f"⚠️ 分组延迟可能异常 ({delay:.1f}秒)")
    
    total_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_time:.1f}秒")

def check_system_status():
    """检查系统状态"""
    log_with_time("=== 检查系统状态 ===")
    
    try:
        # 检查API
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API正常")
        else:
            log_with_time(f"❌ API异常: {response.status_code}")
            return False
        
        # 检查设备9
        response = requests.get(f"{API_BASE_URL}/devices/9", timeout=5)
        if response.status_code == 200:
            device = response.json()
            log_with_time(f"📱 设备9状态: {device.get('online_status')}")
            if device.get('online_status') != 'online':
                log_with_time("⚠️ 设备9不在线")
                return False
        else:
            log_with_time("❌ 无法获取设备9信息")
            return False
        
        # 检查pending任务数量
        import sys
        sys.path.append('.')
        from app.db import get_db
        from app.models.task import TaskQueue
        
        db = next(get_db())
        try:
            pending_count = db.query(TaskQueue).filter(TaskQueue.status == 'pending').count()
            running_count = db.query(TaskQueue).filter(TaskQueue.status == 'running').count()
            log_with_time(f"📊 队列状态: pending={pending_count}, running={running_count}")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        log_with_time(f"❌ 系统检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 修复后的分组延迟测试")
    print("💡 验证：任务真正完成后才记录完成时间")
    print("=" * 60)
    
    # 1. 检查系统状态
    if not check_system_status():
        log_with_time("❌ 系统状态检查失败，退出测试")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 2. 运行简单测试
    test_simple_delay()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 修复要点:")
    print("   1. 任务发送成功 ≠ 任务完成")
    print("   2. 只有收到设备返回结果时才记录完成时间")
    print("   3. 分组延迟基于真正的任务完成时间")
    print("   4. 单位换算：前端秒*1000→毫秒，后端毫秒/1000→秒")
    print("=" * 60)

if __name__ == "__main__":
    main()
