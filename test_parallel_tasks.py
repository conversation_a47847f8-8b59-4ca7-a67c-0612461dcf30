#!/usr/bin/env python3
"""
并行任务处理测试脚本
验证优化后的调度器是否支持真正的并行处理
"""

import time
import requests
import threading
from datetime import datetime
import json

# API配置
API_BASE_URL = "http://localhost:8000"

def log_info(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] ℹ️  {message}")

def log_success(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] ✅ {message}")

def log_error(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] ❌ {message}")

def test_api():
    """测试API连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_devices():
    """获取设备列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        if response.status_code == 200:
            return response.json()
        return []
    except:
        return []

def get_groups():
    """获取分组列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/groups", timeout=10)
        if response.status_code == 200:
            return response.json()
        return []
    except:
        return []

def create_task(task_name, target_scope, target_id, delay_group_sec=3, delay_like_sec=1):
    """创建测试任务"""
    try:
        task_data = {
            "task_type": "sign",
            "parameters": {"count": 1, "test_name": task_name},
            "target_scope": target_scope,
            "target_id": target_id,
            "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
            "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
        }
        
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            log_success(f"任务 {task_name} 创建成功，ID: {task.get('id')}, 耗时: {(end_time-start_time)*1000:.1f}ms")
            return task
        else:
            log_error(f"任务 {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_error(f"创建任务 {task_name} 失败: {e}")
        return None

def create_task_async(task_name, target_scope, target_id, delay_group_sec, results):
    """异步创建任务"""
    task = create_task(task_name, target_scope, target_id, delay_group_sec)
    results[task_name] = task

def test_parallel_creation():
    """测试并行任务创建"""
    log_info("=== 测试并行任务创建 ===")
    
    # 获取测试目标
    devices = get_devices()
    groups = get_groups()
    
    if not devices and not groups:
        log_error("没有可用的设备或分组")
        return False
    
    # 选择测试目标
    targets = []
    if groups:
        for i, group in enumerate(groups[:3]):  # 最多3个分组
            targets.append(("group", group.get('id'), f"分组{i+1}"))
    
    if devices and len(targets) < 3:
        for i, device in enumerate(devices[:3-len(targets)]):
            targets.append(("single", device.get('id'), f"设备{i+1}"))
    
    if not targets:
        log_error("没有可用的测试目标")
        return False
    
    log_info(f"将创建 {len(targets)} 个并行任务")
    
    # 并行创建任务
    results = {}
    threads = []
    start_time = time.time()
    
    for i, (scope, target_id, name) in enumerate(targets):
        task_name = f"并行任务{i+1}_{name}"
        thread = threading.Thread(
            target=create_task_async,
            args=(task_name, scope, target_id, 5.0, results)
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有任务创建完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    success_count = sum(1 for task in results.values() if task is not None)
    
    log_info(f"并行创建完成: {success_count}/{len(targets)} 成功, 总耗时: {total_time:.2f}秒")
    
    if success_count > 0:
        log_success("✅ 并行任务创建测试通过")
        return True
    else:
        log_error("❌ 所有任务创建失败")
        return False

def test_sequential_vs_parallel():
    """对比串行和并行处理效果"""
    log_info("=== 对比串行 vs 并行处理 ===")
    
    devices = get_devices()
    groups = get_groups()
    
    if not groups:
        log_error("需要至少一个分组来测试")
        return False
    
    group = groups[0]
    group_id = group.get('id')
    group_name = group.get('group_name')
    
    log_info(f"使用分组: {group_name} (ID: {group_id})")
    
    # 创建多个任务到同一分组
    task_count = 3
    log_info(f"创建 {task_count} 个任务到同一分组，观察处理效果")
    
    created_tasks = []
    start_time = time.time()
    
    for i in range(task_count):
        task_name = f"分组测试任务{i+1}"
        task = create_task(task_name, "group", group_id, 2.0, 0.5)  # 2秒分组延迟
        if task:
            created_tasks.append(task)
        time.sleep(0.1)  # 稍微间隔一下
    
    end_time = time.time()
    creation_time = end_time - start_time
    
    log_info(f"任务创建完成: {len(created_tasks)}/{task_count}, 耗时: {creation_time:.2f}秒")
    
    if created_tasks:
        log_success("✅ 分组任务创建成功")
        log_info("💡 观察要点:")
        log_info("   1. 不同设备的任务应该并行处理")
        log_info("   2. 同一设备的任务应该按分组延迟串行处理")
        log_info("   3. 查看后端日志确认并行效果")
        return True
    else:
        log_error("❌ 分组任务创建失败")
        return False

def monitor_system_status():
    """监控系统状态"""
    log_info("=== 系统状态监控 ===")
    
    devices = get_devices()
    groups = get_groups()
    
    log_info(f"📊 系统状态:")
    log_info(f"   设备数量: {len(devices)}")
    log_info(f"   分组数量: {len(groups)}")
    
    # 显示设备状态
    if devices:
        log_info("📱 设备状态:")
        for device in devices:
            device_id = device.get('id')
            device_number = device.get('device_number', 'unknown')
            status = device.get('online_status', 'unknown')
            log_info(f"   - {device_number} (ID: {device_id}, 状态: {status})")
    
    # 显示分组信息
    if groups:
        log_info("👥 分组信息:")
        for group in groups:
            group_id = group.get('id')
            group_name = group.get('group_name', 'unknown')
            log_info(f"   - {group_name} (ID: {group_id})")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 并行任务处理测试")
    print("=" * 60)
    
    # 1. 测试API连接
    if not test_api():
        log_error("API连接失败，请确保后端服务正在运行")
        log_info("启动命令: ./start_backend.sh")
        return
    
    log_success("API连接正常")
    
    # 2. 监控系统状态
    monitor_system_status()
    
    print("\n" + "=" * 40)
    print("🚀 开始并行处理测试")
    print("=" * 40)
    
    # 3. 测试并行任务创建
    test1_success = test_parallel_creation()
    
    print("\n" + "-" * 40)
    
    # 4. 测试串行vs并行效果
    test2_success = test_sequential_vs_parallel()
    
    # 5. 测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    
    if test1_success and test2_success:
        log_success("✅ 所有测试通过")
        print("\n💡 优化效果:")
        print("   1. 任务创建速度更快")
        print("   2. 不同设备任务并行处理")
        print("   3. 分组延迟只在同设备间生效")
        print("   4. 新任务不会被旧任务阻塞")
    else:
        log_error("❌ 部分测试失败")
    
    print("\n📝 查看详细日志:")
    print("   journalctl -u wb-system -f")
    print("   或: tail -f logs/app.log")
    print("=" * 60)

if __name__ == "__main__":
    main()
