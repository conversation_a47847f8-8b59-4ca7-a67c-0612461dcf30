#!/usr/bin/env python3
"""
测试静默心跳功能
验证服务端不再返回心跳确认消息
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SilentHeartbeatTester:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        self.received_messages = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} 连接成功")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.websocket and not self.websocket.closed:
                message = await self.websocket.recv()
                self.received_messages.append({
                    'timestamp': datetime.now().isoformat(),
                    'message': message
                })
                print(f"📨 收到服务器消息: {message}")
        except Exception as e:
            print(f"📨 消息监听结束: {e}")
            
    async def send_heartbeat_formats(self):
        """发送各种格式的心跳消息"""
        print("\n📤 测试静默心跳处理...")
        
        heartbeat_formats = [
            # 标准JSON格式
            {
                "name": "标准JSON心跳",
                "data": json.dumps({
                    "type": "heartbeat",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "device_number": self.device_number
                })
            },
            # ISO时间戳格式
            {
                "name": "ISO时间戳心跳",
                "data": datetime.utcnow().isoformat() + "Z"
            },
            # Date对象字符串格式
            {
                "name": "Date字符串心跳",
                "data": f"new Date-{datetime.now().strftime('%a %b %d %Y %H:%M:%S')} GMT+0800"
            },
            # 数字时间戳格式
            {
                "name": "数字时间戳心跳",
                "data": str(int(time.time() * 1000))
            }
        ]
        
        initial_message_count = len(self.received_messages)
        
        for heartbeat in heartbeat_formats:
            try:
                print(f"📤 发送 {heartbeat['name']}: {heartbeat['data'][:50]}...")
                await self.websocket.send(heartbeat['data'])
                
                # 等待可能的响应
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ 发送 {heartbeat['name']} 失败: {e}")
        
        # 检查是否收到任何心跳确认
        final_message_count = len(self.received_messages)
        heartbeat_acks = [
            msg for msg in self.received_messages[initial_message_count:]
            if 'heartbeat_ack' in msg['message']
        ]
        
        print(f"\n📊 心跳测试结果:")
        print(f"   发送心跳数量: {len(heartbeat_formats)}")
        print(f"   收到消息数量: {final_message_count - initial_message_count}")
        print(f"   心跳确认数量: {len(heartbeat_acks)}")
        
        if len(heartbeat_acks) == 0:
            print("✅ 静默心跳成功：没有收到任何心跳确认消息")
            return True
        else:
            print("❌ 静默心跳失败：仍然收到心跳确认消息")
            for ack in heartbeat_acks:
                print(f"   收到确认: {ack['message']}")
            return False
            
    async def test_non_heartbeat_messages(self):
        """测试非心跳消息是否正常处理"""
        print("\n📤 测试非心跳消息处理...")
        
        non_heartbeat_messages = [
            # 任务完成消息
            {
                "type": "task_completion",
                "task_id": 999,
                "device_id": 1,
                "status": "success",
                "timestamp": datetime.utcnow().isoformat()
            },
            # 无效JSON
            "invalid json message",
            # 空消息
            "",
            # 未知类型消息
            {
                "type": "unknown_type",
                "data": "test"
            }
        ]
        
        initial_message_count = len(self.received_messages)
        
        for i, msg in enumerate(non_heartbeat_messages):
            try:
                if isinstance(msg, dict):
                    data = json.dumps(msg)
                    print(f"📤 发送消息 {i+1}: {msg['type']}")
                else:
                    data = msg
                    print(f"📤 发送消息 {i+1}: {data[:30]}...")
                    
                await self.websocket.send(data)
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 发送消息 {i+1} 失败: {e}")
        
        # 检查响应
        final_message_count = len(self.received_messages)
        responses = self.received_messages[initial_message_count:]
        
        print(f"\n📊 非心跳消息测试结果:")
        print(f"   发送消息数量: {len(non_heartbeat_messages)}")
        print(f"   收到响应数量: {len(responses)}")
        
        # 应该收到错误响应或确认响应
        return len(responses) > 0

async def test_silent_heartbeat():
    """测试静默心跳功能"""
    print("🧪 测试静默心跳功能...")
    
    tester = SilentHeartbeatTester("test_silent_device")
    
    try:
        # 连接到服务器
        if not await tester.connect():
            return False
            
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 测试心跳静默处理
        heartbeat_result = await tester.send_heartbeat_formats()
        
        # 等待一段时间确保没有延迟的响应
        await asyncio.sleep(3)
        
        # 测试非心跳消息正常处理
        non_heartbeat_result = await tester.test_non_heartbeat_messages()
        
        # 等待响应
        await asyncio.sleep(2)
        
        print(f"\n📊 总体测试结果:")
        print(f"   静默心跳测试: {'✅ 通过' if heartbeat_result else '❌ 失败'}")
        print(f"   非心跳消息测试: {'✅ 通过' if non_heartbeat_result else '❌ 失败'}")
        
        return heartbeat_result and non_heartbeat_result
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def test_continuous_silent_heartbeat():
    """测试连续静默心跳"""
    print("\n🧪 测试连续静默心跳...")
    
    tester = SilentHeartbeatTester("test_continuous_silent")
    
    try:
        if not await tester.connect():
            return False
            
        print("📤 开始连续发送心跳（20次）...")
        
        initial_message_count = len(tester.received_messages)
        
        # 连续发送20次心跳
        for i in range(20):
            # 交替使用不同格式
            if i % 4 == 0:
                # JSON格式
                heartbeat = json.dumps({
                    "type": "heartbeat",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "sequence": i
                })
            elif i % 4 == 1:
                # ISO时间戳
                heartbeat = datetime.utcnow().isoformat() + "Z"
            elif i % 4 == 2:
                # Date字符串
                heartbeat = f"new Date-{datetime.now().strftime('%a %b %d %Y %H:%M:%S')} GMT+0800"
            else:
                # 数字时间戳
                heartbeat = str(int(time.time() * 1000))
                
            await tester.websocket.send(heartbeat)
            print(f"📤 心跳 #{i+1}")
            
            await asyncio.sleep(0.2)  # 每200ms发送一次
        
        # 等待可能的响应
        await asyncio.sleep(3)
        
        final_message_count = len(tester.received_messages)
        received_count = final_message_count - initial_message_count
        
        print(f"\n📊 连续心跳测试结果:")
        print(f"   发送心跳数量: 20")
        print(f"   收到响应数量: {received_count}")
        
        if received_count == 0:
            print("✅ 连续静默心跳成功：没有收到任何响应")
            return True
        else:
            print("❌ 连续静默心跳失败：收到了响应")
            return False
            
    except Exception as e:
        print(f"❌ 连续心跳测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def main():
    """主测试函数"""
    print("🚀 静默心跳测试开始...\n")
    
    tests = [
        ("静默心跳功能测试", test_silent_heartbeat),
        ("连续静默心跳测试", test_continuous_silent_heartbeat)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待3秒后继续...")
        await asyncio.sleep(3)
    
    print(f"\n📊 总体测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！静默心跳功能正常工作")
        print("\n📋 静默心跳优势:")
        print("1. ✅ 减少网络流量")
        print("2. ✅ 降低客户端处理负担")
        print("3. ✅ 简化消息处理逻辑")
        print("4. ✅ 提高整体性能")
        
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
