('E:\\PythonWBYK\\fastApiProject\\frontend\\build\\run\\PYZ-00.pyz',
 [('__future__', 'D:\\myProgram\\Python39\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\myProgram\\Python39\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\myProgram\\Python39\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\myProgram\\Python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\myProgram\\Python39\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\myProgram\\Python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\myProgram\\Python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\myProgram\\Python39\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\myProgram\\Python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\myProgram\\Python39\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\myProgram\\Python39\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\myProgram\\Python39\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\myProgram\\Python39\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\myProgram\\Python39\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\myProgram\\Python39\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\myProgram\\Python39\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\myProgram\\Python39\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\myProgram\\Python39\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\myProgram\\Python39\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\myProgram\\Python39\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\myProgram\\Python39\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\myProgram\\Python39\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\myProgram\\Python39\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\myProgram\\Python39\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\myProgram\\Python39\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\myProgram\\Python39\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\myProgram\\Python39\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\myProgram\\Python39\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\myProgram\\Python39\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\myProgram\\Python39\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\myProgram\\Python39\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\myProgram\\Python39\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\myProgram\\Python39\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\myProgram\\Python39\\lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\myProgram\\Python39\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'E:\\PythonWBYK\\fastApiProject\\frontend\\config.py', 'PYMODULE'),
  ('configparser', 'D:\\myProgram\\Python39\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\myProgram\\Python39\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\myProgram\\Python39\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\myProgram\\Python39\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\myProgram\\Python39\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\myProgram\\Python39\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\myProgram\\Python39\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\myProgram\\Python39\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\myProgram\\Python39\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\myProgram\\Python39\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\myProgram\\Python39\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\myProgram\\Python39\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\myProgram\\Python39\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\myProgram\\Python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\myProgram\\Python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\myProgram\\Python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\myProgram\\Python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\myProgram\\Python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\myProgram\\Python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\myProgram\\Python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\myProgram\\Python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\myProgram\\Python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\myProgram\\Python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\myProgram\\Python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\myProgram\\Python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\myProgram\\Python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\myProgram\\Python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\myProgram\\Python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\myProgram\\Python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\myProgram\\Python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\myProgram\\Python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\myProgram\\Python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\myProgram\\Python39\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\myProgram\\Python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\myProgram\\Python39\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\myProgram\\Python39\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\myProgram\\Python39\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\myProgram\\Python39\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\myProgram\\Python39\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\myProgram\\Python39\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\myProgram\\Python39\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\myProgram\\Python39\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\myProgram\\Python39\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\myProgram\\Python39\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\myProgram\\Python39\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\myProgram\\Python39\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\myProgram\\Python39\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\myProgram\\Python39\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\myProgram\\Python39\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server', 'D:\\myProgram\\Python39\\lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\myProgram\\Python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\myProgram\\Python39\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\myProgram\\Python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\myProgram\\Python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\myProgram\\Python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\myProgram\\Python39\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\myProgram\\Python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\myProgram\\Python39\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\myProgram\\Python39\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\myProgram\\Python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\myProgram\\Python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\myProgram\\Python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\myProgram\\Python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging', 'D:\\myProgram\\Python39\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\myProgram\\Python39\\lib\\lzma.py', 'PYMODULE'),
  ('main', 'E:\\PythonWBYK\\fastApiProject\\frontend\\main.py', 'PYMODULE'),
  ('mimetypes', 'D:\\myProgram\\Python39\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\myProgram\\Python39\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\myProgram\\Python39\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\myProgram\\Python39\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\myProgram\\Python39\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\myProgram\\Python39\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\myProgram\\Python39\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\myProgram\\Python39\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\myProgram\\Python39\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\myProgram\\Python39\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\myProgram\\Python39\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\myProgram\\Python39\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\myProgram\\Python39\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\myProgram\\Python39\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\myProgram\\Python39\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\myProgram\\Python39\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\myProgram\\Python39\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\myProgram\\Python39\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\myProgram\\Python39\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\myProgram\\Python39\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\myProgram\\Python39\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\myProgram\\Python39\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\myProgram\\Python39\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\myProgram\\Python39\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\myProgram\\Python39\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'D:\\myProgram\\Python39\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\myProgram\\Python39\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\myProgram\\Python39\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\myProgram\\Python39\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\myProgram\\Python39\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\myProgram\\Python39\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\myProgram\\Python39\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\myProgram\\Python39\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\myProgram\\Python39\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\myProgram\\Python39\\lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\myProgram\\Python39\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\myProgram\\Python39\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\myProgram\\Python39\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\myProgram\\Python39\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\myProgram\\Python39\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\myProgram\\Python39\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\myProgram\\Python39\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\myProgram\\Python39\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\myProgram\\Python39\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\myProgram\\Python39\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\myProgram\\Python39\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\myProgram\\Python39\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\myProgram\\Python39\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\myProgram\\Python39\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\myProgram\\Python39\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\myProgram\\Python39\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\myProgram\\Python39\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\myProgram\\Python39\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\myProgram\\Python39\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\myProgram\\Python39\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\myProgram\\Python39\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\myProgram\\Python39\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\myProgram\\Python39\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\myProgram\\Python39\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\myProgram\\Python39\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\myProgram\\Python39\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\myProgram\\Python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\myProgram\\Python39\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\myProgram\\Python39\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('utils',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\utils\\__init__.py',
   'PYMODULE'),
  ('utils.api_client',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\utils\\api_client.py',
   'PYMODULE'),
  ('utils.time_utils',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\utils\\time_utils.py',
   'PYMODULE'),
  ('uu', 'D:\\myProgram\\Python39\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'D:\\myProgram\\Python39\\lib\\webbrowser.py', 'PYMODULE'),
  ('widgets',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\__init__.py',
   'PYMODULE'),
  ('widgets.device_manager',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\device_manager.py',
   'PYMODULE'),
  ('widgets.group_manager',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\group_manager.py',
   'PYMODULE'),
  ('widgets.new_task_manager',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\new_task_manager.py',
   'PYMODULE'),
  ('widgets.status_bar',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\status_bar.py',
   'PYMODULE'),
  ('widgets.task_viewer',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\task_viewer.py',
   'PYMODULE'),
  ('widgets.websocket_monitor',
   'E:\\PythonWBYK\\fastApiProject\\frontend\\widgets\\websocket_monitor.py',
   'PYMODULE'),
  ('xml', 'D:\\myProgram\\Python39\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\myProgram\\Python39\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\myProgram\\Python39\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\myProgram\\Python39\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\myProgram\\Python39\\lib\\zipimport.py', 'PYMODULE')])
