#!/usr/bin/env python3
"""
单个任务测试 - 验证分发延迟
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_single_task():
    """创建单个任务并观察"""
    log_with_time("=== 单个任务测试 ===")
    
    # 获取设备
    devices_response = requests.get(f"{API_BASE_URL}/devices")
    devices = devices_response.json()
    
    if not devices:
        log_with_time("❌ 没有可用设备")
        return
    
    device = devices[0]
    device_id = device.get('id')
    device_number = device.get('device_number')
    
    log_with_time(f"📱 使用设备: {device_number} (ID: {device_id})")
    
    # 创建任务
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test_name": "单个任务测试"},
        "target_scope": "single",
        "target_id": device_id,
        "delay_group": 5000,  # 5秒分组延迟
        "delay_like": 1000    # 1秒操作延迟
    }
    
    log_with_time("🚀 开始创建任务...")
    start_time = time.time()
    
    response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=15)
    
    end_time = time.time()
    duration = (end_time - start_time) * 1000
    
    if response.status_code == 200:
        task = response.json()
        log_with_time(f"✅ 任务创建成功，ID: {task.get('id')}, 耗时: {duration:.1f}ms")
        
        # 等待一段时间，然后检查任务状态
        log_with_time("⏳ 等待10秒后检查任务状态...")
        time.sleep(10)
        
        # 检查任务状态
        status_response = requests.get(f"{API_BASE_URL}/tasks/{task.get('id')}/status")
        if status_response.status_code == 200:
            status = status_response.json()
            log_with_time(f"📊 任务状态: {status}")
        
    else:
        log_with_time(f"❌ 任务创建失败: {response.status_code}")
        log_with_time(f"响应: {response.text}")

if __name__ == "__main__":
    create_single_task()
