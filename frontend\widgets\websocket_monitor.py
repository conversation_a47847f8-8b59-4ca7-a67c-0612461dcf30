"""
WebSocket连接监控界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading
import time

from config import Config

class WebSocketMonitorFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.connections_data = []
        self.is_monitoring = False
        
        self.create_widgets()
        self.start_monitoring()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题和工具栏
        self.create_header()
        
        # 连接状态概览
        self.create_status_overview()
        
        # 连接列表
        self.create_connection_list()
        
        # 日志区域
        self.create_log_area()
        
    def create_header(self):
        """创建标题和工具栏"""
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 标题
        title_label = ttk.Label(header_frame, text="WebSocket连接监控", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # 工具按钮
        btn_frame = ttk.Frame(header_frame)
        btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(btn_frame, text="🔄 刷新", command=self.refresh).pack(side=tk.LEFT, padx=2)
        self.monitor_btn = ttk.Button(btn_frame, text="⏸️ 停止监控", command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="🗑️ 清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=2)
        
    def create_status_overview(self):
        """创建状态概览"""
        overview_frame = ttk.LabelFrame(self, text="连接状态概览")
        overview_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 统计信息框架
        stats_frame = ttk.Frame(overview_frame)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 创建统计标签
        self.stats_labels = {}
        stats_items = [
            ('总连接数', 'total', '#007bff'),
            ('活跃连接', 'active', '#28a745'),
            ('断开连接', 'disconnected', '#dc3545'),
            ('心跳正常', 'heartbeat_ok', '#28a745'),
            ('心跳异常', 'heartbeat_error', '#ffc107')
        ]
        
        for i, (label_text, key, color) in enumerate(stats_items):
            frame = ttk.Frame(stats_frame)
            frame.grid(row=0, column=i, padx=10, pady=5)
            
            ttk.Label(frame, text=label_text, style='Header.TLabel').pack()
            value_label = ttk.Label(frame, text="0", font=('Arial', 16, 'bold'), foreground=color)
            value_label.pack()
            self.stats_labels[key] = value_label
            
        # 最后更新时间
        self.last_update_label = ttk.Label(overview_frame, text="最后更新: -", style='Status.TLabel')
        self.last_update_label.pack(pady=5)
        
    def create_connection_list(self):
        """创建连接列表"""
        # 主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧连接列表
        list_frame = ttk.LabelFrame(main_frame, text="活跃连接")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 创建Treeview
        columns = ('device_number', 'device_ip', 'status', 'last_heartbeat', 'duration')
        self.connection_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        # 设置列标题
        self.connection_tree.heading('device_number', text='设备号')
        self.connection_tree.heading('device_ip', text='IP地址')
        self.connection_tree.heading('status', text='状态')
        self.connection_tree.heading('last_heartbeat', text='最后心跳')
        self.connection_tree.heading('duration', text='连接时长')
        
        # 设置列宽
        self.connection_tree.column('device_number', width=120)
        self.connection_tree.column('device_ip', width=120)
        self.connection_tree.column('status', width=80)
        self.connection_tree.column('last_heartbeat', width=150)
        self.connection_tree.column('duration', width=100)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.connection_tree.yview)
        self.connection_tree.configure(yscrollcommand=scrollbar_y.set)
        
        # 布局
        self.connection_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.connection_tree.bind('<<TreeviewSelect>>', self.on_connection_selected)
        
        # 右侧连接详情
        self.create_connection_details(main_frame)
        
    def create_connection_details(self, parent):
        """创建连接详情面板"""
        details_frame = ttk.LabelFrame(parent, text="连接详情")
        details_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        details_frame.configure(width=300)
        
        # 详情信息
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息
        ttk.Label(info_frame, text="连接信息", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 10))
        
        self.detail_labels = {}
        fields = [
            ('设备号', 'device_number'),
            ('IP地址', 'device_ip'),
            ('连接状态', 'status'),
            ('最后心跳', 'last_heartbeat'),
            ('连接时长', 'duration'),
            ('心跳间隔', 'heartbeat_interval')
        ]
        
        for label_text, field_name in fields:
            frame = ttk.Frame(info_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(frame, text=f"{label_text}:", width=10).pack(side=tk.LEFT)
            label = ttk.Label(frame, text="-", foreground="blue")
            label.pack(side=tk.LEFT, fill=tk.X, expand=True)
            self.detail_labels[field_name] = label
            
        # 操作按钮
        ttk.Separator(info_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        
        btn_frame = ttk.Frame(info_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="发送测试消息", command=self.send_test_message).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="断开连接", command=self.disconnect_device).pack(fill=tk.X, pady=2)
        
    def create_log_area(self):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(self, text="连接日志")
        log_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 配置日志文本样式
        self.log_text.tag_configure('info', foreground='blue')
        self.log_text.tag_configure('warning', foreground='orange')
        self.log_text.tag_configure('error', foreground='red')
        self.log_text.tag_configure('success', foreground='green')
        
    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        self.monitor_btn.config(text="⏸️ 停止监控")
        
        def monitor_loop():
            while self.is_monitoring:
                try:
                    self.refresh_connections()
                    time.sleep(Config.CONNECTION_CHECK_INTERVAL)
                except Exception as e:
                    self.add_log(f"监控异常: {e}", 'error')
                    time.sleep(5)
                    
        threading.Thread(target=monitor_loop, daemon=True).start()
        self.add_log("开始WebSocket连接监控", 'info')
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.monitor_btn.config(text="▶️ 开始监控")
        self.add_log("停止WebSocket连接监控", 'info')
        
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.is_monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()
            
    def refresh(self):
        """手动刷新"""
        self.refresh_connections()
        self.add_log("手动刷新连接状态", 'info')
        
    def refresh_connections(self):
        """刷新连接状态"""
        def fetch_data():
            try:
                connections = self.api_client.get_websocket_connections()
                heartbeat_status = self.api_client.get_heartbeat_status()
                self.winfo_toplevel().after(0, lambda: self.update_connections_display(connections, heartbeat_status))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: self.add_log(f"获取连接数据失败: {e}", 'error'))
                
        threading.Thread(target=fetch_data, daemon=True).start()
        
    def update_connections_display(self, connections, heartbeat_status):
        """更新连接显示"""
        self.connections_data = connections or []
        
        # 更新统计信息
        self.update_statistics(heartbeat_status)
        
        # 更新连接列表
        for item in self.connection_tree.get_children():
            self.connection_tree.delete(item)
            
        for conn in self.connections_data:
            # 格式化最后心跳时间
            last_heartbeat = conn.get('last_heartbeat', '')
            if last_heartbeat:
                try:
                    dt = datetime.fromisoformat(last_heartbeat.replace('Z', '+00:00'))
                    last_heartbeat = dt.strftime('%H:%M:%S')
                except:
                    pass
                    
            # 计算连接时长（这里需要根据实际数据结构调整）
            duration = "计算中..."
            
            # 状态显示
            status = conn.get('status', 'unknown')
            status_text = "🟢 已连接" if status == 'connected' else "🔴 断开"
            
            values = (
                conn.get('device_number', ''),
                conn.get('device_ip', ''),
                status_text,
                last_heartbeat,
                duration
            )
            
            self.connection_tree.insert('', tk.END, values=values)
            
        # 更新最后更新时间
        self.last_update_label.config(text=f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
        
    def update_statistics(self, heartbeat_status):
        """更新统计信息"""
        if heartbeat_status:
            self.stats_labels['total'].config(text=str(heartbeat_status.get('total', 0)))
            self.stats_labels['active'].config(text=str(heartbeat_status.get('online', 0)))
            self.stats_labels['disconnected'].config(text=str(heartbeat_status.get('offline', 0)))
            self.stats_labels['heartbeat_ok'].config(text=str(heartbeat_status.get('online', 0)))
            self.stats_labels['heartbeat_error'].config(text=str(heartbeat_status.get('offline', 0)))
        else:
            for key in self.stats_labels:
                self.stats_labels[key].config(text="0")
                
    def on_connection_selected(self, event):
        """连接选择事件"""
        selection = self.connection_tree.selection()
        if not selection:
            return
            
        item = selection[0]
        device_number = self.connection_tree.item(item)['values'][0]
        
        # 查找连接数据
        conn_data = None
        for conn in self.connections_data:
            if conn.get('device_number') == device_number:
                conn_data = conn
                break
                
        if conn_data:
            self.update_connection_details(conn_data)
            
    def update_connection_details(self, conn_data):
        """更新连接详情显示"""
        # 格式化最后心跳时间
        last_heartbeat = conn_data.get('last_heartbeat', '')
        if last_heartbeat:
            try:
                dt = datetime.fromisoformat(last_heartbeat.replace('Z', '+00:00'))
                last_heartbeat = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
                
        # 更新详情标签
        self.detail_labels['device_number'].config(text=conn_data.get('device_number', '-'))
        self.detail_labels['device_ip'].config(text=conn_data.get('device_ip', '-'))
        
        status = conn_data.get('status', 'unknown')
        status_text = "已连接" if status == 'connected' else "断开"
        status_color = Config.COLORS.get('online' if status == 'connected' else 'offline', 'black')
        self.detail_labels['status'].config(text=status_text, foreground=status_color)
        
        self.detail_labels['last_heartbeat'].config(text=last_heartbeat)
        self.detail_labels['duration'].config(text="计算中...")
        self.detail_labels['heartbeat_interval'].config(text="3秒")
        
    def get_selected_connection(self):
        """获取当前选中的连接"""
        selection = self.connection_tree.selection()
        if not selection:
            return None
            
        item = selection[0]
        device_number = self.connection_tree.item(item)['values'][0]
        
        for conn in self.connections_data:
            if conn.get('device_number') == device_number:
                return conn
        return None
        
    def send_test_message(self):
        """发送测试消息"""
        conn = self.get_selected_connection()
        if not conn:
            messagebox.showwarning("未选择连接", "请先选择要发送测试消息的连接")
            return
            
        # TODO: 实现发送测试消息功能
        device_number = conn.get('device_number')
        self.add_log(f"向设备 {device_number} 发送测试消息", 'info')
        messagebox.showinfo("功能开发中", f"向设备 {device_number} 发送测试消息功能正在开发中")
        
    def disconnect_device(self):
        """断开设备连接"""
        conn = self.get_selected_connection()
        if not conn:
            messagebox.showwarning("未选择连接", "请先选择要断开的连接")
            return
            
        device_number = conn.get('device_number')
        if messagebox.askyesno("确认断开", f"确定要断开设备 {device_number} 的连接吗？"):
            # TODO: 实现断开连接功能
            self.add_log(f"断开设备 {device_number} 的连接", 'warning')
            messagebox.showinfo("功能开发中", f"断开设备 {device_number} 连接功能正在开发中")
            
    def add_log(self, message, level='info'):
        """添加日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message, level)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > Config.MAX_LOG_LINES:
            self.log_text.delete(1.0, f"{lines - Config.MAX_LOG_LINES}.0")
            
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("日志已清空", 'info')
