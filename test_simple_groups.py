#!/usr/bin/env python3
"""
简单测试分组端点
"""

import requests
import json

def test_groups_endpoint():
    """测试分组端点"""
    url = "http://localhost:8000/groups/"
    
    try:
        print(f"请求: GET {url}")
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 成功获取分组数据:")
                print(f"   数据类型: {type(data)}")
                print(f"   数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
                
                if isinstance(data, list) and data:
                    print(f"   第一个分组: {data[0]}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text[:500]}")
                
        else:
            print(f"❌ 请求失败")
            print(f"   响应内容: {response.text[:500]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

def test_single_group():
    """测试单个分组端点"""
    # 先测试存在的分组ID
    for group_id in [1, 2]:
        url = f"http://localhost:8000/groups/{group_id}"
        
        try:
            print(f"\n请求: GET {url}")
            response = requests.get(url, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 成功获取分组 {group_id}:")
                    print(f"   分组数据: {data}")
                    break
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    
            elif response.status_code == 404:
                print(f"   分组 {group_id} 不存在")
            else:
                print(f"❌ 请求失败: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("🧪 简单测试分组端点...")
    print("="*50)
    
    # 1. 测试分组列表端点
    print("1. 测试分组列表端点")
    test_groups_endpoint()
    
    # 2. 测试单个分组端点
    print("\n2. 测试单个分组端点")
    test_single_group()
    
    print("\n" + "="*50)
    print("🎯 测试完成")
    
    print("\n💡 如果仍然有500错误，请:")
    print("1. 重启后端服务器")
    print("2. 检查服务器日志")
    print("3. 确认所有依赖都已更新")

if __name__ == "__main__":
    main()
