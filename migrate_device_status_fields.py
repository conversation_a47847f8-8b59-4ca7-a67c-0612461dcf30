#!/usr/bin/env python3
"""
为DeviceStatus表添加last_update字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db import engine

def migrate_device_status_table():
    """为DeviceStatus表添加last_update字段"""
    print("🔄 开始迁移DeviceStatus表...")
    
    try:
        with engine.connect() as connection:
            # 检查字段是否已存在 (MySQL语法)
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'device_status'
            """))
            columns = [row[0] for row in result.fetchall()]
            
            print(f"   当前字段: {columns}")
            
            # 添加last_update字段
            if 'last_update' not in columns:
                print("   添加 last_update 字段...")
                connection.execute(text("""
                    ALTER TABLE device_status 
                    ADD COLUMN last_update DATETIME DEFAULT CURRENT_TIMESTAMP
                """))
                connection.commit()
                print("   ✅ last_update 字段添加成功")
            else:
                print("   ✅ last_update 字段已存在")
                
            # 验证字段添加
            result = connection.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'device_status'
            """))
            new_columns = [row[0] for row in result.fetchall()]
            
            print(f"   更新后字段: {new_columns}")
            
            if 'last_update' in new_columns:
                print("✅ DeviceStatus表迁移成功")
                return True
            else:
                print("❌ DeviceStatus表迁移失败")
                return False
                
    except Exception as e:
        print(f"❌ 迁移异常: {e}")
        return False

def sync_existing_data():
    """同步现有数据"""
    print("\n🔄 同步现有数据...")
    
    try:
        with engine.connect() as connection:
            # 将现有的last_heartbeat值复制到last_update
            connection.execute(text("""
                UPDATE device_status 
                SET last_update = COALESCE(last_heartbeat, NOW())
                WHERE last_update IS NULL
            """))
            connection.commit()
            
            print("✅ 现有数据同步成功")
            return True
            
    except Exception as e:
        print(f"❌ 数据同步异常: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        from sqlalchemy.orm import sessionmaker
        from app.models.device_status import DeviceStatus
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 尝试查询设备状态
            statuses = db.query(DeviceStatus).limit(1).all()
            
            if statuses:
                status = statuses[0]
                print(f"   测试状态记录: 设备ID {status.device_id}")
                print(f"   is_online: {status.is_online}")
                print(f"   status: {status.status}")
                print(f"   last_heartbeat: {status.last_heartbeat}")
                print(f"   last_update: {status.last_update}")
                print("✅ 新字段可以正常访问")
                return True
            else:
                print("⚠️ 没有设备状态数据，无法验证")
                return True
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

def main():
    """主函数"""
    print("🗄️ DeviceStatus表字段迁移...")
    print("="*50)
    
    # 执行迁移
    if migrate_device_status_table():
        # 同步现有数据
        if sync_existing_data():
            # 验证迁移
            if verify_migration():
                print("\n🎉 迁移完成！")
                print("\n📋 新增字段:")
                print("- last_update: 最后更新时间")
                print("\n💡 现在设备状态更新逻辑已修复")
            else:
                print("\n⚠️ 迁移完成但验证失败")
        else:
            print("\n⚠️ 迁移完成但数据同步失败")
    else:
        print("\n❌ 迁移失败")

if __name__ == "__main__":
    main()
