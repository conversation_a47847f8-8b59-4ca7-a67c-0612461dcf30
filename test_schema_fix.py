#!/usr/bin/env python3
"""
测试schema修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.db import engine
from app.models.device import Device
from app.schemas.device import DeviceRead

def test_device_schema():
    """测试设备schema"""
    print("🔍 测试设备schema...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 获取一个设备
        device = db.query(Device).first()
        if not device:
            print("❌ 没有设备数据")
            return False
            
        print(f"   数据库设备: {device.device_number}")
        print(f"   设备ID: {device.id}")
        print(f"   分组ID: {device.group_id}")
        print(f"   在线状态: {device.online_status}")
        
        # 使用schema转换
        try:
            device_read = DeviceRead.model_validate(device)
            print(f"\n   Schema转换成功:")
            print(f"   设备编号: {device_read.device_number}")
            print(f"   设备ID: {device_read.id}")
            print(f"   分组ID: {device_read.group_id}")
            print(f"   在线状态: {device_read.online_status}")
            
            # 转换为字典
            device_dict = device_read.model_dump()
            print(f"\n   字典格式:")
            for key, value in device_dict.items():
                print(f"     {key}: {value}")
                
            if 'group_id' in device_dict:
                print("✅ Schema包含group_id字段")
                return True
            else:
                print("❌ Schema缺少group_id字段")
                return False
                
        except Exception as e:
            print(f"❌ Schema转换失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
        
    finally:
        db.close()

def test_device_api_direct():
    """直接测试设备API"""
    print("\n📡 直接测试设备API...")
    
    try:
        from app.routes.device import list_devices
        from app.db import get_db
        
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 调用API函数
            devices = list_devices(db)
            
            print(f"   API返回设备数量: {len(devices)}")
            
            if devices:
                first_device = devices[0]
                print(f"   第一个设备:")
                print(f"     类型: {type(first_device)}")
                
                if hasattr(first_device, 'group_id'):
                    print(f"     分组ID: {first_device.group_id}")
                    print("✅ API返回包含group_id")
                else:
                    print("❌ API返回缺少group_id")
                    
                # 如果是字典，检查字典内容
                if hasattr(first_device, 'model_dump'):
                    device_dict = first_device.model_dump()
                    print(f"     字典内容: {device_dict}")
                    
                return hasattr(first_device, 'group_id')
            else:
                print("❌ 没有设备数据")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 直接API测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_schema_file():
    """检查schema文件内容"""
    print("\n📋 检查schema文件内容...")
    
    try:
        with open('app/schemas/device.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查DeviceBase是否包含group_id
        if 'group_id: Optional[int] = None' in content:
            print("✅ DeviceBase包含group_id字段")
        else:
            print("❌ DeviceBase缺少group_id字段")
            
        # 显示DeviceRead类的定义
        lines = content.split('\n')
        in_device_read = False
        device_read_lines = []
        
        for line in lines:
            if 'class DeviceRead(' in line:
                in_device_read = True
                device_read_lines.append(line)
            elif in_device_read:
                if line.startswith('class ') and 'DeviceRead' not in line:
                    break
                device_read_lines.append(line)
                
        if device_read_lines:
            print("\n   DeviceRead类定义:")
            for line in device_read_lines:
                print(f"     {line}")
                
        return True
        
    except Exception as e:
        print(f"❌ 检查schema文件异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试schema修复...")
    print("="*50)
    
    tests = [
        ("Schema文件检查", check_schema_file),
        ("设备Schema测试", test_device_schema),
        ("直接API测试", test_device_api_direct)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
            
    print("="*50)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 Schema修复成功！")
        print("\n💡 需要重启后端服务器以加载新的schema")
    else:
        print("⚠️ Schema修复可能有问题")

if __name__ == "__main__":
    main()
