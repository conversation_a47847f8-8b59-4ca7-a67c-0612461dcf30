#!/usr/bin/env python3
"""
前端测试脚本
用于验证前端组件是否正常工作
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
        
    try:
        import tkinter.ttk
        print("✅ tkinter.ttk 导入成功")
    except ImportError as e:
        print(f"❌ tkinter.ttk 导入失败: {e}")
        return False
        
    return True

def test_config_import():
    """测试配置导入"""
    print("测试配置导入...")
    
    try:
        from config import Config
        print(f"✅ 配置导入成功")
        print(f"   API_BASE_URL: {Config.API_BASE_URL}")
        print(f"   WINDOW_WIDTH: {Config.WINDOW_WIDTH}")
        return True
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")
        return False

def test_api_client():
    """测试API客户端"""
    print("测试API客户端...")
    
    try:
        from utils.api_client import APIClient
        from config import Config
        
        client = APIClient(Config.API_BASE_URL)
        print("✅ API客户端创建成功")
        
        # 测试连接检查
        connected = client.check_connection()
        print(f"   连接状态: {'✅ 已连接' if connected else '❌ 未连接'}")
        
        return True
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False

def test_widgets_import():
    """测试组件导入"""
    print("测试组件导入...")
    
    widgets = [
        ("设备管理器", "widgets.device_manager", "DeviceManagerFrame"),
        ("任务管理器", "widgets.task_manager", "TaskManagerFrame"),
        ("分组管理器", "widgets.group_manager", "GroupManagerFrame"),
        ("WebSocket监控", "widgets.websocket_monitor", "WebSocketMonitorFrame"),
        ("状态栏", "widgets.status_bar", "StatusBar")
    ]
    
    success_count = 0
    
    for name, module_name, class_name in widgets:
        try:
            module = __import__(module_name, fromlist=[class_name])
            widget_class = getattr(module, class_name)
            print(f"✅ {name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {name} 导入失败: {e}")
            
    return success_count == len(widgets)

def test_main_app():
    """测试主应用"""
    print("测试主应用...")
    
    try:
        from main import MainApplication
        print("✅ 主应用导入成功")
        return True
    except Exception as e:
        print(f"❌ 主应用导入失败: {e}")
        return False

class TestGUI:
    """测试GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("前端测试界面")
        self.root.geometry("600x400")
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建测试界面"""
        # 标题
        title_label = ttk.Label(self.root, text="设备管理系统前端测试", font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # 测试按钮框架
        btn_frame = ttk.Frame(self.root)
        btn_frame.pack(pady=20)
        
        # 测试按钮
        ttk.Button(btn_frame, text="测试API连接", command=self.test_api_connection).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="测试设备管理器", command=self.test_device_manager).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="测试任务管理器", command=self.test_task_manager).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        self.result_text = tk.Text(self.root, height=15, width=70)
        scrollbar = ttk.Scrollbar(self.root, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # 运行基本测试
        self.run_basic_tests()
        
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_message)
        self.result_text.see(tk.END)
        self.root.update()
        
    def run_basic_tests(self):
        """运行基本测试"""
        self.log("开始运行基本测试...")
        
        tests = [
            ("基本导入测试", test_basic_imports),
            ("配置导入测试", test_config_import),
            ("API客户端测试", test_api_client),
            ("组件导入测试", test_widgets_import),
            ("主应用测试", test_main_app)
        ]
        
        passed = 0
        for test_name, test_func in tests:
            self.log(f"运行 {test_name}...")
            try:
                if test_func():
                    self.log(f"✅ {test_name} 通过")
                    passed += 1
                else:
                    self.log(f"❌ {test_name} 失败")
            except Exception as e:
                self.log(f"❌ {test_name} 异常: {e}")
                
        self.log(f"\n基本测试完成: {passed}/{len(tests)} 通过")
        
    def test_api_connection(self):
        """测试API连接"""
        self.log("测试API连接...")
        
        def test_connection():
            try:
                from utils.api_client import APIClient
                from config import Config
                
                client = APIClient(Config.API_BASE_URL)
                connected = client.check_connection()
                
                self.root.after(0, lambda: self.log(f"API连接状态: {'✅ 已连接' if connected else '❌ 未连接'}"))
                
                if connected:
                    # 测试获取设备列表
                    devices = client.get_devices()
                    self.root.after(0, lambda: self.log(f"获取到 {len(devices) if devices else 0} 个设备"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ API连接测试失败: {e}"))
                
        threading.Thread(target=test_connection, daemon=True).start()
        
    def test_device_manager(self):
        """测试设备管理器"""
        self.log("测试设备管理器...")
        
        try:
            from widgets.device_manager import DeviceManagerFrame
            from utils.api_client import APIClient
            from config import Config
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("设备管理器测试")
            test_window.geometry("800x600")
            
            # 创建设备管理器
            api_client = APIClient(Config.API_BASE_URL)
            device_manager = DeviceManagerFrame(test_window, api_client)
            device_manager.pack(fill=tk.BOTH, expand=True)
            
            self.log("✅ 设备管理器创建成功")
            
        except Exception as e:
            self.log(f"❌ 设备管理器测试失败: {e}")
            
    def test_task_manager(self):
        """测试任务管理器"""
        self.log("测试任务管理器...")
        
        try:
            from widgets.task_manager import TaskManagerFrame
            from utils.api_client import APIClient
            from config import Config
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("任务管理器测试")
            test_window.geometry("800x600")
            
            # 创建任务管理器
            api_client = APIClient(Config.API_BASE_URL)
            task_manager = TaskManagerFrame(test_window, api_client)
            task_manager.pack(fill=tk.BOTH, expand=True)
            
            self.log("✅ 任务管理器创建成功")
            
        except Exception as e:
            self.log(f"❌ 任务管理器测试失败: {e}")
            
    def run(self):
        """运行测试界面"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动前端测试...")
    
    # 运行控制台测试
    print("\n=== 控制台测试 ===")
    console_tests = [
        ("基本导入", test_basic_imports),
        ("配置导入", test_config_import),
        ("API客户端", test_api_client),
        ("组件导入", test_widgets_import),
        ("主应用", test_main_app)
    ]
    
    passed = 0
    for test_name, test_func in console_tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            
    print(f"\n控制台测试完成: {passed}/{len(console_tests)} 通过")
    
    # 启动GUI测试
    if passed > 0:
        print("\n启动GUI测试界面...")
        try:
            test_gui = TestGUI()
            test_gui.run()
        except Exception as e:
            print(f"GUI测试失败: {e}")
    else:
        print("控制台测试失败过多，跳过GUI测试")

if __name__ == "__main__":
    main()
