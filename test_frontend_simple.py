#!/usr/bin/env python3
"""
简单的前端功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.api_client import APIClient
from config import Config

def test_api_client():
    """测试API客户端功能"""
    print("🧪 测试API客户端功能...")
    print("="*50)
    
    # 创建API客户端
    api_client = APIClient(Config.API_BASE_URL)
    
    # 1. 测试连接
    print("1. 测试API连接...")
    connected = api_client.check_connection()
    print(f"   连接状态: {'✅ 已连接' if connected else '❌ 未连接'}")
    
    if not connected:
        print("❌ API连接失败，请检查后端服务器")
        return
    
    # 2. 测试获取设备
    print("\n2. 测试获取设备...")
    devices = api_client.get_devices()
    print(f"   设备数量: {len(devices) if devices else 0}")
    
    # 3. 测试获取分组
    print("\n3. 测试获取分组...")
    groups = api_client.get_groups()
    print(f"   分组数量: {len(groups) if groups else 0}")
    
    if groups:
        print("   分组列表:")
        for group in groups:
            print(f"     ID: {group.get('id')}, 名称: {group.get('group_name')}")
    
    # 4. 测试创建分组
    print("\n4. 测试创建分组...")
    group_data = {
        "group_name": "API测试分组",
        "description": "通过API客户端创建的测试分组"
    }
    
    new_group = api_client.create_group(group_data)
    if new_group:
        print(f"   ✅ 分组创建成功:")
        print(f"     ID: {new_group.get('id')}")
        print(f"     名称: {new_group.get('group_name')}")
        print(f"     描述: {new_group.get('description')}")
    else:
        print("   ❌ 分组创建失败")
    
    # 5. 测试获取任务
    print("\n5. 测试获取任务...")
    tasks = api_client.get_tasks()
    print(f"   任务数量: {len(tasks) if tasks else 0}")
    
    if tasks:
        # 统计任务类型
        task_types = {}
        for task in tasks:
            task_type = task.get('task_type')
            task_types[task_type] = task_types.get(task_type, 0) + 1
        
        print("   任务类型统计:")
        for task_type, count in task_types.items():
            print(f"     {task_type}: {count} 个")
    
    # 6. 测试创建inex任务
    print("\n6. 测试创建inex任务...")
    task_data = {
        "task_type": "inex",
        "parameters": {
            "user_id": "api_test_user",
            "count": 1
        },
        "target_scope": "single",
        "target_id": 1,
        "delay_group": 1000,
        "delay_like": 500
    }
    
    new_task = api_client.create_task(task_data)
    if new_task:
        print(f"   ✅ inex任务创建成功:")
        print(f"     ID: {new_task.get('id')}")
        print(f"     类型: {new_task.get('task_type')}")
        print(f"     参数: {new_task.get('parameters')}")
        print(f"     状态: {new_task.get('status')}")
    else:
        print("   ❌ inex任务创建失败")
    
    # 7. 测试任务操作
    if new_task and new_task.get('id'):
        task_id = new_task.get('id')
        print(f"\n7. 测试任务操作 (任务ID: {task_id})...")
        
        # 获取任务详情
        task_detail = api_client.get_task(task_id)
        if task_detail:
            print(f"   ✅ 获取任务详情成功")
        
        # 获取任务状态
        task_status = api_client.get_task_status(task_id)
        if task_status:
            print(f"   ✅ 获取任务状态成功: {task_status.get('status')}")
    
    print("\n" + "="*50)
    print("🎯 API客户端测试完成")
    
    # 总结
    success_count = 0
    total_tests = 7
    
    if connected: success_count += 1
    if devices: success_count += 1
    if groups: success_count += 1
    if new_group: success_count += 1
    if tasks: success_count += 1
    if new_task: success_count += 1
    if new_task and api_client.get_task(new_task.get('id')): success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有API功能正常！")
        print("\n💡 前端功能状态:")
        print("✅ 设备管理: 可以获取设备列表和详情")
        print("✅ 分组管理: 可以创建、获取分组")
        print("✅ 任务管理: 可以创建、获取任务（包括inex）")
        print("✅ 任务操作: 可以查看任务详情和状态")
        
        print("\n🚀 前端界面功能:")
        print("1. 任务创建: 支持所有任务类型包括inex（主页关注）")
        print("2. 分组管理: 显示分组名称和设备数量")
        print("3. 设备管理: 显示设备状态和信息")
        print("4. 实时监控: WebSocket连接状态")
    else:
        print("⚠️ 部分功能可能存在问题")

def main():
    """主函数"""
    test_api_client()

if __name__ == "__main__":
    main()
