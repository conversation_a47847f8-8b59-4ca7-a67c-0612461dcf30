#!/usr/bin/env python3
"""
设备管理系统 - Tkinter前端
基于FastAPI后端的设备任务管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import threading
import time
from datetime import datetime
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from widgets.device_manager import DeviceManagerFrame
from widgets.new_task_manager import NewTaskManagerFrame
from widgets.task_viewer import TaskViewerFrame
from widgets.group_manager import GroupManagerFrame
from widgets.websocket_monitor import WebSocketMonitorFrame
from widgets.status_bar import StatusBar
from utils.api_client import APIClient

class MainApplication:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("设备管理系统 v1.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化API客户端
        self.api_client = APIClient(Config.get_api_base_url())
        
        # 创建主界面
        self.create_main_interface()
        
        # 启动后台任务
        self.start_background_tasks()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 9))
        
        # 配置Treeview样式
        style.configure('Treeview', rowheight=25)
        style.configure('Treeview.Heading', font=('Arial', 9, 'bold'))
        
    def create_main_interface(self):
        """创建主界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出设备列表", command=self.export_devices)
        file_menu.add_command(label="导出任务报告", command=self.export_tasks)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="服务器配置", command=self.show_server_config)
        settings_menu.add_command(label="刷新间隔", command=self.show_refresh_config)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 刷新按钮
        ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_all).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 快速操作按钮
        ttk.Button(toolbar, text="➕ 新建任务", command=self.quick_create_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="⏸️ 暂停所有", command=self.pause_all_tasks).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="▶️ 恢复所有", command=self.resume_all_tasks).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="⚙️ 设置", command=self.show_settings).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 连接状态指示器
        self.connection_status = ttk.Label(toolbar, text="🔴 未连接", style='Status.TLabel')
        self.connection_status.pack(side=tk.RIGHT, padx=5)
        
        # 在线设备数量
        self.online_devices_label = ttk.Label(toolbar, text="在线设备: 0", style='Status.TLabel')
        self.online_devices_label.pack(side=tk.RIGHT, padx=5)
        
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建主要的Notebook控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 设备管理页面
        self.device_frame = DeviceManagerFrame(self.notebook, self.api_client)
        self.notebook.add(self.device_frame, text="📱 设备管理")
        
        # 任务管理页面
        self.task_frame = NewTaskManagerFrame(self.notebook, self.api_client)
        self.notebook.add(self.task_frame, text="📋 任务管理")

        # 任务查看页面
        self.task_viewer_frame = TaskViewerFrame(self.notebook, self.api_client)
        self.notebook.add(self.task_viewer_frame, text="🔍 任务查看")

        # 分组管理页面
        self.group_frame = GroupManagerFrame(self.notebook, self.api_client)
        self.notebook.add(self.group_frame, text="👥 分组管理")
        
        # WebSocket监控页面
        self.websocket_frame = WebSocketMonitorFrame(self.notebook, self.api_client)
        self.notebook.add(self.websocket_frame, text="🔗 连接监控")
        
        # 绑定页面切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = StatusBar(self.root, self.api_client)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def start_background_tasks(self):
        """启动后台任务"""
        # 启动定时刷新任务
        self.start_auto_refresh()
        
        # 启动连接状态检查
        self.start_connection_check()
        
    def start_auto_refresh(self):
        """启动自动刷新"""
        def auto_refresh():
            while True:
                try:
                    # 每5秒刷新一次数据
                    time.sleep(5)
                    self.root.after(0, self.refresh_current_tab)
                except Exception as e:
                    print(f"自动刷新异常: {e}")
                    
        thread = threading.Thread(target=auto_refresh, daemon=True)
        thread.start()
        
    def start_connection_check(self):
        """启动连接状态检查"""
        def check_connection():
            while True:
                try:
                    # 检查API连接状态
                    if self.api_client.check_connection():
                        self.root.after(0, lambda: self.connection_status.config(text="🟢 已连接"))
                    else:
                        self.root.after(0, lambda: self.connection_status.config(text="🔴 未连接"))
                        
                    # 更新在线设备数量
                    online_count = self.api_client.get_online_device_count()
                    self.root.after(0, lambda: self.online_devices_label.config(text=f"在线设备: {online_count}"))
                    
                except Exception as e:
                    print(f"连接检查异常: {e}")
                    self.root.after(0, lambda: self.connection_status.config(text="🔴 连接异常"))
                    
                time.sleep(3)
                
        thread = threading.Thread(target=check_connection, daemon=True)
        thread.start()
        
    def on_tab_changed(self, event):
        """页面切换事件处理"""
        selected_tab = event.widget.tab('current')['text']
        print(f"切换到页面: {selected_tab}")
        
        # 刷新当前页面数据
        self.refresh_current_tab()
        
    def refresh_current_tab(self):
        """刷新当前页面"""
        try:
            current_tab = self.notebook.index(self.notebook.select())

            if current_tab == 0:  # 设备管理
                self.device_frame.refresh()
            elif current_tab == 1:  # 任务管理
                self.task_frame.refresh()
            elif current_tab == 2:  # 任务查看
                self.task_viewer_frame.refresh()
            elif current_tab == 3:  # 分组管理
                self.group_frame.refresh()
            elif current_tab == 4:  # WebSocket监控
                self.websocket_frame.refresh()

        except Exception as e:
            print(f"刷新页面异常: {e}")
            
    def refresh_all(self):
        """刷新所有页面"""
        try:
            self.device_frame.refresh()
            self.task_frame.refresh()
            self.task_viewer_frame.refresh()
            self.group_frame.refresh()
            self.websocket_frame.refresh()
            self.status_bar.refresh()

            messagebox.showinfo("刷新完成", "所有数据已刷新")

        except Exception as e:
            messagebox.showerror("刷新失败", f"刷新数据时发生错误: {e}")
            
    def quick_create_task(self):
        """快速创建任务"""
        self.notebook.select(1)  # 切换到任务管理页面
        self.task_frame.show_create_task_dialog()
        
    def pause_all_tasks(self):
        """暂停所有任务"""
        if messagebox.askyesno("确认操作", "确定要暂停所有正在运行的任务吗？"):
            try:
                result = self.api_client.pause_all_tasks()
                if result:
                    messagebox.showinfo("操作成功", "所有任务已暂停")
                    self.refresh_current_tab()
                else:
                    messagebox.showerror("操作失败", "暂停任务失败")
            except Exception as e:
                messagebox.showerror("操作异常", f"暂停任务时发生错误: {e}")
                
    def resume_all_tasks(self):
        """恢复所有任务"""
        if messagebox.askyesno("确认操作", "确定要恢复所有暂停的任务吗？"):
            try:
                result = self.api_client.resume_all_tasks()
                if result:
                    messagebox.showinfo("操作成功", "所有任务已恢复")
                    self.refresh_current_tab()
                else:
                    messagebox.showerror("操作失败", "恢复任务失败")
            except Exception as e:
                messagebox.showerror("操作异常", f"恢复任务时发生错误: {e}")
                
    def export_devices(self):
        """导出设备列表"""
        # TODO: 实现设备列表导出功能
        messagebox.showinfo("功能开发中", "设备列表导出功能正在开发中")
        
    def export_tasks(self):
        """导出任务报告"""
        # TODO: 实现任务报告导出功能
        messagebox.showinfo("功能开发中", "任务报告导出功能正在开发中")
        
    def show_settings(self):
        """显示设置界面"""
        try:
            from widgets.settings_dialog import SettingsDialog
            dialog = SettingsDialog(self.root)
            if dialog.show():
                # 配置已更改，提示重启
                if messagebox.askyesno("重启应用", "配置已更改，是否立即重启应用以应用新配置？"):
                    self.restart_application()
        except Exception as e:
            messagebox.showerror("设置错误", f"打开设置界面时发生错误: {e}")

    def restart_application(self):
        """重启应用程序"""
        try:
            import sys
            import os

            # 保存当前状态
            self.root.quit()

            # 重启程序
            python = sys.executable
            os.execl(python, python, *sys.argv)
        except Exception as e:
            messagebox.showerror("重启失败", f"重启应用程序失败: {e}")

    def show_server_config(self):
        """显示服务器配置对话框（兼容性方法）"""
        self.show_settings()
        
    def show_refresh_config(self):
        """显示刷新间隔配置对话框"""
        # TODO: 实现刷新间隔配置对话框
        messagebox.showinfo("功能开发中", "刷新间隔配置功能正在开发中")
        
    def show_help(self):
        """显示使用说明"""
        help_text = """
设备管理系统使用说明

1. 设备管理：
   - 查看所有设备的在线状态
   - 监控设备心跳和连接状态
   - 管理设备分组

2. 任务管理：
   - 创建签到、点赞等任务
   - 监控任务执行状态
   - 暂停/恢复任务

3. 分组管理：
   - 创建设备分组
   - 批量分发任务到组

4. 连接监控：
   - 实时监控WebSocket连接
   - 查看心跳状态
        """
        messagebox.showinfo("使用说明", help_text)
        
    def show_about(self):
        """显示关于信息"""
        about_text = """
设备管理系统 v1.0

基于FastAPI + Tkinter开发的设备任务管理系统

功能特点：
- 实时设备状态监控
- 任务批量分发和管理
- WebSocket连接监控
- 设备分组管理

开发时间：2025年6月
        """
        messagebox.showinfo("关于", about_text)
        
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            print(f"程序运行异常: {e}")
            messagebox.showerror("程序异常", f"程序运行时发生错误: {e}")

def main():
    """主函数"""
    try:
        app = MainApplication()
        app.run()
    except Exception as e:
        print(f"启动应用程序失败: {e}")
        messagebox.showerror("启动失败", f"启动应用程序时发生错误: {e}")

if __name__ == "__main__":
    main()
