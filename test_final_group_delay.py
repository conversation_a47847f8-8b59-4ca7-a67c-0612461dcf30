#!/usr/bin/env python3
"""
最终分组延迟测试
验证所有问题都已解决
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class FinalTestDevice:
    """最终测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []
        self.task_counts = {}
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                task_id = data.get('task_id')
                
                # 统计任务接收次数
                if task_id in self.task_counts:
                    self.task_counts[task_id] += 1
                    log_with_time(f"⚠️ 设备 {self.device_number} 重复收到任务 {task_id} (第{self.task_counts[task_id]}次)")
                else:
                    self.task_counts[task_id] = 1
                    log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id}")
                
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                pass
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_final_test_task(task_name, group_id, delay_group_sec=4):
    """创建最终测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": f"final_test_{int(time.time())}"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 秒转毫秒
        "delay_like": 500  # 0.5秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_final_group_delay():
    """最终分组延迟测试"""
    log_with_time("=== 最终分组延迟测试 ===")
    
    # 创建测试设备
    device = FinalTestDevice("ceshi212")
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 2. 创建测试任务
        delay_seconds = 4  # 4秒分组延迟
        log_with_time(f"🚀 创建最终测试任务（{delay_seconds}秒延迟）")
        
        test_start_time = time.time()
        
        # 创建3个任务
        tasks = []
        for i in range(3):
            task_name = f"最终测试{i+1}"
            task = create_final_test_task(task_name, 5, delay_seconds)
            if task:
                tasks.append(task)
            await asyncio.sleep(0.5)  # 任务创建间隔0.5秒
        
        if len(tasks) < 3:
            log_with_time("❌ 任务创建不完整，测试终止")
            return
        
        # 3. 等待任务执行
        expected_time = len(tasks) * (1 + delay_seconds)  # 每个任务1秒执行 + 4秒延迟
        log_with_time(f"⏳ 等待任务执行（预计需要约{expected_time}秒）...")
        await asyncio.sleep(expected_time + 10)  # 额外等待10秒
        
        # 4. 分析结果
        log_with_time("📊 最终测试结果分析...")
        
        # 检查重复任务
        duplicate_tasks = []
        for task_id, count in device.task_counts.items():
            if count > 1:
                duplicate_tasks.append((task_id, count))
        
        if duplicate_tasks:
            log_with_time("❌ 发现重复任务:")
            for task_id, count in duplicate_tasks:
                log_with_time(f"   任务{task_id}: 收到{count}次")
        else:
            log_with_time("✅ 没有重复任务")
        
        # 检查分组延迟
        if len(device.task_times) >= 2:
            log_with_time("分组延迟效果分析:")
            
            intervals = []
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                intervals.append(interval)
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期
                expected_min = delay_seconds + 0.5  # 4 + 0.5 = 4.5秒
                expected_max = delay_seconds + 2.0  # 4 + 2.0 = 6秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常")
            
            # 总体分析
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                log_with_time(f"📊 平均间隔: {avg_interval:.1f}秒")
                
                if delay_seconds + 0.5 <= avg_interval <= delay_seconds + 2.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                else:
                    log_with_time("⚠️ 分组延迟功能可能有问题")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: {len(tasks)}")
        
        # 最终评估
        success_criteria = [
            len(duplicate_tasks) == 0,  # 没有重复任务
            len(device.received_tasks) >= 2,  # 至少收到2个任务
            len(intervals) > 0 and all(delay_seconds + 0.5 <= i <= delay_seconds + 2.0 for i in intervals)  # 分组延迟正常
        ]
        
        if all(success_criteria):
            log_with_time("🎉 最终测试完全通过！")
            log_with_time("✅ 重复发送问题已解决")
            log_with_time("✅ 分组延迟功能正常")
            log_with_time("✅ 后续任务可以正常收到")
        else:
            log_with_time("⚠️ 最终测试部分失败")
            if duplicate_tasks:
                log_with_time("❌ 仍有重复发送问题")
            if len(device.received_tasks) < 2:
                log_with_time("❌ 后续任务收不到")
            if not (len(intervals) > 0 and all(delay_seconds + 0.5 <= i <= delay_seconds + 2.0 for i in intervals)):
                log_with_time("❌ 分组延迟功能异常")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 最终分组延迟测试")
    print("💡 验证所有问题都已解决")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 50)
    print("🚀 开始最终测试")
    print("=" * 50)
    
    # 最终测试
    await test_final_group_delay()
    
    print("\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 验证要点:")
    print("   1. ✅ 不重复发送同一任务给同一设备")
    print("   2. ✅ 后续任务可以正常收到")
    print("   3. ✅ 分组延迟功能正常工作")
    print("   4. ✅ 任务按顺序执行，有延迟间隔")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
