#!/usr/bin/env python3
"""
测试时间优化和任务日志功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import asyncio
import websockets
import json
from datetime import datetime

def test_backend_time():
    """测试后端时间显示"""
    print("🕐 测试后端时间显示...")
    
    try:
        # 测试设备API时间格式
        response = requests.get("http://localhost:8002/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            if devices:
                device = devices[0]
                last_heartbeat = device.get('last_heartbeat')
                print(f"✅ 设备心跳时间: {last_heartbeat}")
                
                # 检查时间格式
                if last_heartbeat:
                    try:
                        dt = datetime.fromisoformat(last_heartbeat.replace('Z', '+00:00'))
                        print(f"   解析后时间: {dt}")
                        print(f"   时区信息: {dt.tzinfo}")
                    except Exception as e:
                        print(f"   时间解析失败: {e}")
                        
                return True
            else:
                print("❌ 没有设备数据")
                return False
        else:
            print(f"❌ 设备API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 后端时间测试异常: {e}")
        return False

def test_task_device_logs_api():
    """测试任务设备日志API"""
    print("\n📋 测试任务设备日志API...")
    
    try:
        # 先获取任务列表
        response = requests.get("http://localhost:8002/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            if tasks:
                task_id = tasks[0].get('id')
                print(f"   测试任务ID: {task_id}")
                
                # 测试任务设备日志API
                response = requests.get(f"http://localhost:8002/task-sync/task-device-logs/{task_id}", timeout=10)
                if response.status_code == 200:
                    logs = response.json()
                    print(f"✅ 任务设备日志API正常，返回 {len(logs)} 条日志")
                    
                    if logs:
                        log = logs[0]
                        print(f"   示例日志: {log.get('device_number')} - {log.get('status')}")
                        print(f"   完成时间: {log.get('finish_time')}")
                        print(f"   结果摘要: {log.get('result_summary')}")
                    
                    return True
                else:
                    print(f"❌ 任务设备日志API失败: {response.status_code}")
                    return False
            else:
                print("❌ 没有任务数据")
                return False
        else:
            print(f"❌ 任务API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务设备日志API测试异常: {e}")
        return False

async def test_websocket_beijing_time():
    """测试WebSocket北京时间"""
    print("\n🔗 测试WebSocket北京时间...")
    
    try:
        uri = "ws://localhost:8002/ws/time_test_device"
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送心跳并观察服务器时间处理
            heartbeat_msg = {
                "type": "heartbeat",
                "timestamp": datetime.now().isoformat(),
                "device_number": "time_test_device"
            }
            
            await websocket.send(json.dumps(heartbeat_msg))
            print(f"→ 发送心跳: {heartbeat_msg['timestamp']}")
            
            # 等待一下让服务器处理
            await asyncio.sleep(2)
            
            print("✅ WebSocket北京时间测试完成")
            return True
            
    except Exception as e:
        print(f"❌ WebSocket北京时间测试异常: {e}")
        return False

def test_frontend_time_utils():
    """测试前端时间工具"""
    print("\n🖥️ 测试前端时间工具...")
    
    try:
        # 导入前端时间工具
        sys.path.append('frontend')
        from utils.time_utils import utc_to_beijing, get_relative_time, beijing_timestamp
        
        # 测试UTC转北京时间
        utc_time = "2025-06-07T05:24:51.726Z"
        beijing_time = utc_to_beijing(utc_time)
        print(f"✅ UTC转北京时间: {utc_time} -> {beijing_time}")
        
        # 测试相对时间
        relative_time = get_relative_time(utc_time)
        print(f"✅ 相对时间: {relative_time}")
        
        # 测试北京时间戳
        timestamp = beijing_timestamp()
        print(f"✅ 北京时间戳: {timestamp}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端时间工具测试异常: {e}")
        return False

def test_time_consistency():
    """测试时间一致性"""
    print("\n⏰ 测试时间一致性...")
    
    try:
        # 测试后端时间工具
        sys.path.append('app')
        from utils.time_utils import get_beijing_now, beijing_timestamp as backend_timestamp
        
        backend_time = get_beijing_now()
        backend_ts = backend_timestamp()
        
        print(f"✅ 后端北京时间: {backend_time}")
        print(f"✅ 后端时间戳: {backend_ts}")
        
        # 测试前端时间工具
        sys.path.append('frontend')
        from utils.time_utils import beijing_timestamp as frontend_timestamp
        
        frontend_ts = frontend_timestamp()
        print(f"✅ 前端时间戳: {frontend_ts}")
        
        # 比较时间差
        from datetime import datetime
        backend_dt = datetime.strptime(backend_ts, '%Y-%m-%d %H:%M:%S')
        frontend_dt = datetime.strptime(frontend_ts, '%Y-%m-%d %H:%M:%S')
        
        diff = abs((backend_dt - frontend_dt).total_seconds())
        if diff <= 2:  # 允许2秒误差
            print(f"✅ 前后端时间一致，差异: {diff}秒")
            return True
        else:
            print(f"⚠️ 前后端时间差异较大: {diff}秒")
            return False
            
    except Exception as e:
        print(f"❌ 时间一致性测试异常: {e}")
        return False

async def run_websocket_test():
    """运行WebSocket测试"""
    return await test_websocket_beijing_time()

def main():
    """主函数"""
    print("🧪 时间优化和任务日志功能测试")
    print("="*60)
    
    tests = [
        ("后端时间显示", test_backend_time),
        ("任务设备日志API", test_task_device_logs_api),
        ("前端时间工具", test_frontend_time_utils),
        ("时间一致性", test_time_consistency)
    ]
    
    passed_tests = 0
    
    # 运行同步测试
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    # 运行WebSocket测试
    try:
        websocket_result = asyncio.run(run_websocket_test())
        if websocket_result:
            passed_tests += 1
            print("✅ WebSocket北京时间 测试通过\n")
        else:
            print("❌ WebSocket北京时间 测试失败\n")
    except Exception as e:
        print(f"❌ WebSocket北京时间 测试异常: {e}\n")
    
    total_tests = len(tests) + 1
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！时间优化成功！")
        
        print("\n✅ 优化总结:")
        print("1. 所有时间显示统一为北京时间")
        print("2. 前后端时间工具统一")
        print("3. WebSocket心跳使用北京时间")
        print("4. 任务设备日志API正常工作")
        print("5. 前端任务管理页面支持设备日志")
        
        print("\n💡 功能说明:")
        print("1. 设备管理页面 - 显示北京时间的心跳时间")
        print("2. 任务管理页面 - 显示北京时间的创建时间")
        print("3. 任务管理页面 - 新增设备任务完成日志框")
        print("4. 日志显示 - 设备执行结果和完成时间")
        print("5. 时间格式 - 统一使用 YYYY-MM-DD HH:MM:SS 格式")
        
        print("\n🔧 使用方法:")
        print("1. 重启前端应用以应用时间优化")
        print("2. 在任务管理页面选择任务查看设备日志")
        print("3. 设备日志显示执行状态和结果摘要")
        print("4. 支持刷新、清空、导出日志功能")
        
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        
        print("\n🔍 故障排除:")
        print("1. 确保后端服务器正在运行")
        print("2. 检查时间工具模块导入")
        print("3. 验证API端点是否正确")
        print("4. 查看后端日志中的错误信息")

if __name__ == "__main__":
    main()
