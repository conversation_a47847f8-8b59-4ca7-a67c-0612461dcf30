#!/usr/bin/env python3
"""
测试任务创建优化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_task_types():
    """测试任务类型配置"""
    print("🎯 测试任务类型配置...")
    
    try:
        # 测试前端配置
        sys.path.append('frontend')
        from config import Config
        
        task_types = Config.TASK_TYPES
        expected_types = {
            "sign": "签到任务",
            "like": "点赞任务", 
            "page_sign": "超话签到任务",
            "inex": "主页关注任务"
        }
        
        if task_types == expected_types:
            print("✅ 任务类型配置正确")
            print(f"   支持的任务类型: {list(task_types.values())}")
            return True
        else:
            print("❌ 任务类型配置不匹配")
            print(f"   期望: {expected_types}")
            print(f"   实际: {task_types}")
            return False
            
    except Exception as e:
        print(f"❌ 任务类型配置测试异常: {e}")
        return False

def test_quick_task_creation():
    """测试快速任务创建API"""
    print("\n🚀 测试快速任务创建API...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试各种任务类型的创建
        test_tasks = [
            {
                "name": "签到任务",
                "data": {
                    "task_type": "sign",
                    "parameters": {"count": 1},
                    "target_scope": "single",
                    "target_id": 1,
                    "delay_group": 2000,
                    "delay_like": 1000
                }
            },
            {
                "name": "点赞任务", 
                "data": {
                    "task_type": "like",
                    "parameters": {
                        "blogger_id": "test_blogger_123",
                        "like_id": "test_like_456"
                    },
                    "target_scope": "single",
                    "target_id": 1,
                    "delay_group": 2000,
                    "delay_like": 1000
                }
            },
            {
                "name": "超话签到任务",
                "data": {
                    "task_type": "page_sign",
                    "parameters": {
                        "page_url": "https://weibo.com/p/test_topic",
                        "blogger_id": "test_blogger_789",
                        "like_id": "test_like_101"
                    },
                    "target_scope": "single", 
                    "target_id": 1,
                    "delay_group": 2000,
                    "delay_like": 1000
                }
            },
            {
                "name": "主页关注任务",
                "data": {
                    "task_type": "inex",
                    "parameters": {
                        "user_id": "test_user_456",
                        "count": 1
                    },
                    "target_scope": "single",
                    "target_id": 1,
                    "delay_group": 2000,
                    "delay_like": 1000
                }
            }
        ]
        
        success_count = 0
        for task in test_tasks:
            try:
                response = requests.post(f"{base_url}/tasks/", json=task["data"], timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ {task['name']} 创建成功 (ID: {result.get('id')})")
                    success_count += 1
                else:
                    print(f"❌ {task['name']} 创建失败: {response.status_code}")
            except Exception as e:
                print(f"❌ {task['name']} 创建异常: {e}")
        
        return success_count == len(test_tasks)
        
    except Exception as e:
        print(f"❌ 快速任务创建测试异常: {e}")
        return False

def test_group_selection():
    """测试分组选择功能"""
    print("\n👥 测试分组选择功能...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取分组列表
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            print(f"✅ 获取到 {len(groups)} 个分组")
            
            if groups:
                for group in groups[:3]:  # 显示前3个分组
                    group_name = group.get('group_name', '未知分组')
                    group_id = group.get('id')
                    print(f"   分组: {group_name} (ID: {group_id})")
                
                # 测试为分组创建任务
                test_group = groups[0]
                task_data = {
                    "task_type": "like",
                    "parameters": {
                        "blogger_id": "group_test_blogger",
                        "like_id": "group_test_like"
                    },
                    "target_scope": "group",
                    "target_id": test_group.get('id'),
                    "delay_group": 3000,
                    "delay_like": 1500
                }
                
                response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 分组任务创建成功 (ID: {result.get('id')})")
                    return True
                else:
                    print(f"❌ 分组任务创建失败: {response.status_code}")
                    return False
            else:
                print("⚠️ 没有可用的分组")
                return True  # 没有分组不算失败
        else:
            print(f"❌ 获取分组失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 分组选择测试异常: {e}")
        return False

def test_settings_persistence():
    """测试设置持久化功能"""
    print("\n💾 测试设置持久化功能...")
    
    try:
        # 创建测试设置
        test_settings = {
            "task_type": "超话签到任务",
            "target_scope": "设备分组",
            "blogger_id": "persistence_test_blogger",
            "like_id": "persistence_test_like",
            "delay_group": "5",
            "delay_like": "2.5"
        }
        
        # 确保设置目录存在
        settings_dir = "frontend/settings"
        if not os.path.exists(settings_dir):
            os.makedirs(settings_dir)
            print("✅ 设置目录已创建")
        
        # 保存设置
        settings_file = os.path.join(settings_dir, "quick_task_settings.json")
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✅ 测试设置已保存")
        
        # 加载设置
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        if loaded_settings == test_settings:
            print("✅ 设置持久化功能正常")
            print(f"   保存的设置: {loaded_settings}")
            return True
        else:
            print("❌ 设置持久化不一致")
            return False
            
    except Exception as e:
        print(f"❌ 设置持久化测试异常: {e}")
        return False

def test_backend_apis():
    """测试后端API支持"""
    print("\n🔧 测试后端API支持...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试基础API
        apis = [
            ("/devices/", "设备API"),
            ("/groups/", "分组API"),
            ("/tasks/", "任务API")
        ]
        
        success_count = 0
        for endpoint, name in apis:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {name} 正常 (返回 {len(data)} 条记录)")
                    success_count += 1
                else:
                    print(f"❌ {name} 失败: {response.status_code}")
            except Exception as e:
                print(f"❌ {name} 异常: {e}")
        
        return success_count == len(apis)
        
    except Exception as e:
        print(f"❌ 后端API测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 任务创建优化功能测试")
    print("="*60)
    
    tests = [
        ("任务类型配置", test_task_types),
        ("快速任务创建", test_quick_task_creation),
        ("分组选择功能", test_group_selection),
        ("设置持久化", test_settings_persistence),
        ("后端API支持", test_backend_apis)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 所有测试通过！任务创建优化成功！")
        
        print("\n✅ 优化功能总结:")
        print("1. ✨ 简化任务创建 - 直接在页面顶部，无需弹窗")
        print("2. 🎯 统一参数输入 - 博主ID和点赞ID适用所有任务")
        print("3. 📋 多分组选择 - 支持勾选多个分组批量创建")
        print("4. 💾 设置持久化 - 参数自动保存和加载")
        print("5. 🔄 自动刷新 - 分组管理操作后自动刷新")
        print("6. 📝 超话签到 - 新增超话签到任务类型")
        
        print("\n🚀 使用指南:")
        print("1. 任务类型选择:")
        print("   • 签到任务 - 普通签到")
        print("   • 点赞任务 - 微博点赞")
        print("   • 超话签到任务 - 超话页面签到")
        print("   • 主页关注任务 - 用户主页关注")
        
        print("\n2. 快速创建流程:")
        print("   • 选择任务类型和目标范围")
        print("   • 输入博主ID和点赞ID")
        print("   • 选择目标设备或分组")
        print("   • 设置延迟参数")
        print("   • 点击创建任务")
        
        print("\n3. 批量操作:")
        print("   • 选择'设备分组'")
        print("   • 勾选多个分组")
        print("   • 使用'全选'/'全不选'快速操作")
        print("   • 一键为所有选中分组创建任务")
        
        print("\n4. 设置管理:")
        print("   • 点击'保存设置'保存当前配置")
        print("   • 下次打开自动加载保存的设置")
        print("   • 支持博主ID、点赞ID、延迟等参数记忆")
        
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
