# 客户端任务处理指南

## 🎯 问题分析

从服务端日志可以看出：
1. ✅ **任务发送成功**：`[WebSocket] 发送任务到设备 devi201: {'task_id': 2, 'type': 'sign', 'parameters': {'count': 9}}`
2. ✅ **心跳正常**：设备在正常发送心跳
3. ❌ **缺少任务完成响应**：没有收到 `task_completion` 消息

## 📨 服务端发送的任务格式

```json
{
    "task_id": 2,
    "type": "sign",
    "parameters": {
        "count": 9
    }
}
```

## 📤 客户端需要返回的格式

```json
{
    "type": "task_completion",
    "task_id": 2,
    "device_id": 4,
    "status": "success",
    "timestamp": "2025-06-06T05:25:34.000Z",
    "result": {
        "processed": 9,
        "success_count": 9,
        "failed_count": 0
    }
}
```

## 🔧 客户端实现建议

### 1. **消息接收处理**

```javascript
ws.onmessage = function(event) {
    try {
        const data = JSON.parse(event.data);
        console.log("收到服务器消息:", data);
        
        // 检查是否是任务消息
        if (data.task_id && data.type && data.parameters) {
            handleTask(data);
        }
        
    } catch (error) {
        console.error("消息解析失败:", error);
    }
};
```

### 2. **任务处理函数**

```javascript
async function handleTask(taskData) {
    console.log("开始处理任务:", taskData);
    
    const { task_id, type, parameters } = taskData;
    
    try {
        let result = {};
        
        // 根据任务类型执行不同的操作
        switch(type) {
            case 'sign':
                result = await executeSignTask(parameters);
                break;
            case 'like':
                result = await executeLikeTask(parameters);
                break;
            case 'page_sign':
                result = await executePageSignTask(parameters);
                break;
            default:
                throw new Error(`未知任务类型: ${type}`);
        }
        
        // 发送成功完成消息
        sendTaskCompletion(task_id, 'success', result);
        
    } catch (error) {
        console.error("任务执行失败:", error);
        
        // 发送失败完成消息
        sendTaskCompletion(task_id, 'failed', {
            error: error.message,
            stack: error.stack
        });
    }
}
```

### 3. **具体任务执行示例**

```javascript
// 签到任务
async function executeSignTask(parameters) {
    console.log("执行签到任务:", parameters);
    
    const { count } = parameters;
    let successCount = 0;
    let failedCount = 0;
    
    for (let i = 0; i < count; i++) {
        try {
            // 这里执行实际的签到操作
            await performSignOperation();
            successCount++;
            
            // 可选：发送进度更新
            sendTaskProgress(task_id, (i + 1) / count * 100);
            
        } catch (error) {
            console.error(`签到 ${i+1} 失败:`, error);
            failedCount++;
        }
        
        // 添加延迟避免过快操作
        await sleep(1000);
    }
    
    return {
        processed: count,
        success_count: successCount,
        failed_count: failedCount,
        completion_time: new Date().toISOString()
    };
}

// 点赞任务
async function executeLikeTask(parameters) {
    console.log("执行点赞任务:", parameters);
    
    const { count, blogger_id, like_id } = parameters;
    let successCount = 0;
    
    for (let i = 0; i < count; i++) {
        try {
            await performLikeOperation(blogger_id, like_id);
            successCount++;
        } catch (error) {
            console.error(`点赞 ${i+1} 失败:`, error);
        }
        
        await sleep(500); // 点赞间隔
    }
    
    return {
        processed: count,
        success_count: successCount,
        blogger_id: blogger_id,
        like_id: like_id
    };
}
```

### 4. **发送任务完成消息**

```javascript
function sendTaskCompletion(taskId, status, result = {}) {
    const completionMessage = {
        type: "task_completion",  // 重要：必须是这个类型
        task_id: taskId,
        device_id: getDeviceId(), // 获取设备ID的函数
        status: status,           // "success" 或 "failed"
        timestamp: new Date().toISOString(),
        result: result
    };
    
    try {
        ws.send(JSON.stringify(completionMessage));
        console.log("任务完成消息已发送:", completionMessage);
    } catch (error) {
        console.error("发送任务完成消息失败:", error);
    }
}

// 获取设备ID的函数（需要根据实际情况实现）
function getDeviceId() {
    // 方法1: 从全局变量获取
    return window.deviceId || 4;
    
    // 方法2: 从本地存储获取
    // return localStorage.getItem('deviceId') || 4;
    
    // 方法3: 从设备号映射获取
    // const deviceMapping = { 'devi201': 4 };
    // return deviceMapping[deviceNumber] || 4;
}
```

### 5. **可选：发送任务进度**

```javascript
function sendTaskProgress(taskId, progress) {
    const progressMessage = {
        type: "task_progress",
        task_id: taskId,
        device_id: getDeviceId(),
        progress: progress, // 0-100
        timestamp: new Date().toISOString()
    };
    
    ws.send(JSON.stringify(progressMessage));
}
```

### 6. **工具函数**

```javascript
// 延迟函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 实际操作函数（需要根据具体业务实现）
async function performSignOperation() {
    // 这里实现具体的签到逻辑
    console.log("执行签到操作...");
    
    // 模拟操作
    await sleep(Math.random() * 1000 + 500);
    
    // 模拟可能的失败
    if (Math.random() < 0.1) {
        throw new Error("签到失败");
    }
    
    return { success: true };
}

async function performLikeOperation(bloggerId, likeId) {
    // 这里实现具体的点赞逻辑
    console.log(`执行点赞操作: blogger=${bloggerId}, like=${likeId}`);
    
    await sleep(Math.random() * 500 + 200);
    
    if (Math.random() < 0.05) {
        throw new Error("点赞失败");
    }
    
    return { success: true };
}
```

## 🚨 关键注意事项

### 1. **必须字段**
- `type`: 必须是 `"task_completion"`
- `task_id`: 必须与接收到的任务ID一致
- `device_id`: 必须是正确的设备ID（很重要！）
- `status`: 必须是 `"success"` 或 `"failed"`

### 2. **设备ID获取**
设备ID是关键，必须正确。可以通过以下方式获取：
```javascript
// 如果服务端在连接时发送了设备信息
let deviceId = null;

ws.onopen = function() {
    // 请求设备信息
    ws.send(JSON.stringify({
        type: "get_device_info",
        device_number: "devi201"
    }));
};

// 或者硬编码映射
const DEVICE_MAPPING = {
    'devi201': 4,
    'devi202': 5,
    // ... 其他设备
};
```

### 3. **错误处理**
```javascript
// 确保即使出错也要发送完成消息
window.addEventListener('error', function(event) {
    if (currentTaskId) {
        sendTaskCompletion(currentTaskId, 'failed', {
            error: event.error.message,
            type: 'uncaught_error'
        });
    }
});
```

## 🧪 测试建议

### 1. **简单测试**
```javascript
// 收到任务后立即返回成功
function quickTest(taskData) {
    setTimeout(() => {
        sendTaskCompletion(taskData.task_id, 'success', {
            test: true,
            message: "快速测试完成"
        });
    }, 1000);
}
```

### 2. **调试日志**
```javascript
// 添加详细日志
console.log("设备ID:", getDeviceId());
console.log("发送完成消息:", completionMessage);
```

实现这些建议后，您应该能看到服务端日志中出现：
```
← 收到设备 devi201 任务完成消息: {...}
✓ 更新任务状态: 任务 2 设备 devi201 状态: done
```
