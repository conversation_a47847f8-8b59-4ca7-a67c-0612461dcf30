#!/usr/bin/env python3
"""
调试WebSocket管理器状态
"""

import requests
import json

def debug_websocket_manager():
    """调试WebSocket管理器状态"""
    print("🔍 调试WebSocket管理器状态...")
    
    # 由于无法直接访问WebSocket管理器的内部状态，
    # 我们需要通过其他方式来检查
    
    try:
        base_url = "http://localhost:8000"
        
        # 1. 检查设备状态
        print("\n📱 检查设备状态:")
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            
            online_devices = [d for d in devices if d.get('online_status') == 'online']
            print(f"   数据库中在线设备: {len(online_devices)}个")
            
            for device in online_devices:
                device_number = device.get('device_number')
                device_id = device.get('id')
                last_heartbeat = device.get('last_heartbeat')
                print(f"     - {device_number} (ID:{device_id}): {last_heartbeat}")
        
        # 2. 检查任务队列状态
        print("\n📋 检查任务队列状态:")
        
        # 检查TaskQueue表中的记录
        # 这里需要一个API来获取TaskQueue的详细信息
        # 暂时通过任务状态API来推断
        
        for device in online_devices:
            device_id = device.get('id')
            device_number = device.get('device_number')
            
            # 检查该设备的任务状态
            response = requests.get(f"{base_url}/tasks/", timeout=10)
            if response.status_code == 200:
                all_tasks = response.json()
                
                # 找到分配给该设备的任务
                device_tasks = [t for t in all_tasks if t.get('target_scope') == 'single' and t.get('target_id') == device_id]
                pending_tasks = [t for t in device_tasks if t.get('status') == 'pending']
                running_tasks = [t for t in device_tasks if t.get('status') == 'running']
                
                print(f"   设备 {device_number} (ID:{device_id}):")
                print(f"     总任务: {len(device_tasks)}个")
                print(f"     pending: {len(pending_tasks)}个")
                print(f"     running: {len(running_tasks)}个")
                
                if pending_tasks:
                    print(f"     最新pending任务: 任务{pending_tasks[-1].get('id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试WebSocket管理器异常: {e}")
        return False

def create_debug_task():
    """创建调试任务并观察分发过程"""
    print("\n🧪 创建调试任务...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取在线设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取设备")
            return False
            
        devices = response.json()
        online_devices = [d for d in devices if d.get('online_status') == 'online']
        
        if not online_devices:
            print("❌ 没有在线设备")
            return False
            
        test_device = online_devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number')
        
        print(f"   使用设备: {device_number} (ID: {device_id})")
        
        # 创建调试任务
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "debug_websocket_test",
                "like_id": "debug_websocket_like",
                "delay_click": 500
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 1000
        }
        
        print("   📤 创建调试任务...")
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            print(f"   ✅ 调试任务创建成功: 任务ID {task_id}")
            
            # 等待几秒钟观察任务状态变化
            import time
            for i in range(5):
                time.sleep(2)
                
                # 检查任务状态
                response = requests.get(f"{base_url}/tasks/{task_id}/status", timeout=10)
                if response.status_code == 200:
                    status_info = response.json()
                    task_status = status_info.get('status')
                    queues = status_info.get('queues', {})
                    
                    print(f"   第{i+1}次检查: 任务状态={task_status}, 队列状态={queues}")
                else:
                    print(f"   第{i+1}次检查失败: {response.status_code}")
            
            return task_id
        else:
            print(f"   ❌ 调试任务创建失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 创建调试任务异常: {e}")
        return None

def check_scheduler_logs():
    """检查调度器日志"""
    print("\n📝 检查调度器日志...")
    
    # 这里我们无法直接访问调度器的内部状态
    # 但可以通过观察系统行为来推断问题
    
    try:
        base_url = "http://localhost:8000"
        
        # 检查超时监控状态
        response = requests.get(f"{base_url}/timeout-monitor/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"   超时监控状态: {status.get('monitor_status')}")
            print(f"   运行中任务: {status.get('running_tasks_count')}个")
        
        # 检查运行中任务详情
        response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
        if response.status_code == 200:
            running_info = response.json()
            tasks = running_info.get('tasks', [])
            print(f"   队列中运行任务: {len(tasks)}个")
            
            if tasks:
                for task in tasks:
                    task_id = task.get('task_id')
                    device_id = task.get('device_id')
                    runtime = task.get('runtime_minutes', 0)
                    print(f"     - 任务{task_id} -> 设备{device_id}: 运行{runtime}分钟")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查调度器日志异常: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 问题分析和解决方案:")
    
    print("\n🔍 可能的问题:")
    print("1. WebSocket连接状态不一致")
    print("   - 数据库显示设备在线")
    print("   - 但WebSocket管理器的active_connections中没有该设备")
    
    print("\n2. 调度器设备检查逻辑问题")
    print("   - _process_device_queue方法检查ws_manager.active_connections")
    print("   - 如果设备不在active_connections中，任务不会被处理")
    
    print("\n3. 心跳机制问题")
    print("   - 设备可能发送了心跳更新数据库状态")
    print("   - 但WebSocket连接可能已经断开")
    
    print("\n🛠️ 解决方案:")
    print("1. 临时解决方案 - 修改调度器逻辑")
    print("   - 不仅检查active_connections")
    print("   - 同时检查数据库中的online_status")
    
    print("\n2. 长期解决方案 - 同步连接状态")
    print("   - 确保WebSocket连接断开时更新数据库状态")
    print("   - 确保数据库状态与active_connections一致")
    
    print("\n3. 调试方案 - 添加详细日志")
    print("   - 在调度器中添加更多调试日志")
    print("   - 记录设备检查和任务分发过程")

def main():
    """主函数"""
    print("🔧 WebSocket管理器调试")
    print("="*50)
    
    # 调试WebSocket管理器
    debug_websocket_manager()
    
    # 创建调试任务
    debug_task_id = create_debug_task()
    
    # 检查调度器日志
    check_scheduler_logs()
    
    # 建议解决方案
    suggest_solutions()
    
    print("\n" + "="*50)
    print("📊 调试总结:")
    
    if debug_task_id:
        print(f"✅ 调试任务 {debug_task_id} 已创建")
        print("   请观察后端日志，查看任务是否被正确分发")
    else:
        print("❌ 调试任务创建失败")
    
    print("\n🔧 下一步操作:")
    print("1. 检查后端日志中的任务分发信息")
    print("2. 确认WebSocket连接状态")
    print("3. 考虑修改调度器的设备检查逻辑")

if __name__ == "__main__":
    main()
