#!/usr/bin/env python3
"""
最终测试：验证修复后的分组延迟功能
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_group_task(task_name, group_id, delay_group_sec, delay_like_sec=1):
    """创建分组任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1,
            "test_name": task_name,
            "like_id": "test_like_123"  # 添加必需的like_id参数
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def monitor_task_completion(task_id, task_name, max_wait=30):
    """监控任务直到完成"""
    log_with_time(f"👀 监控 {task_name} (ID: {task_id})")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status != last_status:
                    elapsed = time.time() - start_time
                    log_with_time(f"📊 {task_name} 状态: {last_status} → {current_status} (耗时: {elapsed:.1f}s)")
                    last_status = current_status
                
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 完成，状态: {current_status}, 总耗时: {total_time:.1f}s")
                    return completion_time, current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 监控 {task_name} 出错: {e}")
        
        time.sleep(1)
    
    log_with_time(f"⏰ {task_name} 监控超时")
    return None, 'timeout'

def test_group_delay():
    """测试分组延迟功能"""
    log_with_time("=== 测试分组延迟功能 ===")
    
    # 使用分组1
    group_id = 1
    delay_group = 5  # 5秒分组延迟
    delay_like = 1   # 1秒操作延迟
    
    log_with_time(f"📱 使用分组ID: {group_id}")
    log_with_time(f"⏰ 设置: 分组延迟={delay_group}秒, 操作延迟={delay_like}秒")
    
    test_start_time = time.time()
    
    # 创建三个连续的分组任务
    tasks = []
    task_names = ["分组延迟测试1", "分组延迟测试2", "分组延迟测试3"]
    
    for i, task_name in enumerate(task_names):
        log_with_time(f"🚀 创建 {task_name}...")
        task = create_group_task(task_name, group_id, delay_group, delay_like)
        if task:
            tasks.append((task, task_name))
        else:
            log_with_time(f"❌ 创建 {task_name} 失败，停止测试")
            return
        
        # 快速连续创建
        time.sleep(0.2)
    
    log_with_time(f"📋 所有任务已创建，开始监控执行...")
    
    # 监控任务完成时间
    completion_times = []
    for task, task_name in tasks:
        completion_time, status = monitor_task_completion(task.get('id'), task_name, 60)
        if completion_time:
            completion_times.append((task_name, completion_time, status))
        else:
            log_with_time(f"❌ {task_name} 未能完成")
    
    # 分析结果
    log_with_time("📊 执行结果分析:")
    
    if len(completion_times) >= 2:
        for i in range(len(completion_times) - 1):
            task1_name, time1, status1 = completion_times[i]
            task2_name, time2, status2 = completion_times[i + 1]
            
            interval = time2 - time1
            log_with_time(f"   {task1_name}完成 → {task2_name}完成: {interval:.1f}秒")
            
            # 预期：任务1完成 + 5秒分组延迟 + 1秒操作延迟 = 6秒间隔
            expected_min = 5.5  # 允许一些误差
            expected_max = 7.0
            
            if expected_min <= interval <= expected_max:
                log_with_time(f"   ✅ 分组延迟正常工作 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
            else:
                log_with_time(f"   ⚠️ 分组延迟可能异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
    
    total_test_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_test_time:.1f}秒")
    
    # 预期时间：任务1(1s) + 延迟(5s) + 任务2(1s) + 延迟(5s) + 任务3(1s) = 13秒
    expected_total = 13.0
    if abs(total_test_time - expected_total) <= 3.0:  # 允许3秒误差
        log_with_time(f"✅ 总时间符合预期 (预期约{expected_total}秒)")
    else:
        log_with_time(f"⚠️ 总时间与预期差异较大 (预期约{expected_total}秒)")

def check_database_results():
    """检查数据库中的实际执行结果"""
    log_with_time("=== 检查数据库执行结果 ===")
    
    import sys
    sys.path.append('.')
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    from datetime import datetime, timedelta
    
    db = next(get_db())
    try:
        # 查看最近5分钟的分组延迟测试任务
        recent_time = datetime.now() - timedelta(minutes=5)
        recent_tasks = db.query(Task).filter(
            Task.create_time >= recent_time,
            Task.parameters.like('%分组延迟测试%')
        ).order_by(Task.create_time.asc()).all()
        
        log_with_time(f"找到 {len(recent_tasks)} 个分组延迟测试任务")
        
        completion_times = []
        for task in recent_tasks:
            task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task.id).first()
            if task_queue and task_queue.finish_time:
                completion_times.append((task.id, task_queue.finish_time))
                log_with_time(f"任务{task.id}: 完成时间 {task_queue.finish_time}")
        
        # 计算实际间隔
        if len(completion_times) >= 2:
            log_with_time("实际任务间隔:")
            for i in range(len(completion_times) - 1):
                task1_id, time1 = completion_times[i]
                task2_id, time2 = completion_times[i + 1]
                interval = (time2 - time1).total_seconds()
                log_with_time(f"  任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
        
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 最终分组延迟测试")
    print("💡 验证修复后的分组延迟功能")
    print("=" * 60)
    
    # 测试API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 1. 运行分组延迟测试
    test_group_delay()
    
    print("\n" + "-" * 40)
    
    # 2. 检查数据库结果
    check_database_results()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 修复要点:")
    print("   1. 在_process_device_task_with_delay中添加了分组延迟逻辑")
    print("   2. 分组延迟基于上次任务完成时间计算")
    print("   3. 任务完成时才更新设备最后完成时间")
    print("   4. 单位换算正确：前端秒*1000→毫秒，后端毫秒/1000→秒")
    print("=" * 60)

if __name__ == "__main__":
    main()
