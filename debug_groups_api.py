#!/usr/bin/env python3
"""
调试分组API问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app import crud, models
from sqlalchemy import text

def test_groups_crud():
    """测试分组CRUD操作"""
    print("测试分组CRUD操作...")
    
    try:
        db = next(get_db())
        
        # 测试获取所有分组
        print("1. 测试获取所有分组...")
        groups = crud.get_groups(db)
        print(f"✅ 获取到 {len(groups)} 个分组")
        
        for group in groups:
            print(f"   分组ID: {group.id}")
            print(f"   分组名称: {group.group_name}")
            print(f"   描述: {group.description}")
            print(f"   创建时间: {group.create_time}")
            print()
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ CRUD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_services():
    """测试分组服务"""
    print("\n测试分组服务...")
    
    try:
        from app.services.group import get_all_groups
        
        db = next(get_db())
        groups = get_all_groups(db)
        
        print(f"✅ 服务层获取到 {len(groups)} 个分组")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_schema():
    """测试分组Schema"""
    print("\n测试分组Schema...")
    
    try:
        from app.schemas.group import GroupOut
        
        db = next(get_db())
        
        # 获取一个分组
        group = db.query(models.Group).first()
        
        if group:
            print(f"找到分组: {group.group_name}")
            
            # 测试Schema转换
            group_out = GroupOut.model_validate(group)
            print(f"✅ Schema转换成功: {group_out}")
        else:
            print("❌ 数据库中没有分组")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Schema测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_sql():
    """直接SQL测试"""
    print("\n直接SQL测试...")
    
    try:
        db = next(get_db())
        
        # 直接查询分组表
        result = db.execute(text("SELECT * FROM groups"))
        groups = result.fetchall()
        
        print(f"✅ 直接SQL查询到 {len(groups)} 个分组")
        
        for group in groups:
            print(f"   ID: {group[0]}, 名称: {group[1]}, 描述: {group[2]}")
            
        # 检查表结构
        result = db.execute(text("DESCRIBE groups"))
        columns = result.fetchall()
        
        print("\n分组表结构:")
        for col in columns:
            print(f"   {col[0]}: {col[1]}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 直接SQL测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_group_model():
    """检查分组模型"""
    print("\n检查分组模型...")
    
    try:
        # 检查Group模型的字段
        group_fields = [attr for attr in dir(models.Group) if not attr.startswith('_')]
        print(f"Group模型字段: {group_fields}")
        
        # 检查GroupOut schema的字段
        from app.schemas.group import GroupOut
        schema_fields = list(GroupOut.model_fields.keys())
        print(f"GroupOut schema字段: {schema_fields}")
        
        # 检查字段匹配
        print("\n字段匹配检查:")
        for field in schema_fields:
            if hasattr(models.Group, field):
                print(f"   ✅ {field}: 匹配")
            else:
                print(f"   ❌ {field}: 不匹配")
                
        return True
        
    except Exception as e:
        print(f"❌ 模型检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 调试分组API问题...")
    print("="*50)
    
    # 1. 检查分组模型
    check_group_model()
    
    # 2. 直接SQL测试
    test_direct_sql()
    
    # 3. 测试CRUD
    test_groups_crud()
    
    # 4. 测试Schema
    test_group_schema()
    
    # 5. 测试服务层
    test_group_services()
    
    print("\n" + "="*50)
    print("🎯 调试完成")

if __name__ == "__main__":
    main()
