#!/usr/bin/env python3
"""
模拟设备连接WebSocket
"""

import asyncio
import websockets
import json
import time

async def simulate_device_connection():
    """模拟设备连接"""
    device_number = "test_reconnect_device"
    uri = f"ws://localhost:8000/ws/{device_number}"
    
    print(f"🔌 尝试连接WebSocket: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"✅ 设备 {device_number} 已连接WebSocket")
            
            # 发送心跳消息
            heartbeat_msg = {
                "type": "heartbeat",
                "timestamp": time.time(),
                "device_number": device_number
            }
            
            await websocket.send(json.dumps(heartbeat_msg))
            print(f"💓 发送心跳消息: {heartbeat_msg}")
            
            # 保持连接并监听消息
            print("📡 监听服务器消息...")
            
            try:
                while True:
                    # 等待服务器消息
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📨 收到服务器消息: {message}")
                    
                    try:
                        msg_data = json.loads(message)
                        msg_type = msg_data.get('type')
                        
                        if msg_type == 'task':
                            # 收到任务，模拟执行
                            task_id = msg_data.get('task_id')
                            print(f"🚀 收到任务: ID={task_id}")
                            
                            # 模拟任务执行时间
                            await asyncio.sleep(2)
                            
                            # 发送任务完成消息
                            completion_msg = {
                                "type": "task_completion",
                                "task_id": task_id,
                                "status": "completed",
                                "result": "模拟执行成功",
                                "timestamp": time.time()
                            }
                            
                            await websocket.send(json.dumps(completion_msg))
                            print(f"✅ 任务完成: {completion_msg}")
                            
                        elif msg_type == 'heartbeat':
                            # 收到心跳，回复心跳
                            heartbeat_response = {
                                "type": "heartbeat",
                                "timestamp": time.time(),
                                "device_number": device_number
                            }
                            await websocket.send(json.dumps(heartbeat_response))
                            print(f"💓 回复心跳: {heartbeat_response}")
                            
                    except json.JSONDecodeError:
                        print(f"⚠️ 无法解析消息: {message}")
                        
            except asyncio.TimeoutError:
                # 超时，发送心跳
                heartbeat_msg = {
                    "type": "heartbeat", 
                    "timestamp": time.time(),
                    "device_number": device_number
                }
                await websocket.send(json.dumps(heartbeat_msg))
                print(f"💓 发送定时心跳: {heartbeat_msg}")
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def main():
    """主函数"""
    print("🤖 模拟设备连接测试")
    print("="*50)
    
    print("📋 测试步骤:")
    print("   1. 连接WebSocket服务器")
    print("   2. 发送心跳消息")
    print("   3. 监听任务消息")
    print("   4. 模拟任务执行")
    print("   5. 发送任务完成消息")
    
    # 持续连接60秒
    try:
        await asyncio.wait_for(simulate_device_connection(), timeout=60.0)
    except asyncio.TimeoutError:
        print("\n⏰ 连接测试完成（30秒超时）")
    except KeyboardInterrupt:
        print("\n👋 用户中断连接")
    
    print("\n🏁 模拟设备连接测试结束")

if __name__ == "__main__":
    asyncio.run(main())
