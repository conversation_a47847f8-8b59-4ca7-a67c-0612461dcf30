"""
设备管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading

from config import Config
from utils.time_utils import utc_to_beijing, get_relative_time, beijing_timestamp

class DeviceManagerFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.devices_data = []
        
        self.create_widgets()
        self.refresh()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题和工具栏
        self.create_header()
        
        # 设备列表
        self.create_device_list()
        
        # 设备详情
        self.create_device_details()
        
    def create_header(self):
        """创建标题和工具栏"""
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 标题
        title_label = ttk.Label(header_frame, text="设备管理", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # 工具按钮
        btn_frame = ttk.Frame(header_frame)
        btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(btn_frame, text="🔄 刷新", command=self.refresh).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="➕ 添加设备", command=self.show_add_device_dialog).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="✏️ 编辑设备", command=self.show_edit_device_dialog).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="🗑️ 删除设备", command=self.delete_device).pack(side=tk.LEFT, padx=2)
        
        # 搜索框
        search_frame = ttk.Frame(self)
        search_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        
        # 状态过滤
        ttk.Label(search_frame, text="状态:").pack(side=tk.LEFT, padx=(20, 5))
        self.status_filter = ttk.Combobox(search_frame, values=["全部", "在线", "离线"], width=10)
        self.status_filter.set("全部")
        self.status_filter.bind('<<ComboboxSelected>>', self.on_filter_changed)
        self.status_filter.pack(side=tk.LEFT, padx=2)
        
    def create_device_list(self):
        """创建设备列表"""
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧设备列表
        list_frame = ttk.LabelFrame(main_frame, text="设备列表")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 创建Treeview
        columns = ('device_number', 'device_ip', 'online_status', 'account_status', 'current_task', 'last_heartbeat', 'group')
        self.device_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        self.device_tree.heading('device_number', text='设备号')
        self.device_tree.heading('device_ip', text='IP地址')
        self.device_tree.heading('online_status', text='在线状态')
        self.device_tree.heading('account_status', text='账号状态')
        self.device_tree.heading('current_task', text='当前任务')
        self.device_tree.heading('last_heartbeat', text='最后心跳')
        self.device_tree.heading('group', text='所属分组')

        # 设置列宽
        self.device_tree.column('device_number', width=120)
        self.device_tree.column('device_ip', width=120)
        self.device_tree.column('online_status', width=80)
        self.device_tree.column('account_status', width=80)
        self.device_tree.column('current_task', width=100)
        self.device_tree.column('last_heartbeat', width=150)
        self.device_tree.column('group', width=100)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.device_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.device_tree.xview)
        self.device_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.device_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定选择事件
        self.device_tree.bind('<<TreeviewSelect>>', self.on_device_selected)
        self.device_tree.bind('<Double-1>', self.on_device_double_click)
        
        # 右键菜单
        self.create_context_menu()
        
    def create_device_details(self):
        """创建设备详情面板"""
        # 右侧详情面板
        details_frame = ttk.LabelFrame(self, text="设备详情")
        details_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0), pady=5)
        details_frame.configure(width=300)
        
        # 设备信息显示
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息
        ttk.Label(info_frame, text="基本信息", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 10))
        
        self.detail_labels = {}
        fields = [
            ('设备号', 'device_number'),
            ('IP地址', 'device_ip'),
            ('在线状态', 'online_status'),
            ('账号状态', 'account_status'),
            ('当前任务', 'current_task'),
            ('最后心跳', 'last_heartbeat'),
            ('所属分组', 'group')
        ]
        
        for label_text, field_name in fields:
            frame = ttk.Frame(info_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(frame, text=f"{label_text}:", width=10).pack(side=tk.LEFT)
            label = ttk.Label(frame, text="-", foreground="blue")
            label.pack(side=tk.LEFT, fill=tk.X, expand=True)
            self.detail_labels[field_name] = label
            
        # 操作按钮
        ttk.Separator(info_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        
        btn_frame = ttk.Frame(info_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="编辑设备", command=self.show_edit_device_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="删除设备", command=self.delete_device).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="发送测试任务", command=self.send_test_task).pack(fill=tk.X, pady=2)
        
        # 任务队列信息
        ttk.Separator(info_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        ttk.Label(info_frame, text="任务队列", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 5))

        # 任务队列按钮
        queue_btn_frame = ttk.Frame(info_frame)
        queue_btn_frame.pack(fill=tk.X, pady=2)

        ttk.Button(queue_btn_frame, text="查看任务队列", command=self.show_task_queue).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_btn_frame, text="同步状态", command=self.sync_device_status).pack(side=tk.LEFT, padx=2)

        # 统计信息
        ttk.Separator(info_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        ttk.Label(info_frame, text="统计信息", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 5))

        self.stats_frame = ttk.Frame(info_frame)
        self.stats_frame.pack(fill=tk.X)

        self.stats_labels = {}
        stats_fields = [
            ('总设备数', 'total'),
            ('在线设备', 'online'),
            ('离线设备', 'offline'),
            ('执行任务', 'running_tasks')
        ]

        for label_text, field_name in stats_fields:
            frame = ttk.Frame(self.stats_frame)
            frame.pack(fill=tk.X, pady=1)

            ttk.Label(frame, text=f"{label_text}:", width=10).pack(side=tk.LEFT)
            label = ttk.Label(frame, text="0", foreground="green")
            label.pack(side=tk.LEFT)
            self.stats_labels[field_name] = label

        # 日志框
        ttk.Separator(info_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        ttk.Label(info_frame, text="操作日志", style='Header.TLabel').pack(anchor=tk.W, pady=(0, 5))

        # 日志文本框
        log_frame = ttk.Frame(info_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(log_frame, height=8, width=35, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 日志控制按钮
        log_btn_frame = ttk.Frame(info_frame)
        log_btn_frame.pack(fill=tk.X, pady=2)

        ttk.Button(log_btn_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(log_btn_frame, text="导出日志", command=self.export_log).pack(side=tk.LEFT, padx=2)

        # 初始化日志
        self.add_log("设备管理器已启动")
            
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="编辑设备", command=self.show_edit_device_dialog)
        self.context_menu.add_command(label="删除设备", command=self.delete_device)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="发送测试任务", command=self.send_test_task)
        self.context_menu.add_command(label="查看任务历史", command=self.show_task_history)
        
        self.device_tree.bind("<Button-3>", self.show_context_menu)
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
            
    def refresh(self):
        """刷新设备列表"""
        def fetch_data():
            try:
                devices = self.api_client.get_devices()
                self.winfo_toplevel().after(0, lambda: self.update_device_list(devices))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: messagebox.showerror("刷新失败", f"获取设备数据失败: {e}"))
                
        threading.Thread(target=fetch_data, daemon=True).start()
        
    def update_device_list(self, devices):
        """更新设备列表显示"""
        # 清空现有数据
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
            
        self.devices_data = devices or []
        
        # 添加设备数据
        for device in self.devices_data:
            # 格式化最后心跳时间为北京时间
            last_heartbeat = device.get('last_heartbeat', '')
            if last_heartbeat:
                last_heartbeat = utc_to_beijing(last_heartbeat)
                    
            # 获取分组名称
            group_name = device.get('group', {}).get('name', '未分组') if device.get('group') else '未分组'

            # 获取当前任务信息
            current_task = "无"
            if device.get('current_task_type'):
                task_type_name = Config.TASK_TYPES.get(device.get('current_task_type'), device.get('current_task_type'))
                current_task = f"🔄 {task_type_name}"

            values = (
                device.get('device_number', ''),
                device.get('device_ip', ''),
                Config.DEVICE_STATUS.get(device.get('online_status', ''), device.get('online_status', '')),
                Config.ACCOUNT_STATUS.get(device.get('account_status', ''), device.get('account_status', '')),
                current_task,
                last_heartbeat,
                group_name
            )
            
            item = self.device_tree.insert('', tk.END, values=values)
            
            # 设置行颜色
            if device.get('online_status') == 'online':
                self.device_tree.set(item, 'online_status', '🟢 在线')
            else:
                self.device_tree.set(item, 'online_status', '🔴 离线')
                
        # 更新统计信息
        self.update_statistics()
        
        # 应用过滤
        self.apply_filters()
        
    def update_statistics(self):
        """更新统计信息"""
        total = len(self.devices_data)
        online = sum(1 for device in self.devices_data if device.get('online_status') == 'online')
        offline = total - online
        running_tasks = sum(1 for device in self.devices_data if device.get('current_task_type'))

        self.stats_labels['total'].config(text=str(total))
        self.stats_labels['online'].config(text=str(online), foreground=Config.COLORS['online'])
        self.stats_labels['offline'].config(text=str(offline), foreground=Config.COLORS['offline'])
        self.stats_labels['running_tasks'].config(text=str(running_tasks), foreground='orange')
        
    def on_search_changed(self, *args):
        """搜索框内容变化"""
        self.apply_filters()
        
    def on_filter_changed(self, event):
        """状态过滤变化"""
        self.apply_filters()
        
    def apply_filters(self):
        """应用搜索和过滤"""
        search_text = self.search_var.get().lower()
        status_filter = self.status_filter.get()
        
        # 清空现有显示
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
            
        # 重新添加符合条件的设备
        for device in self.devices_data:
            # 搜索过滤
            if search_text:
                device_text = f"{device.get('device_number', '')} {device.get('device_ip', '')}".lower()
                if search_text not in device_text:
                    continue
                    
            # 状态过滤
            if status_filter != "全部":
                device_status = "在线" if device.get('online_status') == 'online' else "离线"
                if status_filter != device_status:
                    continue
                    
            # 添加到列表
            last_heartbeat = device.get('last_heartbeat', '')
            if last_heartbeat:
                last_heartbeat = utc_to_beijing(last_heartbeat)
                    
            group_name = device.get('group', {}).get('name', '未分组') if device.get('group') else '未分组'
            
            values = (
                device.get('device_number', ''),
                device.get('device_ip', ''),
                Config.DEVICE_STATUS.get(device.get('online_status', ''), device.get('online_status', '')),
                Config.ACCOUNT_STATUS.get(device.get('account_status', ''), device.get('account_status', '')),
                last_heartbeat,
                group_name
            )
            
            item = self.device_tree.insert('', tk.END, values=values)
            
            # 设置状态图标
            if device.get('online_status') == 'online':
                self.device_tree.set(item, 'online_status', '🟢 在线')
            else:
                self.device_tree.set(item, 'online_status', '🔴 离线')
                
    def on_device_selected(self, event):
        """设备选择事件"""
        selection = self.device_tree.selection()
        if not selection:
            return
            
        item = selection[0]
        device_number = self.device_tree.item(item)['values'][0]
        
        # 查找设备数据
        device_data = None
        for device in self.devices_data:
            if device.get('device_number') == device_number:
                device_data = device
                break
                
        if device_data:
            self.update_device_details(device_data)
            
    def on_device_double_click(self, event):
        """设备双击事件"""
        self.show_edit_device_dialog()
        
    def update_device_details(self, device_data):
        """更新设备详情显示"""
        # 格式化最后心跳时间为北京时间
        last_heartbeat = device_data.get('last_heartbeat', '')
        if last_heartbeat:
            last_heartbeat = utc_to_beijing(last_heartbeat)
                
        # 更新详情标签
        self.detail_labels['device_number'].config(text=device_data.get('device_number', '-'))
        self.detail_labels['device_ip'].config(text=device_data.get('device_ip', '-'))
        self.detail_labels['online_status'].config(
            text=Config.DEVICE_STATUS.get(device_data.get('online_status', ''), '-'),
            foreground=Config.COLORS.get(device_data.get('online_status', ''), 'black')
        )
        self.detail_labels['account_status'].config(text=Config.ACCOUNT_STATUS.get(device_data.get('account_status', ''), '-'))

        # 当前任务信息
        current_task = "无"
        if device_data.get('current_task_type'):
            task_type_name = Config.TASK_TYPES.get(device_data.get('current_task_type'), device_data.get('current_task_type'))
            current_task = f"🔄 {task_type_name}"
            if device_data.get('current_task_id'):
                current_task += f" (ID: {device_data.get('current_task_id')})"
        self.detail_labels['current_task'].config(
            text=current_task,
            foreground='orange' if device_data.get('current_task_type') else 'gray'
        )

        self.detail_labels['last_heartbeat'].config(text=last_heartbeat)

        group_name = device_data.get('group', {}).get('name', '未分组') if device_data.get('group') else '未分组'
        self.detail_labels['group'].config(text=group_name)
        
    def get_selected_device(self):
        """获取当前选中的设备"""
        selection = self.device_tree.selection()
        if not selection:
            return None
            
        item = selection[0]
        device_number = self.device_tree.item(item)['values'][0]
        
        for device in self.devices_data:
            if device.get('device_number') == device_number:
                return device
        return None
        
    def show_add_device_dialog(self):
        """显示添加设备对话框"""
        # TODO: 实现添加设备对话框
        messagebox.showinfo("功能开发中", "添加设备功能正在开发中")
        
    def show_edit_device_dialog(self):
        """显示编辑设备对话框"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要编辑的设备")
            return
            
        # TODO: 实现编辑设备对话框
        messagebox.showinfo("功能开发中", f"编辑设备 {device.get('device_number')} 功能正在开发中")
        
    def delete_device(self):
        """删除设备"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要删除的设备")
            return
            
        if messagebox.askyesno("确认删除", f"确定要删除设备 {device.get('device_number')} 吗？"):
            # TODO: 实现删除设备功能
            messagebox.showinfo("功能开发中", "删除设备功能正在开发中")
            
    def send_test_task(self):
        """发送测试任务"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要发送测试任务的设备")
            return
            
        # TODO: 实现发送测试任务功能
        messagebox.showinfo("功能开发中", f"向设备 {device.get('device_number')} 发送测试任务功能正在开发中")
        
    def show_task_history(self):
        """显示任务历史"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要查看任务历史的设备")
            return

        self.show_task_queue()

    def show_task_queue(self):
        """显示设备任务队列"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要查看任务队列的设备")
            return

        def fetch_queue():
            try:
                import requests
                response = requests.get(f"{self.api_client.base_url}/task-sync/device-queue/{device.get('id')}", timeout=10)
                if response.status_code == 200:
                    queue_data = response.json()
                    self.winfo_toplevel().after(0, lambda: self.display_task_queue(device, queue_data))
                else:
                    self.winfo_toplevel().after(0, lambda: self.log_message(f"获取任务队列失败: {response.status_code}"))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: self.log_message(f"获取任务队列异常: {e}"))

        threading.Thread(target=fetch_queue, daemon=True).start()

    def display_task_queue(self, device, queue_data):
        """显示任务队列对话框"""
        dialog = tk.Toplevel(self)
        dialog.title(f"设备 {device.get('device_number')} 任务队列")
        dialog.geometry("800x500")
        dialog.transient(self.winfo_toplevel())
        dialog.grab_set()

        # 创建任务队列列表
        frame = ttk.Frame(dialog)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        ttk.Label(frame, text=f"设备: {device.get('device_number')} 任务队列", font=('Arial', 12, 'bold')).pack(pady=5)

        # 任务列表
        columns = ('queue_id', 'task_type', 'status', 'create_time', 'result')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=15)

        tree.heading('queue_id', text='队列ID')
        tree.heading('task_type', text='任务类型')
        tree.heading('status', text='状态')
        tree.heading('create_time', text='创建时间')
        tree.heading('result', text='结果')

        tree.column('queue_id', width=80)
        tree.column('task_type', width=120)
        tree.column('status', width=80)
        tree.column('create_time', width=150)
        tree.column('result', width=200)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充数据
        queue = queue_data.get('queue', [])
        for item in queue:
            task_type_name = Config.TASK_TYPES.get(item.get('task_type'), item.get('task_type'))
            create_time = item.get('create_time', '')
            if create_time:
                try:
                    dt = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
                    create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass

            values = (
                item.get('queue_id', ''),
                task_type_name,
                item.get('status', ''),
                create_time,
                item.get('result_summary', '')[:50] + '...' if len(item.get('result_summary', '')) > 50 else item.get('result_summary', '')
            )
            tree.insert('', tk.END, values=values)

        # 按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(btn_frame, text="刷新", command=lambda: self.refresh_task_queue(dialog, device, tree)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        self.log_message(f"显示设备 {device.get('device_number')} 任务队列，共 {len(queue)} 个任务")

    def refresh_task_queue(self, dialog, device, tree):
        """刷新任务队列"""
        def fetch_queue():
            try:
                import requests
                response = requests.get(f"{self.api_client.base_url}/task-sync/device-queue/{device.get('id')}", timeout=10)
                if response.status_code == 200:
                    queue_data = response.json()
                    dialog.after(0, lambda: self.update_task_queue_tree(tree, queue_data))
                else:
                    dialog.after(0, lambda: self.log_message(f"刷新任务队列失败: {response.status_code}"))
            except Exception as e:
                dialog.after(0, lambda: self.log_message(f"刷新任务队列异常: {e}"))

        threading.Thread(target=fetch_queue, daemon=True).start()

    def update_task_queue_tree(self, tree, queue_data):
        """更新任务队列树"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)

        # 填充新数据
        queue = queue_data.get('queue', [])
        for item in queue:
            task_type_name = Config.TASK_TYPES.get(item.get('task_type'), item.get('task_type'))
            create_time = item.get('create_time', '')
            if create_time:
                try:
                    dt = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
                    create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass

            values = (
                item.get('queue_id', ''),
                task_type_name,
                item.get('status', ''),
                create_time,
                item.get('result_summary', '')[:50] + '...' if len(item.get('result_summary', '')) > 50 else item.get('result_summary', '')
            )
            tree.insert('', tk.END, values=values)

    def sync_device_status(self):
        """同步设备状态"""
        device = self.get_selected_device()
        if not device:
            messagebox.showwarning("未选择设备", "请先选择要同步状态的设备")
            return

        def sync_status():
            try:
                import requests
                # 同步设备当前任务
                response = requests.post(f"{self.api_client.base_url}/task-sync/sync-device/{device.get('id')}", timeout=10)
                if response.status_code == 200:
                    self.winfo_toplevel().after(0, lambda: self.log_message(f"设备 {device.get('device_number')} 状态同步成功"))
                    self.winfo_toplevel().after(0, self.refresh)
                else:
                    self.winfo_toplevel().after(0, lambda: self.log_message(f"设备状态同步失败: {response.status_code}"))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: self.log_message(f"设备状态同步异常: {e}"))

        threading.Thread(target=sync_status, daemon=True).start()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = beijing_timestamp().split(' ')[1]  # 只取时间部分
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = self.log_text.get('1.0', tk.END).split('\n')
        if len(lines) > 100:
            # 保留最后100行
            self.log_text.delete('1.0', f'{len(lines)-100}.0')

    def add_log(self, message):
        """添加日志的别名方法"""
        self.log_message(message)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete('1.0', tk.END)
        self.log_message("日志已清空")

    def export_log(self):
        """导出日志"""
        from tkinter import filedialog

        filename = filedialog.asksaveasfilename(
            title="导出日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get('1.0', tk.END))
                self.log_message(f"日志已导出到: {filename}")
            except Exception as e:
                self.log_message(f"导出日志失败: {e}")
                messagebox.showerror("导出失败", f"导出日志失败: {e}")
