#!/usr/bin/env python3
"""
简化的任务创建器
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class SimpleTaskCreator(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        
        # 初始化数据
        self.groups_data = []
        self.devices_data = []
        self.group_vars = {}
        self.group_map = {}
        
        self.create_widgets()
        self.load_data()
        
    def create_widgets(self):
        """创建现代化界面组件"""
        # 配置样式
        self.configure_styles()

        # 主容器 - 使用现代化卡片设计
        main_container = ttk.Frame(self, style='Card.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题区域
        title_frame = ttk.Frame(main_container, style='Card.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(title_frame, text="🚀 快速创建任务", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        # 状态指示器
        self.status_label = ttk.Label(title_frame, text="● 就绪", style='Status.TLabel')
        self.status_label.pack(side=tk.RIGHT)

        # 任务配置区域
        config_frame = ttk.LabelFrame(main_container, text="任务配置", style='Section.TLabelframe')
        config_frame.pack(fill=tk.X, pady=(0, 10))

        # 使用网格布局
        config_frame.grid_columnconfigure(1, weight=1)
        config_frame.grid_columnconfigure(3, weight=1)

        # 第一行：任务类型和目标
        ttk.Label(config_frame, text="任务类型:", style='Label.TLabel').grid(row=0, column=0, sticky=tk.W, padx=10, pady=8)
        self.task_type = ttk.Combobox(config_frame, values=["签到任务", "点赞任务", "超话签到任务", "主页关注任务"],
                                     width=15, style='Modern.TCombobox')
        self.task_type.set("点赞任务")
        self.task_type.grid(row=0, column=1, sticky=tk.W, padx=10, pady=8)

        ttk.Label(config_frame, text="执行目标:", style='Label.TLabel').grid(row=0, column=2, sticky=tk.W, padx=10, pady=8)
        self.target_scope = ttk.Combobox(config_frame, values=["设备分组"], width=15, style='Modern.TCombobox')
        self.target_scope.set("设备分组")
        self.target_scope.bind('<<ComboboxSelected>>', self.on_target_changed)
        self.target_scope.grid(row=0, column=3, sticky=tk.W, padx=10, pady=8)

        # 第二行：参数输入
        ttk.Label(config_frame, text="博主ID:", style='Label.TLabel').grid(row=1, column=0, sticky=tk.W, padx=10, pady=8)
        self.blogger_id = tk.StringVar()
        blogger_entry = ttk.Entry(config_frame, textvariable=self.blogger_id, width=20, style='Modern.TEntry')
        blogger_entry.grid(row=1, column=1, sticky=tk.EW, padx=10, pady=8)

        ttk.Label(config_frame, text="点赞ID:", style='Label.TLabel').grid(row=1, column=2, sticky=tk.W, padx=10, pady=8)
        self.like_id = tk.StringVar()
        like_entry = ttk.Entry(config_frame, textvariable=self.like_id, width=20, style='Modern.TEntry')
        like_entry.grid(row=1, column=3, sticky=tk.EW, padx=10, pady=8)

        # 第三行：延迟设置
        delay_frame = ttk.Frame(config_frame)
        delay_frame.grid(row=2, column=0, columnspan=4, sticky=tk.EW, padx=10, pady=8)

        ttk.Label(delay_frame, text="分组延迟:", style='Label.TLabel').pack(side=tk.LEFT)
        self.delay_group = tk.StringVar(value="2")
        delay_group_entry = ttk.Entry(delay_frame, textvariable=self.delay_group, width=8, style='Modern.TEntry')
        delay_group_entry.pack(side=tk.LEFT, padx=(5, 15))
        ttk.Label(delay_frame, text="秒", style='Unit.TLabel').pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(delay_frame, text="操作延迟:", style='Label.TLabel').pack(side=tk.LEFT)
        self.delay_like = tk.StringVar(value="1")
        delay_like_entry = ttk.Entry(delay_frame, textvariable=self.delay_like, width=8, style='Modern.TEntry')
        delay_like_entry.pack(side=tk.LEFT, padx=(5, 15))
        ttk.Label(delay_frame, text="秒", style='Unit.TLabel').pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(delay_frame, text="点赞延迟:", style='Label.TLabel').pack(side=tk.LEFT)
        self.delay_click = tk.StringVar(value="0.5")
        delay_click_entry = ttk.Entry(delay_frame, textvariable=self.delay_click, width=8, style='Modern.TEntry')
        delay_click_entry.pack(side=tk.LEFT, padx=(5, 15))
        ttk.Label(delay_frame, text="秒", style='Unit.TLabel').pack(side=tk.LEFT)

        # 分组选择区域
        groups_frame = ttk.LabelFrame(main_container, text="选择分组", style='Section.TLabelframe')
        groups_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 分组选择容器
        self.groups_container = ttk.Frame(groups_frame)
        self.groups_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 操作按钮区域
        button_frame = ttk.Frame(main_container, style='Card.TFrame')
        button_frame.pack(fill=tk.X, pady=(0, 5))

        # 主要操作按钮
        self.create_btn = ttk.Button(button_frame, text="🚀 创建任务", command=self.create_task,
                                    style='Primary.TButton')
        self.create_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 次要操作按钮
        ttk.Button(button_frame, text="🔄 刷新", command=self.load_data,
                  style='Secondary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 保存", command=self.save_settings,
                  style='Secondary.TButton').pack(side=tk.LEFT, padx=(0, 5))

        # 进度指示器
        self.progress_frame = ttk.Frame(main_container)
        self.progress_frame.pack(fill=tk.X, pady=(5, 0))

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate', style='Modern.Horizontal.TProgressbar')
        self.progress_label = ttk.Label(self.progress_frame, text="", style='Progress.TLabel')

    def configure_styles(self):
        """配置现代化样式"""
        style = ttk.Style()

        # 配置主题色彩
        style.configure('Card.TFrame', relief='flat', borderwidth=1)
        style.configure('Title.TLabel', font=('Segoe UI', 14, 'bold'), foreground='#2c3e50')
        style.configure('Status.TLabel', font=('Segoe UI', 9), foreground='#27ae60')
        style.configure('Label.TLabel', font=('Segoe UI', 9), foreground='#34495e')
        style.configure('Unit.TLabel', font=('Segoe UI', 8), foreground='#7f8c8d')
        style.configure('Progress.TLabel', font=('Segoe UI', 8), foreground='#3498db')

        # 现代化组件样式
        style.configure('Modern.TEntry', fieldbackground='white', borderwidth=1, relief='solid')
        style.configure('Modern.TCombobox', fieldbackground='white', borderwidth=1, relief='solid')
        style.configure('Section.TLabelframe', relief='flat', borderwidth=1)
        style.configure('Section.TLabelframe.Label', font=('Segoe UI', 10, 'bold'), foreground='#2c3e50')

        # 按钮样式
        style.configure('Primary.TButton', font=('Segoe UI', 9, 'bold'))
        style.configure('Secondary.TButton', font=('Segoe UI', 9))
        style.configure('Modern.Horizontal.TProgressbar', troughcolor='#ecf0f1', background='#3498db')
        
    def load_data(self):
        """加载设备和分组数据"""
        try:
            # 获取分组数据
            self.groups_data = self.api_client.get_groups() or []
            self.devices_data = self.api_client.get_devices() or []
            
            print(f"加载了 {len(self.groups_data)} 个分组")
            print(f"加载了 {len(self.devices_data)} 个设备")
            
            self.update_groups_display()
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            messagebox.showerror("错误", f"加载数据失败: {e}")
    
    def update_groups_display(self):
        """更新现代化分组显示"""
        # 清空容器
        for widget in self.groups_container.winfo_children():
            widget.destroy()

        self.group_vars = {}
        self.group_map = {}

        if not self.groups_data:
            empty_frame = ttk.Frame(self.groups_container)
            empty_frame.pack(expand=True, fill=tk.BOTH)

            ttk.Label(empty_frame, text="📭", font=('Segoe UI', 24)).pack(pady=(20, 5))
            ttk.Label(empty_frame, text="暂无分组数据", style='Label.TLabel').pack()
            ttk.Label(empty_frame, text="请先在分组管理中创建分组", font=('Segoe UI', 8), foreground='#7f8c8d').pack(pady=(0, 20))
            return

        # 控制按钮区域
        control_frame = ttk.Frame(self.groups_container)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(control_frame, text="✓ 全选", command=self.select_all,
                  style='Secondary.TButton', width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="✗ 全不选", command=self.deselect_all,
                  style='Secondary.TButton', width=8).pack(side=tk.LEFT, padx=(0, 10))

        # 分组统计
        total_devices = sum(len([d for d in self.devices_data if d.get('group_id') == g.get('id')])
                           for g in self.groups_data)
        stats_label = ttk.Label(control_frame,
                               text=f"共 {len(self.groups_data)} 个分组，{total_devices} 台设备",
                               style='Unit.TLabel')
        stats_label.pack(side=tk.RIGHT)

        # 创建现代化分组卡片容器
        canvas = tk.Canvas(self.groups_container, highlightthickness=0, bg='white')
        scrollbar = ttk.Scrollbar(self.groups_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 使用网格布局创建分组卡片
        cols = 2  # 每行显示2个分组
        for i, group in enumerate(self.groups_data):
            group_id = group.get('id')
            group_name = group.get('group_name', '')

            # 计算设备数量和在线状态
            group_devices = [d for d in self.devices_data if d.get('group_id') == group_id]
            device_count = len(group_devices)
            online_count = len([d for d in group_devices if d.get('online_status') == 'online'])

            var = tk.BooleanVar()
            self.group_vars[group_id] = var
            self.group_map[group_id] = group_name

            # 创建分组卡片
            row = i // cols
            col = i % cols

            card_frame = ttk.Frame(scrollable_frame, style='Card.TFrame', relief='solid', borderwidth=1)
            card_frame.grid(row=row, column=col, sticky=tk.EW, padx=5, pady=3)

            # 配置列权重
            scrollable_frame.grid_columnconfigure(col, weight=1)

            # 卡片内容
            header_frame = ttk.Frame(card_frame)
            header_frame.pack(fill=tk.X, padx=10, pady=(8, 4))

            # 复选框和分组名
            cb = ttk.Checkbutton(header_frame, variable=var, style='Modern.TCheckbutton')
            cb.pack(side=tk.LEFT)

            name_label = ttk.Label(header_frame, text=group_name, style='Label.TLabel', font=('Segoe UI', 9, 'bold'))
            name_label.pack(side=tk.LEFT, padx=(5, 0))

            # 状态指示器
            if online_count > 0:
                status_color = '#27ae60' if online_count == device_count else '#f39c12'
                status_text = f"● {online_count}/{device_count} 在线"
            else:
                status_color = '#e74c3c'
                status_text = "● 离线"

            status_label = ttk.Label(header_frame, text=status_text, foreground=status_color, font=('Segoe UI', 8))
            status_label.pack(side=tk.RIGHT)

            # 设备信息
            info_frame = ttk.Frame(card_frame)
            info_frame.pack(fill=tk.X, padx=10, pady=(0, 8))

            device_info = f"📱 {device_count} 台设备"
            if device_count > 0:
                device_info += f" | 🟢 {online_count} 在线"
                if device_count - online_count > 0:
                    device_info += f" | 🔴 {device_count - online_count} 离线"

            ttk.Label(info_frame, text=device_info, style='Unit.TLabel').pack(side=tk.LEFT)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        print(f"显示了 {len(self.groups_data)} 个现代化分组卡片")
    
    def on_target_changed(self, event):
        """目标范围变化"""
        self.update_groups_display()
    
    def select_all(self):
        """全选分组"""
        for var in self.group_vars.values():
            var.set(True)
    
    def deselect_all(self):
        """全不选分组"""
        for var in self.group_vars.values():
            var.set(False)
    
    def create_task(self):
        """创建任务 - 现代化交互"""
        try:
            # 验证输入
            if not self.validate_inputs():
                return

            # 显示进度
            self.show_progress("正在创建任务...")
            self.update_status("● 创建中", '#f39c12')

            # 禁用创建按钮
            self.create_btn.configure(state='disabled')

            # 获取任务配置
            task_config = self.get_task_config()
            if not task_config:
                self.hide_progress()
                self.create_btn.configure(state='normal')
                return

            # 异步创建任务
            import threading
            threading.Thread(target=self.create_tasks_async, args=(task_config,), daemon=True).start()

        except Exception as e:
            self.hide_progress()
            self.create_btn.configure(state='normal')
            self.update_status("● 错误", '#e74c3c')
            messagebox.showerror("创建失败", f"创建任务时发生错误: {e}")

    def validate_inputs(self):
        """验证输入参数"""
        # 检查任务类型
        if not self.task_type.get():
            self.show_error_tooltip("请选择任务类型")
            return False

        # 检查参数 - 根据任务类型验证
        task_type_name = self.task_type.get()

        # 点赞任务只需要点赞ID
        if task_type_name == "点赞任务":
            if not self.like_id.get().strip():
                self.show_error_tooltip("请输入点赞ID")
                return False
        else:
            # 其他任务需要博主ID
            if not self.blogger_id.get().strip():
                self.show_error_tooltip("请输入博主ID")
                return False

            # 超话签到任务还需要点赞ID
            if task_type_name == "超话签到" and not self.like_id.get().strip():
                self.show_error_tooltip("请输入点赞ID")
                return False

        # 检查分组选择
        selected_count = sum(1 for var in self.group_vars.values() if var.get())
        if selected_count == 0:
            self.show_error_tooltip("请至少选择一个分组")
            return False

        # 检查延迟设置
        try:
            float(self.delay_group.get())
            float(self.delay_like.get())
            float(self.delay_click.get())
        except ValueError:
            self.show_error_tooltip("延迟设置必须是数字")
            return False

        return True

    def get_task_config(self):
        """获取任务配置"""
        try:
            task_type_name = self.task_type.get()
            task_type_map = {
                "签到任务": "sign",
                "点赞任务": "like",
                "超话签到任务": "page_sign",
                "主页关注任务": "inex"
            }
            task_type = task_type_map.get(task_type_name)

            blogger_id = self.blogger_id.get().strip()
            like_id = self.like_id.get().strip()

            # 构建参数，添加点赞延迟
            delay_click_ms = int(float(self.delay_click.get()) * 1000)  # 转换为毫秒

            if task_type == "sign":
                parameters = {"count": 1, "delay_click": delay_click_ms}
            elif task_type == "like":
                # 点赞任务只需要点赞ID
                parameters = {
                    "like_id": like_id,
                    "delay_click": delay_click_ms
                }
            elif task_type == "page_sign":
                parameters = {
                    "page_url": f"https://weibo.com/p/{blogger_id}",
                    "blogger_id": blogger_id,
                    "like_id": like_id,
                    "delay_click": delay_click_ms
                }
            elif task_type == "inex":
                parameters = {
                    "user_id": blogger_id,
                    "count": 1,
                    "delay_click": delay_click_ms
                }

            # 获取选中的分组
            selected_groups = [group_id for group_id, var in self.group_vars.items() if var.get()]

            return {
                "task_type": task_type,
                "task_type_name": task_type_name,
                "parameters": parameters,
                "selected_groups": selected_groups,
                "delay_group": int(float(self.delay_group.get()) * 1000),
                "delay_like": int(float(self.delay_like.get()) * 1000)
            }

        except Exception as e:
            print(f"获取任务配置失败: {e}")
            return None

    def show_error_tooltip(self, message):
        """显示错误提示"""
        self.update_status(f"● {message}", '#e74c3c')
        # 3秒后恢复状态
        self.after(3000, lambda: self.update_status("● 就绪", '#27ae60'))

    def show_progress(self, message):
        """显示进度"""
        self.progress_label.configure(text=message)
        self.progress_label.pack(side=tk.LEFT, padx=(0, 10))
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.progress_bar.start(10)

    def hide_progress(self):
        """隐藏进度"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        self.progress_label.pack_forget()

    def update_status(self, text, color):
        """更新状态"""
        self.status_label.configure(text=text, foreground=color)

    def create_tasks_async(self, config):
        """异步创建任务"""
        try:
            created_tasks = []
            failed_tasks = []
            total_groups = len(config["selected_groups"])

            for i, group_id in enumerate(config["selected_groups"]):
                # 更新进度
                progress_text = f"正在为分组 {i+1}/{total_groups} 创建任务..."
                self.after(0, lambda t=progress_text: self.progress_label.configure(text=t))

                try:
                    task_data = {
                        "task_type": config["task_type"],
                        "parameters": config["parameters"],
                        "target_scope": "group",
                        "target_id": group_id,
                        "delay_group": config["delay_group"],
                        "delay_like": config["delay_like"]
                    }

                    result = self.api_client.create_task(task_data)
                    if result:
                        group_name = self.group_map.get(group_id, f"分组{group_id}")
                        created_tasks.append(f"{group_name} (ID: {result.get('id')})")
                    else:
                        group_name = self.group_map.get(group_id, f"分组{group_id}")
                        failed_tasks.append(group_name)

                except Exception as e:
                    group_name = self.group_map.get(group_id, f"分组{group_id}")
                    failed_tasks.append(f"{group_name}: {str(e)}")

            # 在主线程中显示结果
            self.after(0, lambda: self.show_creation_result(created_tasks, failed_tasks, config["task_type_name"]))

        except Exception as e:
            self.after(0, lambda: self.show_creation_error(str(e)))

    def show_creation_result(self, created_tasks, failed_tasks, task_type_name):
        """显示创建结果"""
        self.hide_progress()
        self.create_btn.configure(state='normal')

        if created_tasks and not failed_tasks:
            # 全部成功
            self.update_status("● 创建成功", '#27ae60')
            self.show_success_dialog(created_tasks, task_type_name)
        elif created_tasks and failed_tasks:
            # 部分成功
            self.update_status("● 部分成功", '#f39c12')
            self.show_partial_success_dialog(created_tasks, failed_tasks, task_type_name)
        else:
            # 全部失败
            self.update_status("● 创建失败", '#e74c3c')
            self.show_failure_dialog(failed_tasks)

    def show_creation_error(self, error_msg):
        """显示创建错误"""
        self.hide_progress()
        self.create_btn.configure(state='normal')
        self.update_status("● 错误", '#e74c3c')
        messagebox.showerror("创建失败", f"批量创建任务失败: {error_msg}")

    def show_success_dialog(self, created_tasks, task_type_name):
        """显示成功对话框"""
        message = f"🎉 成功创建 {len(created_tasks)} 个{task_type_name}！\n\n"
        message += "创建的任务:\n" + "\n".join(f"✓ {task}" for task in created_tasks)
        messagebox.showinfo("创建成功", message)

    def show_partial_success_dialog(self, created_tasks, failed_tasks, task_type_name):
        """显示部分成功对话框"""
        message = f"⚠️ 部分任务创建成功\n\n"
        message += f"✅ 成功 {len(created_tasks)} 个:\n" + "\n".join(f"✓ {task}" for task in created_tasks)
        message += f"\n\n❌ 失败 {len(failed_tasks)} 个:\n" + "\n".join(f"✗ {task}" for task in failed_tasks)
        messagebox.showwarning("部分成功", message)

    def show_failure_dialog(self, failed_tasks):
        """显示失败对话框"""
        message = f"❌ 所有任务创建失败\n\n失败原因:\n"
        message += "\n".join(f"✗ {task}" for task in failed_tasks)
        messagebox.showerror("创建失败", message)
    
    def create_multiple_tasks(self, task_type, parameters, group_ids):
        """为多个分组创建任务"""
        try:
            delay_group = int(float(self.delay_group.get()) * 1000)
            delay_like = int(float(self.delay_like.get()) * 1000)
            
            created_tasks = []
            failed_tasks = []
            
            for group_id in group_ids:
                try:
                    task_data = {
                        "task_type": task_type,
                        "parameters": parameters,
                        "target_scope": "group",
                        "target_id": group_id,
                        "delay_group": delay_group,
                        "delay_like": delay_like
                    }
                    
                    result = self.api_client.create_task(task_data)
                    if result:
                        group_name = self.group_map.get(group_id, f"分组{group_id}")
                        created_tasks.append(f"{group_name} (ID: {result.get('id')})")
                    else:
                        group_name = self.group_map.get(group_id, f"分组{group_id}")
                        failed_tasks.append(group_name)
                        
                except Exception as e:
                    group_name = self.group_map.get(group_id, f"分组{group_id}")
                    failed_tasks.append(f"{group_name}: {e}")
            
            # 显示结果
            message = ""
            if created_tasks:
                message += f"成功创建 {len(created_tasks)} 个任务:\n"
                message += "\n".join(created_tasks)
            
            if failed_tasks:
                if message:
                    message += "\n\n"
                message += f"失败 {len(failed_tasks)} 个任务:\n"
                message += "\n".join(failed_tasks)
            
            if created_tasks and not failed_tasks:
                messagebox.showinfo("全部成功", message)
            elif created_tasks and failed_tasks:
                messagebox.showwarning("部分成功", message)
            else:
                messagebox.showerror("全部失败", message)
                
        except Exception as e:
            messagebox.showerror("错误", f"批量创建任务失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                "task_type": self.task_type.get(),
                "blogger_id": self.blogger_id.get(),
                "like_id": self.like_id.get(),
                "delay_group": self.delay_group.get(),
                "delay_like": self.delay_like.get(),
                "delay_click": self.delay_click.get()
            }
            
            # 确保设置目录存在
            current_dir = os.path.dirname(os.path.abspath(__file__))
            settings_dir = os.path.join(current_dir, "..", "settings")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            settings_file = os.path.join(settings_dir, "simple_task_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo("保存成功", "设置已保存")
            print(f"设置已保存到: {settings_file}")
            
        except Exception as e:
            messagebox.showerror("保存失败", f"保存设置失败: {e}")
