#!/usr/bin/env python3
"""
调试inex任务API问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app import crud, models
from app.schemas.task import TaskCreate
from app.services.dispatcher import dispatcher

def test_direct_task_creation():
    """直接测试任务创建"""
    print("直接测试任务创建...")
    
    try:
        # 创建TaskCreate对象
        task_data = TaskCreate(
            task_type="inex",
            parameters={"user_id": "test123", "count": 1},
            target_scope="single",
            target_id=1,
            delay_group=1000,
            delay_like=500
        )
        
        print(f"TaskCreate对象: {task_data}")
        
        # 使用crud创建任务
        db = next(get_db())
        task = crud.create_task(db, task_data)
        
        print(f"✅ 任务创建成功:")
        print(f"   ID: {task.id}")
        print(f"   类型: {task.task_type}")
        print(f"   参数: {task.parameters}")
        print(f"   状态: {task.status}")
        
        db.close()
        return task.id
        
    except Exception as e:
        print(f"❌ 直接创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_dispatcher():
    """测试调度器"""
    print("\n测试调度器...")
    
    try:
        # 创建TaskCreate对象
        task_data = TaskCreate(
            task_type="inex",
            parameters={"user_id": "test123", "count": 1},
            target_scope="single",
            target_id=1,
            delay_group=1000,
            delay_like=500
        )
        
        print(f"TaskCreate对象: {task_data}")
        
        # 使用dispatcher
        import asyncio
        
        async def test_dispatch():
            result = await dispatcher.dispatch_group_task(task_data)
            return result
        
        # 运行异步函数
        result = asyncio.run(test_dispatch())
        
        print(f"✅ 调度器测试成功:")
        print(f"   结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_validation():
    """测试schema验证"""
    print("\n测试schema验证...")
    
    try:
        from app.schemas.task import TaskCreate, TaskOut
        
        # 测试TaskCreate
        task_create = TaskCreate(
            task_type="inex",
            parameters={"user_id": "test123", "count": 1},
            target_scope="single",
            target_id=1,
            delay_group=1000,
            delay_like=500
        )
        
        print(f"✅ TaskCreate验证成功: {task_create}")
        
        # 测试从数据库获取任务
        db = next(get_db())
        task = db.query(models.Task).filter(models.Task.task_type == "inex").first()
        
        if task:
            print(f"找到inex任务: ID={task.id}")
            
            # 测试TaskOut
            task_out = TaskOut.model_validate(task)
            print(f"✅ TaskOut验证成功: {task_out}")
        else:
            print("❌ 数据库中没有inex任务")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Schema验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enum_values():
    """测试枚举值"""
    print("\n测试枚举值...")
    
    try:
        from sqlalchemy import text
        
        db = next(get_db())
        
        # 检查任务类型枚举
        result = db.execute(text("""
            SELECT COLUMN_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'tasks' 
            AND COLUMN_NAME = 'task_type'
        """))
        
        enum_def = result.fetchone()
        print(f"数据库枚举定义: {enum_def[0]}")
        
        # 测试插入inex任务
        result = db.execute(text("""
            INSERT INTO tasks (task_type, parameters, target_scope, target_id, delay_group, delay_like, status)
            VALUES ('inex', '{"user_id": "debug_test", "count": 1}', 'single', 1, 1000, 500, 'pending')
        """))
        
        task_id = result.lastrowid
        db.commit()
        
        print(f"✅ 直接SQL插入成功，任务ID: {task_id}")
        
        # 验证插入的任务
        result = db.execute(text("SELECT task_type, parameters FROM tasks WHERE id = :id"), {"id": task_id})
        task = result.fetchone()
        
        if task:
            print(f"   任务类型: {task[0]}")
            print(f"   参数: {task[1]}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 枚举测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 调试inex任务API问题...")
    print("="*60)
    
    # 1. 测试枚举值
    test_enum_values()
    
    # 2. 测试schema验证
    test_schema_validation()
    
    # 3. 测试直接任务创建
    task_id = test_direct_task_creation()
    
    # 4. 测试调度器
    test_dispatcher()
    
    print("\n" + "="*60)
    print("🎯 调试完成")
    
    if task_id:
        print(f"✅ 成功创建了inex任务，ID: {task_id}")
        print("问题可能在API路由层面，请检查服务器日志")
    else:
        print("❌ 任务创建失败，请检查上述错误信息")

if __name__ == "__main__":
    main()
