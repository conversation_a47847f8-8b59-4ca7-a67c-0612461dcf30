#!/usr/bin/env python3
"""
简单有效的分组延迟实现
直接在WebSocket层处理，避免复杂的调度器逻辑
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional
from app.utils.time_utils import get_beijing_now_naive

logger = logging.getLogger(__name__)

class SimpleGroupDelayManager:
    """简单的分组延迟管理器"""
    
    def __init__(self):
        # 记录每个设备最后一次任务完成的时间
        self.device_last_completion_time: Dict[int, datetime] = {}
        
        # 记录每个设备当前正在处理的任务
        self.device_current_task: Dict[int, Optional[int]] = {}
        
        # 设备锁，确保同一设备的任务串行处理
        self.device_locks: Dict[int, asyncio.Lock] = {}
    
    def get_device_lock(self, device_id: int) -> asyncio.Lock:
        """获取设备锁"""
        if device_id not in self.device_locks:
            self.device_locks[device_id] = asyncio.Lock()
        return self.device_locks[device_id]
    
    async def apply_group_delay_before_send(self, device_id: int, task_id: int, delay_group_ms: int) -> bool:
        """
        在发送任务前应用分组延迟
        
        Args:
            device_id: 设备ID
            task_id: 任务ID
            delay_group_ms: 分组延迟（毫秒）
            
        Returns:
            bool: True表示可以发送任务，False表示设备忙碌
        """
        if delay_group_ms <= 0:
            return True  # 没有分组延迟，直接发送
        
        # 使用设备锁确保串行处理
        lock = self.get_device_lock(device_id)
        
        # 非阻塞检查锁状态
        if lock.locked():
            logger.info(f"⚠️ 设备{device_id} 正在处理其他任务，任务{task_id} 等待中")
            return False
        
        async with lock:
            try:
                # 检查是否需要等待分组延迟
                delay_seconds = delay_group_ms / 1000.0
                wait_time = self._calculate_wait_time(device_id, delay_seconds)
                
                if wait_time > 0:
                    logger.info(f"⏰ 分组延迟: 设备{device_id} 任务{task_id} 等待 {wait_time:.1f}秒")
                    await asyncio.sleep(wait_time)
                    logger.info(f"✅ 分组延迟完成: 设备{device_id} 任务{task_id} 可以发送")
                
                # 记录当前处理的任务
                self.device_current_task[device_id] = task_id
                return True
                
            except Exception as e:
                logger.error(f"❌ 应用分组延迟失败: 设备{device_id}, 任务{task_id}, 错误:{e}")
                return True  # 出错时允许发送，避免阻塞
    
    def _calculate_wait_time(self, device_id: int, delay_seconds: float) -> float:
        """计算需要等待的时间"""
        if device_id not in self.device_last_completion_time:
            return 0.0  # 首次执行，无需等待
        
        current_time = get_beijing_now_naive()
        last_completion = self.device_last_completion_time[device_id]
        elapsed = (current_time - last_completion).total_seconds()
        
        if elapsed < delay_seconds:
            wait_time = delay_seconds - elapsed
            logger.debug(f"⏰ 分组延迟计算: 设备{device_id} 需要等待 {wait_time:.1f}秒 (已过{elapsed:.1f}秒, 需要{delay_seconds}秒)")
            return wait_time
        else:
            logger.debug(f"⏰ 分组延迟计算: 设备{device_id} 无需等待 (已过{elapsed:.1f}秒 >= {delay_seconds}秒)")
            return 0.0
    
    def mark_task_completed(self, device_id: int, task_id: int):
        """标记任务完成"""
        try:
            # 检查是否是当前处理的任务
            current_task = self.device_current_task.get(device_id)
            if current_task != task_id:
                logger.warning(f"⚠️ 任务完成标记异常: 设备{device_id} 当前任务{current_task}, 完成任务{task_id}")
            
            # 记录完成时间
            self.device_last_completion_time[device_id] = get_beijing_now_naive()
            
            # 清除当前任务
            self.device_current_task[device_id] = None
            
            logger.info(f"✅ 任务完成: 设备{device_id} 任务{task_id} 完成时间已记录")
            
        except Exception as e:
            logger.error(f"❌ 标记任务完成失败: 设备{device_id}, 任务{task_id}, 错误:{e}")
    
    def is_device_busy(self, device_id: int) -> bool:
        """检查设备是否忙碌"""
        return self.device_current_task.get(device_id) is not None
    
    def get_device_status(self, device_id: int) -> dict:
        """获取设备状态"""
        return {
            'device_id': device_id,
            'current_task': self.device_current_task.get(device_id),
            'last_completion_time': self.device_last_completion_time.get(device_id),
            'is_busy': self.is_device_busy(device_id)
        }
    
    def clear_device_status(self, device_id: int):
        """清除设备状态（设备离线时调用）"""
        self.device_last_completion_time.pop(device_id, None)
        self.device_current_task.pop(device_id, None)
        logger.info(f"🧹 清除设备{device_id}状态")

# 创建全局实例
simple_group_delay_manager = SimpleGroupDelayManager()
