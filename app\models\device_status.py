from sqlalchemy import Column, <PERSON>te<PERSON>, <PERSON>olean, DateTime, String, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from app.db import Base

class DeviceStatus(Base):
    __tablename__ = "device_status"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), unique=True)
    is_online = Column(Boolean, default=False)
    status = Column(String(32), default='offline')  # online/offline/busy
    current_task_id = Column(Integer, nullable=True)
    last_heartbeat = Column(DateTime, nullable=True)
    last_update = Column(DateTime, default=datetime.utcnow)  # 添加last_update字段
    # account_status = Column(String(64), nullable=True)

    device = relationship("Device", back_populates="status")
