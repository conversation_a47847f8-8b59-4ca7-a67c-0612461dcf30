#!/usr/bin/env python3
"""
简化的分组延迟测试
直接模拟任务完成消息来测试分组延迟逻辑
"""

import asyncio
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_like_task(task_name, group_id, delay_group_sec=3, delay_like_sec=1):
    """创建点赞任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "5139005100786544",
            "blogger_id": "5136084399294965"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def simulate_task_completion(task_id, device_id):
    """模拟任务完成"""
    try:
        import sys
        sys.path.append('.')
        from app.websocket.ws_manager import ConnectionManager
        
        manager = ConnectionManager()
        
        # 模拟任务完成数据
        completion_data = {
            'task_id': task_id,
            'device_id': device_id,
            'status': 'success',
            'result': f'模拟任务{task_id}完成',
            'timestamp': time.time()
        }
        
        log_with_time(f"🔄 模拟任务 {task_id} 完成...")
        
        # 直接调用任务完成处理方法
        await manager._handle_task_completion(f'device_{device_id}', completion_data)
        
        # 调用下一任务处理（包含分组延迟）
        await manager._process_next_task(f'device_{device_id}', completion_data)
        
        log_with_time(f"✅ 任务 {task_id} 完成处理结束")
        
    except Exception as e:
        log_with_time(f"❌ 模拟任务完成失败: {e}")

def get_actual_device_id(task_id):
    """获取任务实际分配的设备ID"""
    try:
        import sys
        sys.path.append('.')
        from app.db import get_db
        from app.models.task import TaskQueue

        db = next(get_db())
        try:
            task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
            if task_queue:
                return task_queue.device_id
            return None
        finally:
            db.close()
    except Exception as e:
        log_with_time(f"❌ 获取设备ID失败: {e}")
        return None

async def test_group_delay_logic():
    """测试分组延迟逻辑"""
    log_with_time("=== 分组延迟逻辑测试 ===")
    
    group_id = 1
    delay_group = 3  # 3秒分组延迟
    
    log_with_time(f"📱 使用分组ID: {group_id}")
    log_with_time(f"⏰ 设置分组延迟: {delay_group}秒")
    
    # 创建两个任务
    log_with_time("🚀 创建第一个任务...")
    task1 = create_like_task("延迟逻辑测试1", group_id, delay_group, 1)
    if not task1:
        return
    
    log_with_time("🚀 创建第二个任务...")
    task2 = create_like_task("延迟逻辑测试2", group_id, delay_group, 1)
    if not task2:
        return
    
    # 等待任务创建完成
    await asyncio.sleep(2)
    
    # 获取实际的设备ID
    device_id = get_actual_device_id(task1.get('id'))
    log_with_time(f"📱 实际设备ID: {device_id}")
    
    # 模拟第一个任务完成
    log_with_time("📝 模拟第一个任务完成...")
    start_time = time.time()
    await simulate_task_completion(task1.get('id'), device_id)
    
    # 记录第一个任务完成时间
    task1_completion_time = time.time()
    log_with_time(f"⏰ 第一个任务完成时间: {task1_completion_time - start_time:.1f}秒")
    
    # 等待一段时间，观察第二个任务是否立即开始
    log_with_time("⏳ 等待观察第二个任务处理...")
    await asyncio.sleep(5)
    
    # 检查第二个任务状态
    await check_task_status(task2.get('id'))
    
    total_time = time.time() - start_time
    log_with_time(f"📊 总测试时间: {total_time:.1f}秒")

async def check_task_status(task_id):
    """检查任务状态"""
    try:
        import sys
        sys.path.append('.')
        from app.db import get_db
        from app.models.task import Task, TaskQueue
        
        db = next(get_db())
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
            
            if task and task_queue:
                log_with_time(f"📊 任务 {task_id} 状态:")
                log_with_time(f"  主任务状态: {task.status}")
                log_with_time(f"  队列状态: {task_queue.status}")
                if task_queue.dispatch_time:
                    log_with_time(f"  分发时间: {task_queue.dispatch_time}")
                if task_queue.finish_time:
                    log_with_time(f"  完成时间: {task_queue.finish_time}")
            else:
                log_with_time(f"❌ 未找到任务 {task_id}")
                
        finally:
            db.close()
            
    except Exception as e:
        log_with_time(f"❌ 检查任务状态失败: {e}")

async def test_delay_method_directly():
    """直接测试分组延迟方法"""
    log_with_time("=== 直接测试分组延迟方法 ===")
    
    try:
        import sys
        sys.path.append('.')
        from app.websocket.ws_manager import ConnectionManager
        
        manager = ConnectionManager()
        
        # 测试参数
        device_id = 10
        completed_task_id = 999
        
        log_with_time(f"🧪 测试设备 {device_id} 的分组延迟方法...")
        
        start_time = time.time()
        await manager._apply_group_delay_before_next_task(device_id, completed_task_id)
        end_time = time.time()
        
        delay_time = end_time - start_time
        log_with_time(f"⏰ 分组延迟方法执行时间: {delay_time:.1f}秒")
        
        if delay_time >= 2.5:  # 如果有延迟
            log_with_time("✅ 分组延迟方法工作正常")
        else:
            log_with_time("⚠️ 分组延迟方法可能没有延迟")
            
    except Exception as e:
        log_with_time(f"❌ 测试分组延迟方法失败: {e}")

async def main():
    """主函数"""
    print("=" * 50)
    print("🧪 简化分组延迟测试")
    print("💡 直接测试分组延迟逻辑")
    print("=" * 50)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 30)
    print("🚀 开始测试")
    print("=" * 30)
    
    # 1. 直接测试分组延迟方法
    await test_delay_method_directly()
    
    print("\n" + "-" * 30)
    
    # 2. 测试分组延迟逻辑
    await test_group_delay_logic()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 如果分组延迟正常工作:")
    print("   1. 分组延迟方法应该等待指定时间")
    print("   2. 第二个任务应该在延迟后才开始处理")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
