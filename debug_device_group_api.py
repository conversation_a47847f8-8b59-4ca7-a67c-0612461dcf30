#!/usr/bin/env python3
"""
调试设备分组API问题
"""

import requests
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import sessionmaker
from app.db import engine
from app.models.device import Device
from app.models.group import Group, DeviceGroup

def debug_api_call():
    """调试API调用"""
    print("🔍 调试设备分组API调用...")
    
    try:
        # 1. 获取设备和分组数据
        devices_response = requests.get("http://localhost:8000/devices/", timeout=10)
        groups_response = requests.get("http://localhost:8000/groups/", timeout=10)
        
        devices = devices_response.json()
        groups = groups_response.json()
        
        print(f"   设备数量: {len(devices)}")
        print(f"   分组数量: {len(groups)}")
        
        # 2. 找一个未分组的设备
        unassigned_device = None
        for device in devices:
            if device.get('group_id') is None:
                unassigned_device = device
                break
                
        if not unassigned_device:
            print("❌ 没有未分组的设备，创建一个测试设备")
            # 这里可以创建测试设备，但先用现有的
            unassigned_device = devices[0]  # 使用第一个设备
            
        target_group = groups[0] if groups else None
        if not target_group:
            print("❌ 没有分组")
            return
            
        device_id = unassigned_device.get('id')
        group_id = target_group.get('id')
        
        print(f"\n   目标设备: {unassigned_device.get('device_number')} (ID: {device_id})")
        print(f"   当前分组: {unassigned_device.get('group_id')}")
        print(f"   目标分组: {target_group.get('group_name')} (ID: {group_id})")
        
        # 3. 调用API添加设备到分组
        api_data = {
            'device_id': device_id,
            'group_id': group_id
        }
        
        print(f"\n📡 调用API: POST /groups/{group_id}/devices")
        print(f"   请求数据: {json.dumps(api_data, indent=2)}")
        
        response = requests.post(
            f"http://localhost:8000/groups/{group_id}/devices",
            json=api_data,
            timeout=10
        )
        
        print(f"   响应状态: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应文本: {response.text}")
        else:
            print(f"   错误响应: {response.text}")
            
        # 4. 立即检查数据库状态
        print(f"\n🔍 检查数据库状态...")
        check_database_state(device_id, group_id)
        
        # 5. 再次调用API获取设备数据
        print(f"\n📡 重新获取设备数据...")
        updated_response = requests.get("http://localhost:8000/devices/", timeout=10)
        if updated_response.status_code == 200:
            updated_devices = updated_response.json()
            updated_device = next((d for d in updated_devices if d.get('id') == device_id), None)
            
            if updated_device:
                print(f"   API返回的设备分组: {updated_device.get('group_id')}")
            else:
                print("   ❌ API中找不到设备")
        
    except Exception as e:
        print(f"❌ API调试异常: {e}")
        import traceback
        traceback.print_exc()

def check_database_state(device_id, group_id):
    """检查数据库状态"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查设备表
        device = db.query(Device).filter(Device.id == device_id).first()
        if device:
            print(f"   设备表中的group_id: {device.group_id}")
        else:
            print(f"   ❌ 设备表中找不到设备ID {device_id}")
            
        # 检查DeviceGroup表
        device_group = db.query(DeviceGroup).filter(
            DeviceGroup.device_id == device_id,
            DeviceGroup.group_id == group_id
        ).first()
        
        if device_group:
            print(f"   DeviceGroup表中有记录: ID={device_group.id}")
        else:
            print(f"   ❌ DeviceGroup表中没有记录")
            
        # 检查所有DeviceGroup记录
        all_device_groups = db.query(DeviceGroup).all()
        print(f"   DeviceGroup表总记录数: {len(all_device_groups)}")
        
        for dg in all_device_groups:
            print(f"     记录: 设备ID={dg.device_id}, 分组ID={dg.group_id}")
            
    except Exception as e:
        print(f"❌ 数据库检查异常: {e}")
        
    finally:
        db.close()

def check_api_route():
    """检查API路由实现"""
    print("\n📋 检查API路由实现...")
    
    try:
        # 读取路由文件
        with open('../app/routes/group.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找添加设备到分组的路由
        lines = content.split('\n')
        in_route = False
        route_lines = []
        
        for i, line in enumerate(lines):
            if '@router.post("/{group_id}/devices")' in line:
                in_route = True
                route_lines.append(f"{i+1}: {line}")
            elif in_route:
                route_lines.append(f"{i+1}: {line}")
                if line.startswith('@router.') and '/{group_id}/devices' not in line:
                    break
                if line.startswith('def ') and 'add_device_to_existing_group' not in line:
                    break
                    
        if route_lines:
            print("   添加设备到分组的路由:")
            for line in route_lines[:15]:  # 显示前15行
                print(f"     {line}")
                
    except Exception as e:
        print(f"❌ 检查路由异常: {e}")

def test_service_directly():
    """直接测试服务方法"""
    print("\n🔧 直接测试服务方法...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        from app.services.group import add_device_to_group
        from app.schemas.group import GroupDevice
        
        # 找一个设备和分组
        device = db.query(Device).first()
        group = db.query(Group).first()
        
        if not device or not group:
            print("❌ 没有设备或分组")
            return
            
        device_id = device.id
        group_id = group.id
        
        print(f"   测试设备: {device.device_number} (ID: {device_id})")
        print(f"   当前分组: {device.group_id}")
        print(f"   目标分组: {group.group_name} (ID: {group_id})")
        
        # 创建GroupDevice对象
        group_device = GroupDevice(device_id=device_id, group_id=group_id)
        print(f"   GroupDevice对象: {group_device}")
        
        # 调用服务方法
        result = add_device_to_group(db, group_device)
        
        if result:
            print(f"✅ 服务方法返回: {result}")
            
            # 检查设备更新
            updated_device = db.query(Device).filter(Device.id == device_id).first()
            print(f"   设备更新后的group_id: {updated_device.group_id}")
            
        else:
            print("❌ 服务方法返回None")
            
    except Exception as e:
        print(f"❌ 直接测试服务异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

def main():
    """主函数"""
    print("🔍 深度调试设备分组问题...")
    print("="*60)
    
    # 1. 检查API路由
    check_api_route()
    
    # 2. 直接测试服务
    test_service_directly()
    
    # 3. 调试API调用
    debug_api_call()

if __name__ == "__main__":
    main()
