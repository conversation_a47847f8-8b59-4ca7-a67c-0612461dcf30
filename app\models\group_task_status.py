from datetime import datetime
from app.db import Base
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

class GroupTaskStatus(Base):
    """组任务状态跟踪模型"""
    __tablename__ = 'group_task_status'
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True)
    group_id = Column(Integer, ForeignKey('groups.id'), nullable=False)
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    device_id = Column(Integer, ForeignKey('devices.id'), nullable=False)
    total_devices = Column(Integer, default=0)
    completed_devices = Column(Integer, default=0)
    status = Column(String(20), default='pending')  # pending/running/done/failed
    create_time = Column(DateTime, default=datetime.utcnow)
    # 临时注释掉start_time字段以匹配当前数据库
    # start_time = Column(DateTime)
    finish_time = Column(DateTime)

    group = relationship("Group", back_populates="task_statuses")
    task = relationship("Task")
    
    def __repr__(self):
        return f"<GroupTaskStatus(group_id={self.group_id}, task_id={self.task_id}, status={self.status})>"
