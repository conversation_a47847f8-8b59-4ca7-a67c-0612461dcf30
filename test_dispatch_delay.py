#!/usr/bin/env python3
"""
测试分发延迟功能
验证分组延迟是在分发阶段控制，而不是执行阶段
"""

import time
import requests
import threading
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def test_api():
    """测试API连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_devices():
    """获取设备列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        if response.status_code == 200:
            return response.json()
        return []
    except:
        return []

def get_groups():
    """获取分组列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/groups", timeout=10)
        if response.status_code == 200:
            return response.json()
        return []
    except:
        return []

def create_task_with_timing(task_name, target_scope, target_id, delay_group_sec):
    """创建任务并记录时间"""
    start_time = time.time()
    
    try:
        task_data = {
            "task_type": "sign",
            "parameters": {"count": 1, "test_name": task_name},
            "target_scope": target_scope,
            "target_id": target_id,
            "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
            "delay_like": 500  # 0.5秒操作延迟
        }
        
        log_with_time(f"🚀 开始创建任务: {task_name}")
        
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        
        end_time = time.time()
        creation_time = (end_time - start_time) * 1000
        
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ 任务 {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task, creation_time
        else:
            log_with_time(f"❌ 任务 {task_name} 创建失败: {response.status_code}")
            return None, creation_time
    except Exception as e:
        end_time = time.time()
        creation_time = (end_time - start_time) * 1000
        log_with_time(f"❌ 创建任务 {task_name} 失败: {e}")
        return None, creation_time

def test_dispatch_delay_single_device():
    """测试单设备的分发延迟"""
    log_with_time("=== 测试单设备分发延迟 ===")
    
    devices = get_devices()
    if not devices:
        log_with_time("❌ 没有可用设备")
        return False
    
    device = devices[0]
    device_id = device.get('id')
    device_number = device.get('device_number')
    
    log_with_time(f"📱 使用设备: {device_number} (ID: {device_id})")
    log_with_time(f"⏰ 设置分组延迟: 3秒")
    
    # 连续创建3个任务到同一设备
    tasks = []
    creation_times = []
    
    for i in range(3):
        task_name = f"单设备测试任务{i+1}"
        task, creation_time = create_task_with_timing(task_name, "single", device_id, 3.0)
        tasks.append(task)
        creation_times.append(creation_time)
        
        # 稍微间隔一下，避免同时创建
        time.sleep(0.1)
    
    # 分析结果
    success_count = sum(1 for task in tasks if task is not None)
    avg_creation_time = sum(creation_times) / len(creation_times)
    
    log_with_time(f"📊 结果: {success_count}/3 任务创建成功")
    log_with_time(f"📊 平均创建时间: {avg_creation_time:.1f}ms")
    
    if success_count > 0:
        log_with_time("✅ 单设备分发延迟测试完成")
        log_with_time("💡 观察要点:")
        log_with_time("   - 第1个任务应该立即分发")
        log_with_time("   - 第2个任务应该等待3秒后分发")
        log_with_time("   - 第3个任务应该再等待3秒后分发")
        return True
    else:
        log_with_time("❌ 单设备分发延迟测试失败")
        return False

def test_dispatch_delay_group():
    """测试分组的分发延迟"""
    log_with_time("=== 测试分组分发延迟 ===")
    
    groups = get_groups()
    if not groups:
        log_with_time("❌ 没有可用分组")
        return False
    
    group = groups[0]
    group_id = group.get('id')
    group_name = group.get('group_name')
    
    log_with_time(f"👥 使用分组: {group_name} (ID: {group_id})")
    log_with_time(f"⏰ 设置分组延迟: 2秒")
    
    # 连续创建2个任务到同一分组
    tasks = []
    creation_times = []
    
    for i in range(2):
        task_name = f"分组测试任务{i+1}"
        task, creation_time = create_task_with_timing(task_name, "group", group_id, 2.0)
        tasks.append(task)
        creation_times.append(creation_time)
        
        time.sleep(0.1)
    
    # 分析结果
    success_count = sum(1 for task in tasks if task is not None)
    avg_creation_time = sum(creation_times) / len(creation_times)
    
    log_with_time(f"📊 结果: {success_count}/2 任务创建成功")
    log_with_time(f"📊 平均创建时间: {avg_creation_time:.1f}ms")
    
    if success_count > 0:
        log_with_time("✅ 分组分发延迟测试完成")
        log_with_time("💡 观察要点:")
        log_with_time("   - 分组内每个设备都有独立的分发延迟")
        log_with_time("   - 不同设备的任务可以并行分发")
        return True
    else:
        log_with_time("❌ 分组分发延迟测试失败")
        return False

def test_parallel_dispatch():
    """测试并行分发效果"""
    log_with_time("=== 测试并行分发效果 ===")
    
    devices = get_devices()
    if len(devices) < 2:
        log_with_time("⚠️ 设备数量不足，跳过并行测试")
        return True
    
    # 选择两个不同设备
    device1 = devices[0]
    device2 = devices[1]
    
    log_with_time(f"📱 设备1: {device1.get('device_number')} (ID: {device1.get('id')})")
    log_with_time(f"📱 设备2: {device2.get('device_number')} (ID: {device2.get('id')})")
    
    # 并行创建任务
    results = {}
    
    def create_task_thread(device_id, task_name):
        task, creation_time = create_task_with_timing(task_name, "single", device_id, 3.0)
        results[task_name] = (task, creation_time)
    
    # 启动并行线程
    start_time = time.time()
    
    thread1 = threading.Thread(target=create_task_thread, args=(device1.get('id'), "并行任务1"))
    thread2 = threading.Thread(target=create_task_thread, args=(device2.get('id'), "并行任务2"))
    
    thread1.start()
    thread2.start()
    
    thread1.join()
    thread2.join()
    
    end_time = time.time()
    total_time = (end_time - start_time) * 1000
    
    # 分析结果
    success_count = sum(1 for task, _ in results.values() if task is not None)
    
    log_with_time(f"📊 并行创建结果: {success_count}/2 任务成功")
    log_with_time(f"📊 总耗时: {total_time:.1f}ms")
    
    if success_count > 0:
        log_with_time("✅ 并行分发测试完成")
        log_with_time("💡 观察要点:")
        log_with_time("   - 不同设备的任务应该能并行分发")
        log_with_time("   - 总耗时应该接近单个任务的创建时间")
        return True
    else:
        log_with_time("❌ 并行分发测试失败")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 分发延迟功能测试")
    print("=" * 60)
    
    # 1. 测试API连接
    if not test_api():
        log_with_time("❌ API连接失败，请确保后端服务正在运行")
        return
    
    log_with_time("✅ API连接正常")
    
    # 2. 获取系统信息
    devices = get_devices()
    groups = get_groups()
    
    log_with_time(f"📊 系统状态: 设备数量={len(devices)}, 分组数量={len(groups)}")
    
    print("\n" + "=" * 40)
    print("🚀 开始分发延迟测试")
    print("=" * 40)
    
    # 3. 执行测试
    test_results = []
    
    if devices:
        test_results.append(test_dispatch_delay_single_device())
        print()
    
    if groups:
        test_results.append(test_dispatch_delay_group())
        print()
    
    if len(devices) >= 2:
        test_results.append(test_parallel_dispatch())
    
    # 4. 测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    
    success_count = sum(test_results)
    total_tests = len(test_results)
    
    if success_count == total_tests:
        log_with_time("✅ 所有测试通过")
        print("\n💡 分发延迟优化效果:")
        print("   1. 分组延迟在分发阶段控制")
        print("   2. 任务创建立即返回")
        print("   3. 延迟不阻塞API响应")
        print("   4. 不同设备真正并行")
    else:
        log_with_time(f"⚠️ {success_count}/{total_tests} 测试通过")
    
    print("\n📝 查看详细日志:")
    print("   journalctl -u wb-system -f")
    print("   关键词: '分发延迟控制'")
    print("=" * 60)

if __name__ == "__main__":
    main()
