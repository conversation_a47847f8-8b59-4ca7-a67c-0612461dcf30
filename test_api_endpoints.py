#!/usr/bin/env python3
"""
测试API端点
验证所有API端点是否正常工作
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=5)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=5)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, timeout=5)
        elif method.upper() == "DELETE":
            response = requests.delete(url, timeout=5)
        else:
            print(f"❌ 不支持的方法: {method}")
            return False
            
        print(f"{method.upper()} {endpoint}")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ 成功")
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"   返回列表，长度: {len(data)}")
                elif isinstance(data, dict):
                    print(f"   返回对象，键: {list(data.keys())}")
                else:
                    print(f"   返回数据: {data}")
            except:
                print(f"   返回文本: {response.text[:100]}...")
        elif response.status_code == 405:
            print(f"   ❌ 方法不允许 (405)")
        elif response.status_code == 404:
            print(f"   ❌ 端点不存在 (404)")
        elif response.status_code == 422:
            print(f"   ❌ 请求参数错误 (422)")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                pass
        else:
            print(f"   ❌ 错误: {response.status_code}")
            print(f"   响应: {response.text[:200]}...")
            
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: {url}")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时: {url}")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试API端点...")
    print(f"基础URL: {BASE_URL}")
    print("="*60)
    
    # 测试基本连接
    print("\n📡 测试基本连接...")
    test_endpoint("GET", "/")
    
    # 测试设备端点
    print("\n📱 测试设备端点...")
    endpoints = [
        ("GET", "/devices/"),
        ("GET", "/devices/1"),
    ]
    
    device_success = 0
    for method, endpoint in endpoints:
        if test_endpoint(method, endpoint):
            device_success += 1
        print()
        
    # 测试任务端点
    print("\n📋 测试任务端点...")
    task_endpoints = [
        ("GET", "/tasks/"),
        ("GET", "/tasks/1"),
        ("GET", "/tasks/1/status"),
    ]
    
    task_success = 0
    for method, endpoint in task_endpoints:
        if test_endpoint(method, endpoint):
            task_success += 1
        print()
        
    # 测试分组端点
    print("\n👥 测试分组端点...")
    group_endpoints = [
        ("GET", "/groups/"),
        ("GET", "/groups/1"),
    ]
    
    group_success = 0
    for method, endpoint in group_endpoints:
        if test_endpoint(method, endpoint):
            group_success += 1
        print()
        
    # 测试创建任务（如果设备存在）
    print("\n🔧 测试创建任务...")
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1},
        "target_scope": "single",
        "target_id": 1,
        "delay_group": 1000,
        "delay_like": 500
    }
    
    create_success = test_endpoint("POST", "/tasks/", task_data)
    print()
    
    # 总结
    print("="*60)
    print("📊 测试总结:")
    print(f"   设备端点: {device_success}/{len(endpoints)} 成功")
    print(f"   任务端点: {task_success}/{len(task_endpoints)} 成功")
    print(f"   分组端点: {group_success}/{len(group_endpoints)} 成功")
    print(f"   创建任务: {'✅ 成功' if create_success else '❌ 失败'}")
    
    total_tests = len(endpoints) + len(task_endpoints) + len(group_endpoints) + 1
    total_success = device_success + task_success + group_success + (1 if create_success else 0)
    
    print(f"\n🎯 总体结果: {total_success}/{total_tests} 测试通过")
    
    if total_success == total_tests:
        print("🎉 所有API端点测试通过！")
    else:
        print("⚠️ 部分API端点存在问题，请检查后端配置")
        
    # 提供修复建议
    if task_success == 0:
        print("\n💡 任务API修复建议:")
        print("1. 确保 app/routes/task.py 中有 GET /tasks/ 端点")
        print("2. 确保 app/crud.py 中有 get_tasks() 方法")
        print("3. 重启后端服务")

if __name__ == "__main__":
    main()
