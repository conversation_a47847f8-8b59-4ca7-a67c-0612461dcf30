#!/usr/bin/env python3
"""
去重功能测试
测试调度器是否会重复发送任务给同一设备
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class DeduplicationTestDevice:
    """去重测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []
        self.task_counts = {}  # 统计每个任务收到的次数
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                task_id = data.get('task_id')
                
                # 统计任务接收次数
                if task_id in self.task_counts:
                    self.task_counts[task_id] += 1
                    log_with_time(f"⚠️ 设备 {self.device_number} 重复收到任务 {task_id} (第{self.task_counts[task_id]}次)")
                else:
                    self.task_counts[task_id] = 1
                    log_with_time(f"📨 设备 {self.device_number} 首次收到任务 {task_id}")
                
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                pass  # 忽略ack消息
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_dedup_test_task(task_name, group_id, delay_group_sec=3):
    """创建去重测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": f"dedup_test_{int(time.time())}"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 秒转毫秒
        "delay_like": 500  # 0.5秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_deduplication():
    """测试去重功能"""
    log_with_time("=== 去重功能测试 ===")
    
    # 创建测试设备
    device = DeduplicationTestDevice("ceshi212")  # 设备12，属于分组5
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 2. 创建测试任务
        delay_seconds = 3  # 3秒分组延迟
        log_with_time(f"🚀 创建去重测试任务（{delay_seconds}秒延迟）")
        
        test_start_time = time.time()
        
        # 创建2个任务
        task1 = create_dedup_test_task("去重测试1", 5, delay_seconds)
        if not task1:
            log_with_time("❌ 任务1创建失败")
            return
        
        await asyncio.sleep(1)
        
        task2 = create_dedup_test_task("去重测试2", 5, delay_seconds)
        if not task2:
            log_with_time("❌ 任务2创建失败")
            return
        
        # 3. 等待任务执行
        expected_time = 2 * (1 + delay_seconds)  # 2个任务 * (1秒执行 + 3秒延迟)
        log_with_time(f"⏳ 等待任务执行（预计需要约{expected_time}秒）...")
        await asyncio.sleep(expected_time + 5)  # 额外等待5秒
        
        # 4. 分析去重效果
        log_with_time("📊 分析去重效果...")
        
        # 检查重复任务
        duplicate_tasks = []
        for task_id, count in device.task_counts.items():
            if count > 1:
                duplicate_tasks.append((task_id, count))
        
        if duplicate_tasks:
            log_with_time("❌ 发现重复任务:")
            for task_id, count in duplicate_tasks:
                log_with_time(f"   任务{task_id}: 收到{count}次")
            log_with_time("⚠️ 去重功能有问题！")
        else:
            log_with_time("✅ 没有发现重复任务，去重功能正常！")
        
        # 检查任务间隔
        if len(device.task_times) >= 2:
            log_with_time("任务接收时间间隔:")
            
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期
                expected_min = delay_seconds + 0.5  # 3 + 0.5 = 3.5秒
                expected_max = delay_seconds + 2.0  # 3 + 2.0 = 5秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒)")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常 ({interval:.1f}秒)")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务总数: {len(device.received_tasks)}")
        log_with_time(f"📊 唯一任务数: {len(device.task_counts)}")
        
        # 显示任务统计
        log_with_time("📋 任务接收统计:")
        for task_id, count in device.task_counts.items():
            status = "✅ 正常" if count == 1 else f"❌ 重复{count}次"
            log_with_time(f"   任务{task_id}: {status}")
        
        # 总结
        if not duplicate_tasks and len(device.received_tasks) == 2:
            log_with_time("🎉 去重功能测试通过！")
        else:
            log_with_time("⚠️ 去重功能测试失败")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 去重功能测试")
    print("💡 测试调度器是否会重复发送任务给同一设备")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 50)
    print("🚀 开始测试")
    print("=" * 50)
    
    # 去重功能测试
    await test_deduplication()
    
    print("\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 测试要点:")
    print("   1. 每个任务应该只收到一次")
    print("   2. 不应该有重复发送")
    print("   3. 分组延迟应该正常工作")
    print("   4. 任务按顺序执行")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
