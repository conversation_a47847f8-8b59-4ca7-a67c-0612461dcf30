#!/usr/bin/env python3
"""
恢复inex任务类型
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from sqlalchemy import text

def restore_inex_task():
    """恢复被修改的inex任务"""
    print("恢复inex任务类型...")
    
    try:
        db = next(get_db())
        
        # 查找ID为3的任务（之前被修改的那个）
        result = db.execute(text("SELECT id, task_type FROM tasks WHERE id = 3"))
        task = result.fetchone()
        
        if task:
            print(f"找到任务ID 3，当前类型: {task[1]}")
            
            # 恢复为inex
            db.execute(text("UPDATE tasks SET task_type = 'inex' WHERE id = 3"))
            db.commit()
            print("✅ 已恢复任务ID 3的类型为 'inex'")
        else:
            print("❌ 未找到任务ID 3")
            
        # 验证所有任务类型
        print("\n当前所有任务类型:")
        result = db.execute(text("SELECT id, task_type FROM tasks ORDER BY id"))
        tasks = result.fetchall()
        
        for task_id, task_type in tasks:
            print(f"  任务ID {task_id}: {task_type}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inex_task():
    """测试inex任务查询"""
    print("\n测试inex任务查询...")
    
    try:
        from app import crud
        db = next(get_db())
        
        tasks = crud.get_tasks(db)
        print(f"✅ 成功获取 {len(tasks)} 个任务")
        
        # 查找inex任务
        inex_tasks = [t for t in tasks if t.task_type == 'inex']
        print(f"找到 {len(inex_tasks)} 个inex任务")
        
        for task in inex_tasks:
            print(f"  inex任务: ID={task.id}, 状态={task.status}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔄 恢复inex任务类型...")
    print("="*50)
    
    if restore_inex_task():
        test_inex_task()
        print("\n" + "="*50)
        print("🎉 inex任务类型已恢复！")
    else:
        print("\n❌ 恢复失败")

if __name__ == "__main__":
    main()
