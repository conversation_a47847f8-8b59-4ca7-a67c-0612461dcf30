#!/usr/bin/env python3
"""
最终系统测试
"""

import requests
import json

def test_all_features():
    """测试所有新功能"""
    print("🧪 最终系统功能测试...")
    print("="*60)
    
    base_url = "http://localhost:8000"
    
    # 1. 测试设备API包含新字段
    print("1. 测试设备API新字段...")
    try:
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            if devices:
                device = devices[0]
                required_fields = ['current_task_id', 'current_task_type', 'group_id']
                missing_fields = [field for field in required_fields if field not in device]
                
                if not missing_fields:
                    print("✅ 设备API包含所有新字段")
                    print(f"   示例设备: {device.get('device_number')}")
                    print(f"   分组ID: {device.get('group_id')}")
                    print(f"   当前任务ID: {device.get('current_task_id')}")
                    print(f"   当前任务类型: {device.get('current_task_type')}")
                else:
                    print(f"❌ 设备API缺少字段: {missing_fields}")
                    return False
            else:
                print("❌ 没有设备数据")
                return False
        else:
            print(f"❌ 设备API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设备API测试异常: {e}")
        return False
    
    # 2. 测试任务同步API
    print("\n2. 测试任务同步API...")
    try:
        # 测试仪表板统计
        response = requests.get(f"{base_url}/task-sync/dashboard-stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 仪表板统计API正常")
            print(f"   任务统计: {stats.get('tasks')}")
            print(f"   设备统计: {stats.get('devices')}")
            print(f"   执行中任务: {stats.get('running_tasks')}")
        else:
            print(f"❌ 仪表板统计API失败: {response.status_code}")
            return False
            
        # 测试同步所有设备
        response = requests.post(f"{base_url}/task-sync/sync-all-devices", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("✅ 设备同步API正常")
            print(f"   同步结果: {result.get('result')}")
        else:
            print(f"❌ 设备同步API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务同步API测试异常: {e}")
        return False
    
    # 3. 测试设备任务队列API
    print("\n3. 测试设备任务队列API...")
    try:
        # 获取第一个设备的任务队列
        devices_response = requests.get(f"{base_url}/devices/", timeout=10)
        if devices_response.status_code == 200:
            devices = devices_response.json()
            if devices:
                device_id = devices[0].get('id')
                response = requests.get(f"{base_url}/task-sync/device-queue/{device_id}", timeout=10)
                if response.status_code == 200:
                    queue_data = response.json()
                    print("✅ 设备任务队列API正常")
                    print(f"   设备ID: {queue_data.get('device_id')}")
                    print(f"   队列长度: {queue_data.get('count')}")
                else:
                    print(f"❌ 设备任务队列API失败: {response.status_code}")
                    return False
            else:
                print("❌ 没有设备数据")
                return False
        else:
            print(f"❌ 获取设备列表失败: {devices_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设备任务队列API测试异常: {e}")
        return False
    
    # 4. 测试分组任务摘要API
    print("\n4. 测试分组任务摘要API...")
    try:
        groups_response = requests.get(f"{base_url}/groups/", timeout=10)
        if groups_response.status_code == 200:
            groups = groups_response.json()
            if groups:
                group_id = groups[0].get('id')
                response = requests.get(f"{base_url}/task-sync/group-summary/{group_id}", timeout=10)
                if response.status_code == 200:
                    summary = response.json()
                    print("✅ 分组任务摘要API正常")
                    print(f"   分组名称: {summary.get('group_name')}")
                    print(f"   设备数量: {summary.get('device_count')}")
                    print(f"   任务摘要: {summary.get('task_summary')}")
                else:
                    print(f"❌ 分组任务摘要API失败: {response.status_code}")
                    return False
            else:
                print("❌ 没有分组数据")
                return False
        else:
            print(f"❌ 获取分组列表失败: {groups_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 分组任务摘要API测试异常: {e}")
        return False
    
    # 5. 测试创建任务和状态同步
    print("\n5. 测试任务创建和状态同步...")
    try:
        # 创建测试任务
        task_data = {
            "task_type": "inex",
            "parameters": {
                "user_id": "final_test_user",
                "count": 1
            },
            "target_scope": "single",
            "target_id": 1,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            print(f"✅ 任务创建成功 (ID: {task_id})")
            
            # 同步任务状态
            sync_response = requests.post(f"{base_url}/task-sync/sync-task/{task_id}", timeout=10)
            if sync_response.status_code == 200:
                print("✅ 任务状态同步成功")
            else:
                print(f"❌ 任务状态同步失败: {sync_response.status_code}")
                return False
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 任务创建和同步测试异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 最终系统功能验证...")
    print("="*60)
    
    if test_all_features():
        print("\n" + "="*60)
        print("🎉 所有功能测试通过！")
        
        print("\n✅ 已实现的功能:")
        print("1. 设备当前任务显示 - 数据库字段已添加")
        print("2. 任务状态同步 - API已实现")
        print("3. 设备任务队列 - 可以查看设备任务历史")
        print("4. 分组任务摘要 - 可以查看分组任务统计")
        print("5. 仪表板统计 - 实时统计信息")
        
        print("\n🚀 系统架构优化:")
        print("- tasks表：显示任务概览和状态")
        print("- task_queue表：控制实际执行和详细状态")
        print("- 设备表：显示当前执行任务信息")
        print("- 同步机制：保持数据一致性")
        
        print("\n📱 前端功能:")
        print("- 设备管理：显示当前任务、任务队列、操作日志")
        print("- 分组管理：显示分组任务摘要")
        print("- 任务管理：实时状态同步")
        print("- 日志系统：完整的操作记录")
        
        print("\n💡 使用说明:")
        print("1. 后端服务器已启动并包含所有新API")
        print("2. 数据库已迁移，包含新字段")
        print("3. 前端设备管理器已更新，支持:")
        print("   - 当前任务显示")
        print("   - 任务队列查看")
        print("   - 状态同步")
        print("   - 操作日志")
        print("4. 可以正常创建任务、分配到设备、查看执行状态")
        
        print("\n🔧 技术实现:")
        print("- 数据库字段：current_task_id, current_task_type")
        print("- 新增API：/task-sync/* 系列接口")
        print("- 前端组件：增强的设备管理器")
        print("- 同步机制：自动和手动状态同步")
        
    else:
        print("\n❌ 部分功能测试失败")
        print("请检查后端服务器和数据库状态")

if __name__ == "__main__":
    main()
