# 客户端心跳优化建议

## 🔍 当前问题分析

从日志可以看出，设备 `devi201` 发送的心跳消息格式不正确：

### 当前客户端发送的心跳格式：
1. **ISO时间戳字符串**：`2025-06-06T05:16:20.799Z`
2. **Date对象字符串**：`new Date-Fri Jun 06 2025 13:16:21 GMT+0800 (GMT+08:00)`
3. **二进制消息**：未知格式的二进制数据

### 问题：
- 这些都不是有效的JSON对象
- 服务端无法识别为心跳消息
- 导致任务状态检查一直显示 `running`

## 🛠️ 服务端修复

我已经修复了服务端，现在支持以下心跳格式：

### 1. **标准JSON心跳**（推荐）
```json
{
    "type": "heartbeat",
    "timestamp": "2025-06-06T05:16:20.799Z",
    "device_number": "devi201"
}
```

### 2. **ISO时间戳字符串**（兼容）
```
2025-06-06T05:16:20.799Z
```

### 3. **Date对象字符串**（兼容）
```
new Date-Fri Jun 06 2025 13:16:21 GMT+0800 (GMT+08:00)
```

### 4. **数字时间戳**（兼容）
```
1733472980799
```

## 📱 客户端优化建议

### 1. **推荐的心跳实现**

```javascript
// 优化后的心跳发送函数
function sendHeartbeat() {
    const heartbeatMessage = {
        type: "heartbeat",
        timestamp: new Date().toISOString(),
        device_number: deviceNumber,
        // 可选：添加设备状态信息
        status: {
            battery: getBatteryLevel(),
            signal: getSignalStrength(),
            memory: getMemoryUsage()
        }
    };
    
    try {
        ws.send(JSON.stringify(heartbeatMessage));
        console.log("心跳发送成功:", heartbeatMessage.timestamp);
    } catch (error) {
        console.error("心跳发送失败:", error);
    }
}

// 启动心跳
setInterval(sendHeartbeat, 3000); // 每3秒发送一次
```

### 2. **处理心跳确认**

```javascript
ws.onmessage = function(event) {
    try {
        const data = JSON.parse(event.data);
        
        if (data.type === "heartbeat_ack") {
            console.log("收到心跳确认:", data);
            
            // 根据服务器识别的格式调整发送方式
            switch(data.received_format) {
                case "iso_timestamp":
                    console.log("服务器识别为ISO时间戳格式");
                    break;
                case "date_string":
                    console.log("服务器识别为Date字符串格式");
                    break;
                case "numeric_timestamp":
                    console.log("服务器识别为数字时间戳格式");
                    break;
                default:
                    console.log("服务器识别为标准JSON格式");
            }
            
            // 计算延迟
            if (data.latency_ms) {
                console.log("心跳延迟:", data.latency_ms + "ms");
            }
        }
    } catch (error) {
        console.error("解析服务器消息失败:", error);
    }
};
```

### 3. **兼容性心跳实现**

如果无法修改现有的心跳机制，可以使用以下兼容方案：

```javascript
// 方案1：包装现有的心跳函数
function wrapHeartbeat(originalHeartbeatFunction) {
    return function() {
        // 发送标准JSON格式心跳
        const heartbeat = {
            type: "heartbeat",
            timestamp: new Date().toISOString(),
            device_number: deviceNumber
        };
        
        ws.send(JSON.stringify(heartbeat));
        
        // 如果需要，也可以调用原有函数
        // originalHeartbeatFunction();
    };
}

// 方案2：修改现有的startHeartBeat函数
ws.startHeartBeat(
    function () {
        return null; // 不发送额外数据
    }, 
    function () {
        // 返回标准JSON格式而不是时间戳字符串
        return JSON.stringify({
            type: "heartbeat",
            timestamp: new Date().toISOString(),
            device_number: deviceNumber
        });
    }, 
    3000, 
    true
);
```

### 4. **错误处理和重连机制**

```javascript
let heartbeatInterval;
let missedHeartbeats = 0;
const MAX_MISSED_HEARTBEATS = 3;

function startHeartbeat() {
    heartbeatInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
            sendHeartbeat();
        } else {
            missedHeartbeats++;
            console.warn("WebSocket未连接，心跳失败");
            
            if (missedHeartbeats >= MAX_MISSED_HEARTBEATS) {
                console.error("连续心跳失败，尝试重连");
                reconnectWebSocket();
            }
        }
    }, 3000);
}

function stopHeartbeat() {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }
}

function reconnectWebSocket() {
    stopHeartbeat();
    
    // 重连逻辑
    setTimeout(() => {
        initWebSocket();
        startHeartbeat();
    }, 5000);
}

// WebSocket连接成功后启动心跳
ws.onopen = function() {
    console.log("WebSocket连接成功");
    missedHeartbeats = 0;
    startHeartbeat();
};

// WebSocket关闭时停止心跳
ws.onclose = function() {
    console.log("WebSocket连接关闭");
    stopHeartbeat();
};
```

## 🚀 立即修复方案

### 最简单的修复（推荐）

将您现有的心跳代码：

```javascript
ws.startHeartBeat(function () {
    return null;
}, function () {
    return new Date().toISOString();
}, 3000, true);
```

修改为：

```javascript
ws.startHeartBeat(function () {
    return null;
}, function () {
    return JSON.stringify({
        type: "heartbeat",
        timestamp: new Date().toISOString(),
        device_number: "devi201"  // 替换为实际设备号
    });
}, 3000, true);
```

## 📊 预期效果

修复后，您应该看到：

### 修复前的日志：
```
← 收到设备 devi201 文本消息: 2025-06-06T05:16:20.799Z
! 设备 devi201 JSON解析失败: Extra data: line 1 column 5 (char 4)
```

### 修复后的日志：
```
← 设备 devi201 ISO时间戳心跳: 2025-06-06T05:16:20.799Z
✓ 设备 devi201 心跳正常 (上次: 0.1秒前)
```

或者（如果使用标准JSON格式）：
```
← 设备 devi201 心跳
✓ 设备 devi201 心跳正常 (上次: 0.1秒前)
```

## 🎯 总结

1. **服务端已修复** - 现在支持多种心跳格式
2. **客户端建议优化** - 使用标准JSON格式心跳
3. **向后兼容** - 现有的时间戳格式仍然可以工作
4. **任务状态问题解决** - 正确的心跳将解决任务状态检查问题

修复后，任务状态应该能够正常从 `running` 转换为 `done`，下一个任务也能正常分发。
