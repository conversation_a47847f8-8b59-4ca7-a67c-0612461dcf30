# app/schemas/device.py
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional

class OnlineStatusEnum(str, PyEnum):
    online = "online"
    offline = "offline"

class AccountStatusEnum(str, PyEnum):
    normal = "normal"
    not_logged_in = "not_logged_in"
    abnormal = "abnormal"

class DeviceBase(BaseModel):
    device_number: str
    device_ip: Optional[str] = None
    online_status: OnlineStatusEnum
    account_status: AccountStatusEnum
    last_heartbeat: datetime
    group_id: Optional[int] = None
    current_task_id: Optional[int] = None
    current_task_type: Optional[str] = None

class DeviceCreate(BaseModel):
    device_number: str = Field(..., min_length=3, max_length=64, example="DEV-001")
    device_ip: Optional[str] = Field(None, max_length=45, example="*************")
    online_status: Optional[OnlineStatusEnum] = Field("offline")
    account_status: Optional[AccountStatusEnum] = Field("not_logged_in")
    last_heartbeat: Optional[datetime] = Field(None)
    group_id: Optional[int] = Field(None)

    class Config:
        json_schema_extra = {
            "example": {
                "device_number": "DEV-001",
                "device_ip": "*************"
            }
        }

class DeviceUpdate(BaseModel):
    device_number: Optional[str] = Field(None)
    device_ip: Optional[str] = Field(None)
    online_status: Optional[OnlineStatusEnum] = Field(None)
    account_status: Optional[AccountStatusEnum] = Field(None)
    last_heartbeat: Optional[datetime] = Field(None)
    group_id: Optional[int] = Field(None)

class DeviceRead(DeviceBase):
    id: int

    class Config:
        from_attributes = True
