from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from app import crud, schemas, models
from app.db import get_db
from app.websocket import ws_manager
# from app.services.dispatcher import dispatcher  # 已停用，使用task_dispatch_service
# from app.services.scheduler import scheduler  # 已停用，使用优化版调度器
from app.services.task_dispatcher import task_dispatch_service

router = APIRouter()
manager = ws_manager.ConnectionManager()

@router.get("/", response_model=list[schemas.task.TaskOut])
def get_tasks(db: Session = Depends(get_db)):
    """获取所有任务列表"""
    tasks = crud.get_tasks(db)
    return tasks

@router.post("/", response_model=schemas.task.TaskOut)
async def create_task(task: schemas.task.TaskCreate):
    """创建并分发任务 - 使用优化版分发服务"""
    try:
        # 使用新的任务分发服务
        db_task = await task_dispatch_service.create_and_dispatch_task(task)
        return db_task
    except ValueError as e:
        # 验证错误
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 其他错误
        print(f"创建任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.get("/{task_id}", response_model=schemas.task.TaskOut)
def get_task(task_id: int, db: Session = Depends(get_db)):
    """获取任务详情"""
    task = crud.get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.post("/pause-all")
async def pause_all_tasks(db: Session = Depends(get_db)):
    """暂停所有任务"""
    # 使用优化版调度器
    from app.services.optimized_scheduler import optimized_scheduler
    await optimized_scheduler.pause()
    return {"message": "All tasks paused"}

@router.post("/resume-all")
async def resume_all_tasks(db: Session = Depends(get_db)):
    """恢复所有任务"""
    # 使用优化版调度器
    from app.services.optimized_scheduler import optimized_scheduler
    await optimized_scheduler.resume()
    return {"message": "All tasks resumed"}

@router.post("/{task_id}/pause")
async def pause_task(task_id: int, db: Session = Depends(get_db)):
    """暂停单个任务"""
    task = crud.get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 单个任务暂停：将任务状态设为paused
    task.status = 'paused'
    db.commit()
    return {"message": f"Task {task_id} paused"}

@router.post("/{task_id}/resume")
async def resume_task(task_id: int, db: Session = Depends(get_db)):
    """恢复单个任务"""
    task = crud.get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 单个任务恢复：将任务状态设为pending
    if task.status == 'paused':
        task.status = 'pending'
        db.commit()
    return {"message": f"Task {task_id} resumed"}

@router.get("/{task_id}/status")
async def get_task_status(task_id: int, db: Session = Depends(get_db)):
    """获取任务状态"""
    task = crud.get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    queues = db.query(models.TaskQueue).filter(
        models.TaskQueue.task_id == task_id
    ).all()

    status = {
        "total": len(queues),
        "pending": sum(1 for q in queues if q.status == "pending"),
        "running": sum(1 for q in queues if q.status == "running"),
        "done": sum(1 for q in queues if q.status == "done"),
        "failed": sum(1 for q in queues if q.status == "failed")
    }

    return {
        "task_id": task_id,
        "status": task.status,
        "queues": status
    }

@router.get("/stats/device-summary")
async def get_tasks_device_stats(db: Session = Depends(get_db)):
    """获取所有任务的设备执行统计"""
    try:
        # 获取所有任务
        tasks = db.query(models.Task).all()

        task_stats = {}
        for task in tasks:
            # 获取该任务的所有设备队列记录
            queues = db.query(models.TaskQueue).filter(
                models.TaskQueue.task_id == task.id
            ).all()

            # 统计各状态的设备数量
            stats = {
                "total": len(queues),
                "pending": sum(1 for q in queues if q.status == "pending"),
                "running": sum(1 for q in queues if q.status == "running"),
                "done": sum(1 for q in queues if q.status == "done"),
                "failed": sum(1 for q in queues if q.status == "failed")
            }

            task_stats[task.id] = stats

        return task_stats

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务设备统计失败: {str(e)}")

@router.post("/{task_id}/complete", response_model=schemas.task.TaskCompletionResponse)
async def complete_task(
    task_id: int,
    result: dict,
    device_id: int,
    db: Session = Depends(get_db)
):
    """标记任务完成"""
    try:
        # 更新任务队列状态
        task_queue = db.query(models.TaskQueue).filter(
            models.TaskQueue.task_id == task_id,
            models.TaskQueue.device_id == device_id
        ).first()
        
        if not task_queue:
            raise HTTPException(status_code=404, detail="Task queue not found")
        
        # 更新任务状态为完成
        task_queue.status = "done"
        task_queue.result_summary = str(result)
        task_queue.finish_time = datetime.utcnow()
        db.commit()
        
        # 返回标准响应
        return {
            "success": True,
            "task_id": task_id,
            "device_id": device_id,
            "completion_time": datetime.utcnow(),
            "result": result,
            "error": None,
            "metrics": {
                "duration": (datetime.utcnow() - task_queue.dispatch_time).total_seconds()
            }
        }
        
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "task_id": task_id,
            "device_id": device_id,
            "completion_time": datetime.utcnow(),
            "result": None,
            "error": str(e),
            "metrics": None
        }

@router.get("/{task_id}/distribution")
async def get_task_distribution(task_id: int):
    """获取任务分发详情"""
    try:
        distribution_info = await task_dispatch_service.get_task_distribution_info(task_id)
        if 'error' in distribution_info:
            raise HTTPException(status_code=404, detail=distribution_info['error'])
        return distribution_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务分发信息失败: {str(e)}")

@router.post("/{task_id}/cancel")
async def cancel_task(task_id: int):
    """取消任务"""
    try:
        success = await task_dispatch_service.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        return {"message": f"任务 {task_id} 已取消"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")
