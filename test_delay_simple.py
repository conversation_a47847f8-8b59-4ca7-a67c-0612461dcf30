#!/usr/bin/env python3
"""
简单的分组延迟测试脚本
"""

import time
import requests
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:8000"

def log_info(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ℹ️  {message}")

def log_success(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ✅ {message}")

def log_error(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ❌ {message}")

def test_api():
    """测试API连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            log_success("API连接正常")
            return True
        else:
            log_error(f"API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        log_error(f"API连接失败: {e}")
        return False

def get_devices():
    """获取设备列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            log_info(f"获取到 {len(devices)} 个设备")
            return devices
        else:
            log_error(f"获取设备失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取设备失败: {e}")
        return []

def get_groups():
    """获取分组列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/groups", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            log_info(f"获取到 {len(groups)} 个分组")
            return groups
        else:
            log_error(f"获取分组失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取分组失败: {e}")
        return []

def create_delay_test_task(target_scope, target_id, delay_group_sec, delay_like_sec):
    """创建延迟测试任务"""
    try:
        # 前端发送毫秒（用户输入秒*1000）
        delay_group_ms = int(delay_group_sec * 1000)
        delay_like_ms = int(delay_like_sec * 1000)
        
        task_data = {
            "task_type": "sign",
            "parameters": {"count": 1},
            "target_scope": target_scope,
            "target_id": target_id,
            "delay_group": delay_group_ms,  # 发送毫秒
            "delay_like": delay_like_ms     # 发送毫秒
        }
        
        log_info(f"创建测试任务:")
        log_info(f"  分组延迟: {delay_group_sec}秒 (发送{delay_group_ms}ms)")
        log_info(f"  操作延迟: {delay_like_sec}秒 (发送{delay_like_ms}ms)")
        
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_success(f"任务创建成功，ID: {task.get('id')}")
            return task
        else:
            log_error(f"任务创建失败，状态码: {response.status_code}")
            log_error(f"响应: {response.text}")
            return None
    except Exception as e:
        log_error(f"创建任务失败: {e}")
        return None

def monitor_task(task_id, duration=30):
    """监控任务执行"""
    log_info(f"开始监控任务 {task_id}，监控时长 {duration}秒")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < duration:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                current_status = status.get('status')
                
                if current_status != last_status:
                    log_info(f"任务状态: {current_status}")
                    last_status = current_status
                
                queues = status.get('queues', {})
                if queues:
                    total = queues.get('total', 0)
                    pending = queues.get('pending', 0)
                    running = queues.get('running', 0)
                    done = queues.get('done', 0)
                    failed = queues.get('failed', 0)
                    
                    log_info(f"队列: 总计={total}, 待处理={pending}, 执行中={running}, 完成={done}, 失败={failed}")
                
                if current_status in ['done', 'failed', 'cancelled']:
                    log_success(f"任务完成，最终状态: {current_status}")
                    break
                    
        except Exception as e:
            log_error(f"监控出错: {e}")
        
        time.sleep(3)
    
    log_info("监控结束")

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 分组延迟功能测试")
    print("=" * 50)
    
    # 1. 测试API连接
    if not test_api():
        log_error("请先启动后端服务: ./start_backend.sh")
        return
    
    # 2. 获取系统信息
    devices = get_devices()
    groups = get_groups()
    
    if not devices and not groups:
        log_error("系统中没有设备和分组")
        return
    
    # 3. 选择测试目标
    if groups:
        # 使用分组测试
        group = groups[0]
        target_scope = "group"
        target_id = group.get('id')
        target_name = f"分组: {group.get('group_name')}"
        log_info(f"使用分组测试: {target_name}")
    elif devices:
        # 使用设备测试
        device = devices[0]
        target_scope = "single"
        target_id = device.get('id')
        target_name = f"设备: {device.get('device_number')}"
        log_info(f"使用设备测试: {target_name}")
    
    # 4. 创建测试任务
    print("\n" + "=" * 30)
    log_info("创建延迟测试任务")
    task = create_delay_test_task(target_scope, target_id, 5.0, 2.0)
    
    if not task:
        log_error("任务创建失败")
        return
    
    # 5. 监控任务执行
    print("\n" + "=" * 30)
    log_info("开始监控任务执行")
    log_info("💡 同时查看后端日志: journalctl -u wb-system -f")
    log_info("💡 或者: tail -f logs/app.log")
    
    monitor_task(task.get('id'), 60)
    
    # 6. 测试结果说明
    print("\n" + "=" * 50)
    print("✅ 测试完成")
    print("\n💡 在后端日志中查找以下信息:")
    print("   ⏰ 应用分组延迟: 设备X 等待 Y秒")
    print("   ⏰ 应用操作延迟: Z秒")
    print("   🚀 开始处理任务")
    print("   ✅ 模拟任务执行完成")
    print("\n📝 查看日志命令:")
    print("   journalctl -u wb-system -f")
    print("   或: tail -f logs/app.log")
    print("=" * 50)

if __name__ == "__main__":
    main()
