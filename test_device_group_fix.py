#!/usr/bin/env python3
"""
测试设备分组修复
"""

import requests
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.db import engine
from app.models.device import Device
from app.models.group import Group, DeviceGroup

def test_api_device_group():
    """测试API设备分组"""
    print("🔧 测试API设备分组...")
    
    try:
        # 1. 获取设备和分组
        devices_response = requests.get("http://localhost:8000/devices/", timeout=10)
        groups_response = requests.get("http://localhost:8000/groups/", timeout=10)
        
        devices = devices_response.json()
        groups = groups_response.json()
        
        print(f"   设备数量: {len(devices)}")
        print(f"   分组数量: {len(groups)}")
        
        # 2. 找一个设备和分组进行测试
        test_device = devices[0] if devices else None
        test_group = groups[0] if groups else None
        
        if not test_device or not test_group:
            print("❌ 没有设备或分组数据")
            return False
            
        device_id = test_device.get('id')
        group_id = test_group.get('id')
        
        print(f"\n   测试设备: {test_device.get('device_number')} (ID: {device_id})")
        print(f"   当前分组: {test_device.get('group_id')}")
        print(f"   目标分组: {test_group.get('group_name')} (ID: {group_id})")
        
        # 3. 调用API添加设备到分组
        api_data = {
            'device_id': device_id,
            'group_id': group_id
        }
        
        print(f"\n📡 调用API: POST /groups/{group_id}/devices")
        print(f"   请求数据: {json.dumps(api_data, indent=2)}")
        
        response = requests.post(
            f"http://localhost:8000/groups/{group_id}/devices",
            json=api_data,
            timeout=10
        )
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                print("✅ API调用成功")
            except:
                print(f"   响应文本: {response.text}")
                print("✅ API调用成功")
        else:
            print(f"   错误响应: {response.text}")
            print("❌ API调用失败")
            return False
            
        # 4. 验证数据库状态
        print(f"\n🔍 验证数据库状态...")
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 检查设备表
            device = db.query(Device).filter(Device.id == device_id).first()
            if device:
                print(f"   设备表中的group_id: {device.group_id}")
                
                if device.group_id == group_id:
                    print("✅ 设备表更新成功")
                else:
                    print("❌ 设备表未更新")
                    return False
            else:
                print(f"   ❌ 设备表中找不到设备ID {device_id}")
                return False
                
            # 检查DeviceGroup表
            device_group = db.query(DeviceGroup).filter(
                DeviceGroup.device_id == device_id,
                DeviceGroup.group_id == group_id
            ).first()
            
            if device_group:
                print(f"   DeviceGroup表中有记录: ID={device_group.id}")
                print("✅ DeviceGroup表更新成功")
            else:
                print(f"   ❌ DeviceGroup表中没有记录")
                return False
                
        finally:
            db.close()
            
        # 5. 验证API返回的数据
        print(f"\n📡 验证API返回的数据...")
        updated_response = requests.get("http://localhost:8000/devices/", timeout=10)
        if updated_response.status_code == 200:
            updated_devices = updated_response.json()
            updated_device = next((d for d in updated_devices if d.get('id') == device_id), None)
            
            if updated_device:
                api_group_id = updated_device.get('group_id')
                print(f"   API返回的设备分组: {api_group_id}")
                
                if api_group_id == group_id:
                    print("✅ API数据一致")
                    return True
                else:
                    print("❌ API数据不一致")
                    return False
            else:
                print("   ❌ API中找不到设备")
                return False
        else:
            print("   ❌ 无法获取更新后的设备数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_group_display():
    """测试前端分组显示"""
    print("\n👥 测试前端分组显示...")
    
    try:
        # 获取分组数据
        response = requests.get("http://localhost:8000/groups/", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取分组数据")
            return False
            
        groups = response.json()
        devices_response = requests.get("http://localhost:8000/devices/", timeout=10)
        devices = devices_response.json() if devices_response.status_code == 200 else []
        
        print("   分组信息:")
        for group in groups:
            group_id = group.get('id')
            group_name = group.get('group_name')
            description = group.get('description', '')
            
            # 计算分组中的设备数量
            device_count = len([d for d in devices if d.get('group_id') == group_id])
            
            print(f"   - {group_name} (ID: {group_id})")
            print(f"     描述: {description}")
            print(f"     设备数量: {device_count}")
            
            # 显示分组中的设备
            group_devices = [d for d in devices if d.get('group_id') == group_id]
            if group_devices:
                print(f"     设备列表:")
                for device in group_devices[:3]:  # 只显示前3个
                    print(f"       - {device.get('device_number')} (ID: {device.get('id')})")
                if len(group_devices) > 3:
                    print(f"       ... 还有 {len(group_devices) - 3} 个设备")
            print()
            
        print("✅ 分组显示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 分组显示测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试设备分组修复...")
    print("="*50)
    
    tests = [
        ("API设备分组", test_api_device_group),
        ("前端分组显示", test_frontend_group_display)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
            
    print("="*50)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 设备分组功能修复成功！")
        print("\n✅ 修复的问题:")
        print("1. API调用成功但数据库不更新 - 已修复")
        print("2. 设备分组逻辑错误 - 已修复")
        print("3. 前端分组显示 - 正常工作")
        print("4. 数据一致性 - Device表和DeviceGroup表同步")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        
    print("\n💡 现在可以正常使用:")
    print("- 前端添加设备到分组")
    print("- 分组设备数量正确显示")
    print("- 数据库数据一致性")

if __name__ == "__main__":
    main()
