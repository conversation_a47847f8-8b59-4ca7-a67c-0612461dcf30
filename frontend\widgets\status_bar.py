"""
状态栏组件
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
import threading
import time

class StatusBar(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        
        self.create_widgets()
        self.start_status_update()
        
    def create_widgets(self):
        """创建状态栏组件"""
        # 左侧状态信息
        left_frame = ttk.Frame(self)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # API连接状态
        self.api_status_label = ttk.Label(left_frame, text="🔴 API未连接", style='Status.TLabel')
        self.api_status_label.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(left_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 设备统计
        self.device_stats_label = ttk.Label(left_frame, text="设备: 0/0", style='Status.TLabel')
        self.device_stats_label.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(left_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 任务统计
        self.task_stats_label = ttk.Label(left_frame, text="任务: 0运行/0总计", style='Status.TLabel')
        self.task_stats_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧时间和版本信息
        right_frame = ttk.Frame(self)
        right_frame.pack(side=tk.RIGHT)
        
        # 当前时间
        self.time_label = ttk.Label(right_frame, text="", style='Status.TLabel')
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # 分隔符
        ttk.Separator(right_frame, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=5)
        
        # 版本信息
        version_label = ttk.Label(right_frame, text="v1.0", style='Status.TLabel')
        version_label.pack(side=tk.RIGHT, padx=5)
        
    def start_status_update(self):
        """开始状态更新"""
        def update_loop():
            while True:
                try:
                    # 更新时间
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.winfo_toplevel().after(0, lambda: self.time_label.config(text=current_time))
                    
                    # 更新状态信息
                    self.update_status()
                    
                    time.sleep(1)  # 每秒更新一次
                except Exception as e:
                    print(f"状态栏更新异常: {e}")
                    time.sleep(5)
                    
        threading.Thread(target=update_loop, daemon=True).start()
        
    def update_status(self):
        """更新状态信息"""
        def fetch_status():
            try:
                # 检查API连接
                api_connected = self.api_client.check_connection()
                
                if api_connected:
                    # 获取统计信息
                    stats = self.api_client.get_dashboard_stats()
                    self.winfo_toplevel().after(0, lambda: self.update_status_display(True, stats))
                else:
                    self.winfo_toplevel().after(0, lambda: self.update_status_display(False, None))
                    
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: self.update_status_display(False, None))
                
        threading.Thread(target=fetch_status, daemon=True).start()
        
    def update_status_display(self, api_connected, stats):
        """更新状态显示"""
        # 更新API连接状态
        if api_connected:
            self.api_status_label.config(text="🟢 API已连接", foreground="green")
        else:
            self.api_status_label.config(text="🔴 API未连接", foreground="red")
            
        # 更新统计信息
        if stats:
            device_stats = stats.get('devices', {})
            task_stats = stats.get('tasks', {})
            
            # 设备统计
            device_text = f"设备: {device_stats.get('online', 0)}/{device_stats.get('total', 0)}"
            self.device_stats_label.config(text=device_text)
            
            # 任务统计
            task_text = f"任务: {task_stats.get('running', 0)}运行/{task_stats.get('total', 0)}总计"
            self.task_stats_label.config(text=task_text)
        else:
            self.device_stats_label.config(text="设备: -/-")
            self.task_stats_label.config(text="任务: -/-")
            
    def refresh(self):
        """手动刷新状态"""
        self.update_status()
