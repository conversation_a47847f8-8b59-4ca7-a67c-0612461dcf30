#!/usr/bin/env python3
"""
测试API功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.api_client import APIClient
from config import Config

def main():
    """主函数"""
    print("🧪 测试前端API功能...")
    print("="*50)
    
    # 创建API客户端
    api_client = APIClient(Config.API_BASE_URL)
    
    # 1. 测试连接
    print("1. 测试API连接...")
    connected = api_client.check_connection()
    print(f"   连接状态: {'✅ 已连接' if connected else '❌ 未连接'}")
    
    if not connected:
        print("❌ API连接失败，请检查后端服务器")
        return
    
    # 2. 测试获取设备
    print("\n2. 测试获取设备...")
    devices = api_client.get_devices()
    print(f"   设备数量: {len(devices) if devices else 0}")
    
    # 3. 测试获取分组
    print("\n3. 测试获取分组...")
    groups = api_client.get_groups()
    print(f"   分组数量: {len(groups) if groups else 0}")
    
    if groups:
        print("   分组列表:")
        for group in groups:
            print(f"     ID: {group.get('id')}, 名称: {group.get('group_name')}")
    
    # 4. 测试创建分组
    print("\n4. 测试创建分组...")
    group_data = {
        "group_name": "前端测试分组",
        "description": "通过前端API客户端创建"
    }
    
    new_group = api_client.create_group(group_data)
    if new_group:
        print(f"   ✅ 分组创建成功:")
        print(f"     ID: {new_group.get('id')}")
        print(f"     名称: {new_group.get('group_name')}")
    else:
        print("   ❌ 分组创建失败")
    
    # 5. 测试创建inex任务
    print("\n5. 测试创建inex任务...")
    task_data = {
        "task_type": "inex",
        "parameters": {
            "user_id": "frontend_test_user",
            "count": 1
        },
        "target_scope": "single",
        "target_id": 1,
        "delay_group": 1000,
        "delay_like": 500
    }
    
    new_task = api_client.create_task(task_data)
    if new_task:
        print(f"   ✅ inex任务创建成功:")
        print(f"     ID: {new_task.get('id')}")
        print(f"     类型: {new_task.get('task_type')}")
        print(f"     参数: {new_task.get('parameters')}")
    else:
        print("   ❌ inex任务创建失败")
    
    print("\n" + "="*50)
    print("🎯 前端API功能测试完成")
    
    if connected and devices and groups and new_group and new_task:
        print("🎉 所有功能正常！")
        print("\n✅ 修复完成的功能:")
        print("1. 任务创建: 支持inex（主页关注）任务")
        print("2. 分组管理: 正确显示分组名称")
        print("3. 分组创建: 可以创建新分组")
        print("4. API通信: 所有接口正常工作")
    else:
        print("⚠️ 部分功能可能存在问题")

if __name__ == "__main__":
    main()
