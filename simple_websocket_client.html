<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 任务接收测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .task-message {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .heartbeat-message {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .error-message {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .stat-item {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background-color: #f8f9fa;
        }
        input[type="text"] {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebSocket 任务接收测试</h1>
        
        <div class="controls">
            <label>服务器地址: 
                <input type="text" id="serverUrl" value="ws://localhost:8000/ws/devi201" placeholder="ws://localhost:8000/ws/设备号">
            </label>
            <br>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button onclick="sendHeartbeat()">发送心跳</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalMessages">0</div>
                <div>总消息数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="taskMessages">0</div>
                <div>任务消息</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="heartbeatMessages">0</div>
                <div>心跳消息</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="errorMessages">0</div>
                <div>错误消息</div>
            </div>
        </div>
        
        <h3>📨 消息日志</h3>
        <div id="messageLog" class="log-container"></div>
        
        <h3>🎯 最新任务</h3>
        <div id="latestTask" class="message">暂无任务</div>
        
        <div style="margin-top: 20px;">
            <h4>测试说明:</h4>
            <ul>
                <li>1. 修改服务器地址中的设备号（如 devi201）</li>
                <li>2. 点击"连接"按钮连接到WebSocket服务器</li>
                <li>3. 通过API创建任务: <code>POST /tasks/</code></li>
                <li>4. 观察是否收到任务消息</li>
                <li>5. 如果收到任务，会自动发送完成消息</li>
            </ul>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let taskCount = 0;
        let heartbeatCount = 0;
        let errorCount = 0;
        let isConnected = false;

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            if (ws) {
                ws.close();
            }
            
            try {
                ws = new WebSocket(serverUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    logMessage('✅ WebSocket连接已建立', 'info');
                    
                    // 连接成功后发送一个心跳
                    setTimeout(sendHeartbeat, 1000);
                };
                
                ws.onmessage = function(event) {
                    handleMessage(event.data);
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('连接已关闭', 'disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    logMessage('🔌 WebSocket连接已关闭', 'info');
                };
                
                ws.onerror = function(error) {
                    logMessage('❌ WebSocket错误: ' + error, 'error');
                    errorCount++;
                    updateStats();
                };
                
            } catch (error) {
                logMessage('❌ 连接失败: ' + error.message, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }
        
        function handleMessage(data) {
            messageCount++;
            
            try {
                const message = JSON.parse(data);
                const timestamp = new Date().toLocaleTimeString();
                
                // 检查消息类型
                if (isTaskMessage(message)) {
                    taskCount++;
                    logMessage(`🎯 [${timestamp}] 收到任务: ${JSON.stringify(message, null, 2)}`, 'task');
                    updateLatestTask(message);
                    
                    // 自动发送任务完成消息
                    setTimeout(() => autoCompleteTask(message), 2000);
                    
                } else if (isHeartbeatMessage(message)) {
                    heartbeatCount++;
                    logMessage(`💓 [${timestamp}] 心跳消息: ${JSON.stringify(message)}`, 'heartbeat');
                    
                } else if (isErrorMessage(message)) {
                    errorCount++;
                    logMessage(`⚠️ [${timestamp}] 错误消息: ${JSON.stringify(message)}`, 'error');
                    
                } else {
                    logMessage(`📨 [${timestamp}] 其他消息: ${JSON.stringify(message)}`, 'info');
                }
                
            } catch (error) {
                logMessage(`📝 [${new Date().toLocaleTimeString()}] 文本消息: ${data}`, 'info');
            }
            
            updateStats();
        }
        
        function isTaskMessage(message) {
            return message.task_id && message.type && message.parameters;
        }
        
        function isHeartbeatMessage(message) {
            return message.type === 'heartbeat_ack' || message.type === 'heartbeat';
        }
        
        function isErrorMessage(message) {
            return message.type === 'error';
        }
        
        function autoCompleteTask(taskMessage) {
            if (!isConnected || !ws) return;
            
            const completion = {
                type: 'task_completion',
                task_id: taskMessage.task_id,
                status: 'success',
                timestamp: new Date().toISOString(),
                result: {
                    message: '自动测试完成',
                    task_type: taskMessage.type,
                    parameters: taskMessage.parameters
                }
            };
            
            ws.send(JSON.stringify(completion));
            logMessage(`📤 自动发送任务完成: ${JSON.stringify(completion)}`, 'info');
        }
        
        function sendHeartbeat() {
            if (!isConnected || !ws) {
                logMessage('❌ 未连接，无法发送心跳', 'error');
                return;
            }
            
            const heartbeat = {
                type: 'heartbeat',
                timestamp: new Date().toISOString(),
                device_number: 'devi201'
            };
            
            ws.send(JSON.stringify(heartbeat));
            logMessage(`💓 发送心跳: ${JSON.stringify(heartbeat)}`, 'heartbeat');
        }
        
        function updateStatus(text, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = text;
            statusEl.className = 'status ' + className;
        }
        
        function updateStats() {
            document.getElementById('totalMessages').textContent = messageCount;
            document.getElementById('taskMessages').textContent = taskCount;
            document.getElementById('heartbeatMessages').textContent = heartbeatCount;
            document.getElementById('errorMessages').textContent = errorCount;
        }
        
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('messageLog');
            const messageEl = document.createElement('div');
            messageEl.className = 'message';
            
            if (type === 'task') {
                messageEl.className += ' task-message';
            } else if (type === 'heartbeat') {
                messageEl.className += ' heartbeat-message';
            } else if (type === 'error') {
                messageEl.className += ' error-message';
            }
            
            messageEl.textContent = message;
            logContainer.appendChild(messageEl);
            
            // 自动滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志数量
            while (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        function updateLatestTask(task) {
            const taskEl = document.getElementById('latestTask');
            taskEl.innerHTML = `
                <strong>任务ID:</strong> ${task.task_id}<br>
                <strong>类型:</strong> ${task.type}<br>
                <strong>参数:</strong> ${JSON.stringify(task.parameters, null, 2)}<br>
                <strong>时间:</strong> ${new Date().toLocaleString()}
            `;
            taskEl.className = 'message task-message';
        }
        
        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
            messageCount = 0;
            taskCount = 0;
            heartbeatCount = 0;
            errorCount = 0;
            updateStats();
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            // 可以在这里自动连接
            // connect();
        };
    </script>
</body>
</html>
