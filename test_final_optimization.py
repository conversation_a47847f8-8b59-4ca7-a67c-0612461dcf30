#!/usr/bin/env python3
"""
测试最终优化结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_backend_connection():
    """测试后端连接"""
    print("🔗 测试后端连接...")
    
    try:
        base_url = "http://localhost:8000"
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_data_apis():
    """测试数据API"""
    print("\n📊 测试数据API...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试设备API
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ 设备API正常，返回 {len(devices)} 个设备")
        else:
            print(f"❌ 设备API失败: {response.status_code}")
            return False
            
        # 测试分组API
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            print(f"✅ 分组API正常，返回 {len(groups)} 个分组")
            
            # 显示分组信息
            for group in groups[:3]:  # 显示前3个分组
                group_name = group.get('group_name', '未知')
                group_id = group.get('id')
                device_count = len([d for d in devices if d.get('group_id') == group_id])
                print(f"   分组: {group_name} (ID: {group_id}, {device_count}设备)")
                
            return True
        else:
            print(f"❌ 分组API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据API测试异常: {e}")
        return False

def test_task_creation():
    """测试任务创建"""
    print("\n🚀 测试任务创建...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取第一个分组用于测试
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取分组数据")
            return False
            
        groups = response.json()
        if not groups:
            print("⚠️ 没有可用的分组")
            return True
            
        test_group = groups[0]
        group_id = test_group.get('id')
        group_name = test_group.get('group_name', '测试分组')
        
        # 测试各种任务类型
        test_tasks = [
            {
                "name": "点赞任务",
                "data": {
                    "task_type": "like",
                    "parameters": {
                        "blogger_id": "test_blogger_final",
                        "like_id": "test_like_final"
                    },
                    "target_scope": "group",
                    "target_id": group_id,
                    "delay_group": 2000,
                    "delay_like": 1000
                }
            },
            {
                "name": "超话签到任务",
                "data": {
                    "task_type": "page_sign",
                    "parameters": {
                        "page_url": "https://weibo.com/p/test_final_topic",
                        "blogger_id": "test_final_blogger",
                        "like_id": "test_final_like"
                    },
                    "target_scope": "group",
                    "target_id": group_id,
                    "delay_group": 3000,
                    "delay_like": 1500
                }
            }
        ]
        
        success_count = 0
        for task in test_tasks:
            try:
                response = requests.post(f"{base_url}/tasks/", json=task["data"], timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ {task['name']} 创建成功 (ID: {result.get('id')}) - 分组: {group_name}")
                    success_count += 1
                else:
                    print(f"❌ {task['name']} 创建失败: {response.status_code}")
            except Exception as e:
                print(f"❌ {task['name']} 创建异常: {e}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 任务创建测试异常: {e}")
        return False

def test_frontend_files():
    """测试前端文件"""
    print("\n📁 测试前端文件...")
    
    required_files = [
        "frontend/main.py",
        "frontend/widgets/task_manager.py",
        "frontend/widgets/simple_task_creator.py",
        "frontend/config.py",
        "frontend/utils/api_client.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} 缺失")
    
    return len(missing_files) == 0

def test_task_types():
    """测试任务类型配置"""
    print("\n🎯 测试任务类型配置...")
    
    try:
        sys.path.append('frontend')
        from config import Config
        
        task_types = Config.TASK_TYPES
        expected_types = ["签到任务", "点赞任务", "超话签到任务", "主页关注任务"]
        
        actual_types = list(task_types.values())
        
        if "超话签到任务" in actual_types:
            print("✅ 超话签到任务已添加")
        else:
            print("❌ 超话签到任务缺失")
            return False
            
        print(f"✅ 支持的任务类型: {actual_types}")
        return True
        
    except Exception as e:
        print(f"❌ 任务类型配置测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 最终优化功能测试")
    print("="*60)
    
    tests = [
        ("后端连接", test_backend_connection),
        ("数据API", test_data_apis),
        ("任务创建", test_task_creation),
        ("前端文件", test_frontend_files),
        ("任务类型配置", test_task_types)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 所有测试通过！优化完成！")
        
        print("\n✅ 优化成果总结:")
        print("1. ✨ 简化任务创建界面 - 直接在任务管理页面顶部")
        print("2. 🎯 统一参数输入 - 博主ID和点赞ID适用所有任务")
        print("3. 📋 多分组选择 - 支持勾选多个分组批量创建")
        print("4. 💾 设置保存功能 - 参数可以保存和加载")
        print("5. 🔄 分组管理优化 - 操作后自动刷新")
        print("6. 📝 超话签到任务 - 新增任务类型")
        
        print("\n🚀 功能特点:")
        print("• 🎯 简洁易用 - 一键创建，无需弹窗")
        print("• 📊 批量操作 - 支持多分组同时创建任务")
        print("• 🔄 实时刷新 - 分组管理操作后自动刷新")
        print("• 💾 设置记忆 - 参数自动保存和恢复")
        print("• ⏰ 北京时间 - 统一时间显示格式")
        print("• 📝 执行日志 - 实时查看设备执行结果")
        
        print("\n💡 使用方法:")
        print("1. 在任务管理页面顶部的快速创建任务区域")
        print("2. 选择任务类型（签到/点赞/超话签到/主页关注）")
        print("3. 输入博主ID和点赞ID（适用于所有任务类型）")
        print("4. 勾选要创建任务的分组（支持多选）")
        print("5. 设置分组延迟和操作延迟")
        print("6. 点击'创建任务'按钮一键创建")
        print("7. 使用'保存设置'保存常用配置")
        
        print("\n🎯 优化亮点:")
        print("• 界面简洁 - 移除复杂的弹窗创建")
        print("• 参数统一 - 删除任务特定的复杂参数")
        print("• 多选支持 - 分组选择支持复选框批量操作")
        print("• 自动刷新 - 分组管理操作后立即更新显示")
        print("• 设置持久 - 配置保存到本地文件")
        print("• 任务扩展 - 新增超话签到任务类型")
        
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
