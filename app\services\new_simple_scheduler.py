#!/usr/bin/env python3
"""
全新的简单调度器
从头开始设计，简单、可靠、有效
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Set, Optional
from sqlalchemy import and_

from app.db import get_db
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.utils.time_utils import get_beijing_now_naive

logger = logging.getLogger(__name__)

class NewSimpleScheduler:
    """全新的简单调度器"""
    
    def __init__(self):
        # 基本状态
        self.is_running = False
        self.main_task = None
        
        # 设备状态管理
        self.device_last_completion: Dict[int, datetime] = {}  # 设备最后完成任务时间
        self.device_processing: Set[int] = set()  # 正在处理任务的设备
        self.processed_tasks: Set[int] = set()  # 已处理的任务ID（防重复）
        
        # 简单配置
        self.check_interval = 2.0  # 检查间隔2秒
        self.max_retries = 3  # 最大重试次数
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return
            
        logger.info("🚀 启动新的简单调度器...")
        self.is_running = True
        
        # 清理状态
        self.device_last_completion.clear()
        self.device_processing.clear()
        self.processed_tasks.clear()
        
        # 启动主循环
        self.main_task = asyncio.create_task(self._main_loop())
        logger.info("✅ 新的简单调度器启动成功")
        
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
            
        logger.info("🛑 停止新的简单调度器...")
        self.is_running = False
        
        if self.main_task:
            self.main_task.cancel()
            try:
                await self.main_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✅ 新的简单调度器已停止")
        
    async def _main_loop(self):
        """主循环 - 简单直接"""
        logger.info("📋 调度器主循环开始")
        
        while self.is_running:
            try:
                # 1. 获取待处理任务
                pending_tasks = await self._get_pending_tasks()
                
                if pending_tasks:
                    logger.debug(f"📋 找到 {len(pending_tasks)} 个待处理任务")
                    
                    # 2. 处理每个任务
                    for task_queue in pending_tasks:
                        if not self.is_running:
                            break
                            
                        await self._process_single_task(task_queue)
                
                # 3. 等待下次检查
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ 调度器主循环错误: {e}")
                await asyncio.sleep(5)  # 错误后等待5秒
                
    async def _get_pending_tasks(self) -> list:
        """获取待处理任务 - 简单查询"""
        try:
            db = next(get_db())
            try:
                # 只查询pending状态的任务，按创建时间排序
                pending_tasks = db.query(TaskQueue).filter(
                    TaskQueue.status == 'pending'
                ).order_by(TaskQueue.create_time.asc()).limit(50).all()  # 限制50个，避免过载
                
                return pending_tasks
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 获取待处理任务失败: {e}")
            return []
            
    async def _process_single_task(self, task_queue):
        """处理单个任务 - 核心逻辑"""
        task_id = task_queue.task_id
        device_id = task_queue.device_id
        
        try:
            # 1. 防重复检查
            if task_id in self.processed_tasks:
                logger.debug(f"⚠️ 任务 {task_id} 已处理过，跳过")
                return
                
            # 2. 设备忙碌检查
            if device_id in self.device_processing:
                logger.debug(f"⚠️ 设备 {device_id} 正在处理其他任务，跳过任务 {task_id}")
                return
                
            # 3. 设备在线检查
            if not await self._is_device_online(device_id):
                logger.debug(f"⚠️ 设备 {device_id} 离线，跳过任务 {task_id}")
                return
                
            # 4. 分组延迟检查 - 关键功能
            wait_time = await self._calculate_group_delay_wait(task_id, device_id)
            if wait_time > 0:
                logger.info(f"⏰ 任务 {task_id} 需要等待分组延迟 {wait_time:.1f}秒")
                await asyncio.sleep(wait_time)
                
                # 重新检查设备状态（延迟后可能变化）
                if device_id in self.device_processing:
                    logger.debug(f"⚠️ 延迟后设备 {device_id} 已忙碌，跳过任务 {task_id}")
                    return
                    
            # 5. 发送任务
            success = await self._send_task_to_device(task_id, device_id)
            
            if success:
                # 标记为已处理和正在处理
                self.processed_tasks.add(task_id)
                self.device_processing.add(device_id)
                
                # 更新数据库状态
                await self._update_task_status(task_queue, 'running')
                logger.info(f"✅ 任务 {task_id} 已发送到设备 {device_id}")
            else:
                logger.warning(f"❌ 任务 {task_id} 发送失败")
                
        except Exception as e:
            logger.error(f"❌ 处理任务 {task_id} 失败: {e}")
            
    async def _calculate_group_delay_wait(self, task_id: int, device_id: int) -> float:
        """计算分组延迟等待时间 - 简单有效"""
        try:
            # 1. 获取任务的分组延迟设置
            db = next(get_db())
            try:
                task = db.query(Task).filter(Task.id == task_id).first()
                if not task or not task.delay_group or task.delay_group <= 0:
                    return 0.0  # 没有分组延迟
                    
                delay_seconds = task.delay_group / 1000.0  # 毫秒转秒
                
            finally:
                db.close()
                
            # 2. 检查设备最后完成时间
            if device_id not in self.device_last_completion:
                return 0.0  # 首次执行，无需等待
                
            # 3. 计算已过时间
            current_time = get_beijing_now_naive()
            last_completion = self.device_last_completion[device_id]
            elapsed = (current_time - last_completion).total_seconds()
            
            # 4. 计算需要等待的时间
            if elapsed < delay_seconds:
                wait_time = delay_seconds - elapsed
                logger.info(f"⏰ 分组延迟: 设备{device_id} 需要等待 {wait_time:.1f}秒 (已过{elapsed:.1f}秒, 需要{delay_seconds}秒)")
                return wait_time
            else:
                logger.debug(f"⏰ 分组延迟: 设备{device_id} 无需等待 (已过{elapsed:.1f}秒 >= {delay_seconds}秒)")
                return 0.0
                
        except Exception as e:
            logger.error(f"❌ 计算分组延迟失败: 任务{task_id}, 设备{device_id}, 错误:{e}")
            return 0.0
            
    async def _is_device_online(self, device_id: int) -> bool:
        """检查设备是否在线 - 简单检查"""
        try:
            # 检查WebSocket连接
            from app.websocket.ws_manager import manager as ws_manager

            # 获取设备号
            db = next(get_db())
            try:
                device = db.query(Device).filter(Device.id == device_id).first()
                if not device:
                    logger.debug(f"❌ 设备{device_id}不存在")
                    return False

                device_number = device.device_number

            finally:
                db.close()

            # 检查WebSocket连接
            is_connected = device_number in ws_manager.active_connections
            logger.debug(f"🔍 设备{device_id}({device_number}) 在线检查: {is_connected}")

            if not is_connected:
                logger.debug(f"📋 当前活跃连接: {list(ws_manager.active_connections.keys())}")

            return is_connected

        except Exception as e:
            logger.error(f"❌ 检查设备在线状态失败: 设备{device_id}, 错误:{e}")
            return False
            
    async def _send_task_to_device(self, task_id: int, device_id: int) -> bool:
        """发送任务到设备 - 简单发送"""
        try:
            # 1. 获取任务详情
            db = next(get_db())
            try:
                task = db.query(Task).filter(Task.id == task_id).first()
                device = db.query(Device).filter(Device.id == device_id).first()
                
                if not task or not device:
                    logger.error(f"❌ 任务或设备不存在: 任务{task_id}, 设备{device_id}")
                    return False
                    
            finally:
                db.close()
                
            # 2. 构造任务消息
            task_message = {
                "task_id": task_id,
                "device_id": device_id,
                "type": task.task_type,
                "parameters": task.parameters or {},
                "timestamp": datetime.now().isoformat()
            }
            
            # 3. 发送到WebSocket
            from app.websocket.ws_manager import manager as ws_manager
            
            success = await ws_manager.send_task(device.device_number, task_message)
            
            if success:
                logger.info(f"📤 任务 {task_id} 已发送到设备 {device.device_number}")
                return True
            else:
                logger.warning(f"❌ 任务 {task_id} 发送到设备 {device.device_number} 失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 发送任务失败: 任务{task_id}, 设备{device_id}, 错误:{e}")
            return False
            
    async def _update_task_status(self, task_queue, new_status: str):
        """更新任务状态 - 简单更新"""
        try:
            db = next(get_db())
            try:
                # 更新TaskQueue状态
                task_queue.status = new_status
                task_queue.update_time = get_beijing_now_naive()
                
                # 更新Task状态
                task = db.query(Task).filter(Task.id == task_queue.task_id).first()
                if task:
                    task.status = new_status
                    task.update_time = get_beijing_now_naive()
                
                db.commit()
                logger.debug(f"📝 任务 {task_queue.task_id} 状态更新为 {new_status}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: 任务{task_queue.task_id}, 错误:{e}")
            
    def mark_task_completed(self, device_id: int, task_id: int):
        """标记任务完成 - 外部调用"""
        try:
            # 记录完成时间
            self.device_last_completion[device_id] = get_beijing_now_naive()
            
            # 移除处理中标记
            self.device_processing.discard(device_id)
            
            logger.info(f"✅ 任务完成: 设备{device_id} 任务{task_id} 完成时间已记录")
            
        except Exception as e:
            logger.error(f"❌ 标记任务完成失败: 设备{device_id}, 任务{task_id}, 错误:{e}")
            
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'processed_tasks_count': len(self.processed_tasks),
            'processing_devices_count': len(self.device_processing),
            'device_completion_records': len(self.device_last_completion),
            'processing_devices': list(self.device_processing),
            'last_completion_times': {
                device_id: time.isoformat() 
                for device_id, time in self.device_last_completion.items()
            }
        }

# 创建全局实例
new_simple_scheduler = NewSimpleScheduler()
