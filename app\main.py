from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import logging
import socket
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入数据库相关模块，如果失败则跳过
# 🔥 修复：使用简化版稳定调度器
from app.services.simple_stable_scheduler import simple_stable_scheduler as stable_scheduler

try:
    from app.db import engine, Base
    from app.routes import device, task, group, task_sync
    from app.websocket import ws_manager
    from app.services.scheduler import scheduler
    from app.services.task_timeout_monitor import start_timeout_monitoring

    # 创建所有表（首次运行时）
    Base.metadata.create_all(bind=engine)
    DB_AVAILABLE = True
    logger.info("数据库连接成功，启用完整功能")
except Exception as e:
    logger.warning(f"数据库连接失败，启用简化模式: {e}")
    DB_AVAILABLE = False

    # 🔥 简化模式下的基本导入
    try:
        from app.websocket import ws_manager
        from app.services.task_timeout_monitor import start_timeout_monitoring
    except Exception as import_error:
        logger.error(f"基本模块导入失败: {import_error}")

app = FastAPI(title="Control Server")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(device.router, tags=["Devices"])
app.include_router(task.router, prefix="/tasks", tags=["Tasks"])
app.include_router(group.router, prefix="/groups", tags=["Groups"])
app.include_router(task_sync.router, tags=["Task Sync"])
app.include_router(ws_manager.router, prefix="/ws", tags=["WebSocket"])

# 导入超时监控路由
from app.routes import timeout_monitor
app.include_router(timeout_monitor.router, tags=["Timeout Monitor"])

import socket

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        from app.db import get_db
        from datetime import datetime

        db = next(get_db())
        db.execute("SELECT 1")
        db.close()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "database": "connected",
            "services": {
                "scheduler": "running",
                "websocket": "active",
                "timeout_monitor": "active"
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "database": "disconnected"
        }

@app.get("/ip")
async def show_ip_info():
    """显示服务器IP信息"""
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "message": "访问此服务的IP地址可能是: " + get_local_ip()
    }

@app.on_event("startup")
async def startup_event():
    logger.info("Starting up application...")

    try:
        # 启动WebSocket心跳检测器
        asyncio.create_task(ws_manager.manager.heartbeat_checker())
        logger.info("WebSocket heartbeat checker started")

        # 🔥 修复：启动稳定版调度器，增加错误处理
        logger.info("Starting stable scheduler...")
        await stable_scheduler.start()

        if stable_scheduler.is_running:
            logger.info("✅ Stable scheduler started successfully")
        else:
            logger.error("❌ Stable scheduler failed to start")

        # 完全停用原调度器
        # await scheduler.start_scheduler()  # 已停用

        # 启动任务超时监控
        asyncio.create_task(start_timeout_monitoring())
        logger.info("Task timeout monitoring started")

        logger.info("🎉 Application startup completed successfully")

    except Exception as e:
        logger.error(f"❌ Application startup failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
