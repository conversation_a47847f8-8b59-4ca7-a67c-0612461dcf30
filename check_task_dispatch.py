#!/usr/bin/env python3
"""
检查任务分发问题
"""

import requests
import json

def check_devices():
    """检查设备状态"""
    print("📱 检查设备状态...")
    
    try:
        response = requests.get("http://localhost:8000/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"   设备总数: {len(devices)}")
            
            online_devices = [d for d in devices if d.get('online_status') == 'online']
            offline_devices = [d for d in devices if d.get('online_status') == 'offline']
            
            print(f"   在线设备: {len(online_devices)}")
            print(f"   离线设备: {len(offline_devices)}")
            
            print("   设备详情:")
            for device in devices[:5]:
                device_number = device.get('device_number')
                status = device.get('online_status')
                last_heartbeat = device.get('last_heartbeat', 'N/A')
                print(f"     - {device_number}: {status} (心跳: {last_heartbeat})")
            
            return devices
        else:
            print(f"   ❌ 获取设备失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ 检查设备异常: {e}")
        return []

def check_running_tasks():
    """检查运行中的任务"""
    print("\n🔄 检查运行中的任务...")
    
    try:
        response = requests.get("http://localhost:8000/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            
            running_tasks = [t for t in tasks if t.get('status') == 'running']
            pending_tasks = [t for t in tasks if t.get('status') == 'pending']
            
            print(f"   总任务数: {len(tasks)}")
            print(f"   运行中任务: {len(running_tasks)}")
            print(f"   等待中任务: {len(pending_tasks)}")
            
            if running_tasks:
                print("   运行中任务详情:")
                for task in running_tasks[:5]:
                    task_id = task.get('id')
                    task_type = task.get('task_type')
                    target_scope = task.get('target_scope')
                    target_id = task.get('target_id')
                    create_time = task.get('create_time', '')
                    
                    print(f"     - 任务{task_id}: {task_type} -> {target_scope}({target_id}) [{create_time}]")
            
            return running_tasks
        else:
            print(f"   ❌ 获取任务失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ 检查任务异常: {e}")
        return []

def check_task_queue():
    """检查任务队列状态"""
    print("\n📋 检查任务队列状态...")
    
    try:
        # 这里需要一个API来获取TaskQueue的状态
        # 暂时通过超时监控API来获取运行中任务信息
        response = requests.get("http://localhost:8000/timeout-monitor/running-tasks", timeout=10)
        if response.status_code == 200:
            running_info = response.json()
            tasks = running_info.get('tasks', [])
            
            print(f"   队列中运行任务: {len(tasks)}")
            
            if tasks:
                print("   队列任务详情:")
                for task in tasks[:5]:
                    task_id = task.get('task_id')
                    device_id = task.get('device_id')
                    runtime = task.get('runtime_minutes', 0)
                    remaining = task.get('remaining_minutes', 0)
                    
                    print(f"     - 任务{task_id} -> 设备{device_id}: 运行{runtime}分钟, 剩余{remaining:.1f}分钟")
            
            return tasks
        else:
            print(f"   ❌ 获取队列状态失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ 检查队列异常: {e}")
        return []

def check_websocket_manager():
    """检查WebSocket管理器状态"""
    print("\n🔗 检查WebSocket管理器状态...")
    
    try:
        # 检查WebSocket测试端点
        response = requests.get("http://localhost:8000/ws/test", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"   WebSocket服务: {result.get('status')}")
            print(f"   连接URL: {result.get('connection_url')}")
        else:
            print(f"   ❌ WebSocket测试失败: {response.status_code}")
            
        return True
    except Exception as e:
        print(f"   ❌ 检查WebSocket异常: {e}")
        return False

def create_test_task():
    """创建测试任务"""
    print("\n🚀 创建测试任务...")
    
    try:
        # 获取设备
        response = requests.get("http://localhost:8000/devices/", timeout=10)
        if response.status_code != 200:
            print("   ❌ 无法获取设备")
            return None
            
        devices = response.json()
        if not devices:
            print("   ❌ 没有设备")
            return None
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number')
        
        print(f"   使用设备: {device_number} (ID: {device_id})")
        
        # 创建任务
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "dispatch_test_blogger",
                "like_id": "dispatch_test_like",
                "delay_click": 500
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 1000
        }
        
        response = requests.post("http://localhost:8000/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            print(f"   ✅ 测试任务创建成功: 任务ID {task_id}")
            return task_id
        else:
            print(f"   ❌ 任务创建失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"   ❌ 创建测试任务异常: {e}")
        return None

def check_scheduler_status():
    """检查调度器状态"""
    print("\n⚙️ 检查调度器状态...")
    
    try:
        # 检查超时监控状态
        response = requests.get("http://localhost:8000/timeout-monitor/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"   监控器状态: {status.get('monitor_status')}")
            print(f"   超时阈值: {status.get('timeout_threshold_minutes')}分钟")
            print(f"   运行中任务: {status.get('running_tasks_count')}个")
            
            # 检查接近超时的任务
            near_timeout = status.get('near_timeout_tasks', [])
            if near_timeout:
                print(f"   ⚠️ 接近超时任务: {len(near_timeout)}个")
            
            return True
        else:
            print(f"   ❌ 获取调度器状态失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 检查调度器异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 任务分发问题诊断")
    print("="*50)
    
    # 检查各个组件
    devices = check_devices()
    running_tasks = check_running_tasks()
    queue_tasks = check_task_queue()
    websocket_ok = check_websocket_manager()
    scheduler_ok = check_scheduler_status()
    
    # 创建测试任务
    test_task_id = create_test_task()
    
    print("\n" + "="*50)
    print("📊 诊断结果:")
    
    # 分析问题
    online_devices = [d for d in devices if d.get('online_status') == 'online']
    
    if not online_devices:
        print("❌ 主要问题: 没有在线设备")
        print("   解决方案:")
        print("   1. 检查客户端是否连接到WebSocket")
        print("   2. 确认WebSocket连接URL正确")
        print("   3. 检查网络连接")
    elif running_tasks and not queue_tasks:
        print("❌ 主要问题: 任务显示running但未在队列中执行")
        print("   解决方案:")
        print("   1. 检查调度器是否正常工作")
        print("   2. 检查WebSocket任务发送逻辑")
        print("   3. 检查任务状态更新机制")
    elif running_tasks and queue_tasks:
        print("✅ 任务正在正常执行")
        print("   运行中任务和队列任务都存在")
    else:
        print("ℹ️ 系统状态:")
        print(f"   - 在线设备: {len(online_devices)}个")
        print(f"   - 运行任务: {len(running_tasks)}个")
        print(f"   - 队列任务: {len(queue_tasks)}个")
        print(f"   - WebSocket: {'正常' if websocket_ok else '异常'}")
        print(f"   - 调度器: {'正常' if scheduler_ok else '异常'}")
    
    if test_task_id:
        print(f"\n🧪 测试任务 {test_task_id} 已创建，请观察是否被正确分发")

if __name__ == "__main__":
    main()
