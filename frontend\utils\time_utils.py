"""
时间工具模块 - 统一处理北京时间
"""

from datetime import datetime, timezone, timedelta
import pytz

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def get_beijing_now():
    """获取当前北京时间"""
    return datetime.now(BEIJING_TZ)

def utc_to_beijing(utc_time_str):
    """将UTC时间字符串转换为北京时间"""
    try:
        if not utc_time_str:
            return "未知时间"
            
        # 处理不同的时间格式
        if isinstance(utc_time_str, str):
            # 移除末尾的Z或+00:00
            utc_time_str = utc_time_str.replace('Z', '+00:00')
            
            # 尝试解析ISO格式
            try:
                utc_time = datetime.fromisoformat(utc_time_str)
            except ValueError:
                # 尝试其他格式
                try:
                    utc_time = datetime.strptime(utc_time_str, '%Y-%m-%d %H:%M:%S')
                    utc_time = utc_time.replace(tzinfo=timezone.utc)
                except ValueError:
                    return utc_time_str  # 无法解析，返回原字符串
        elif isinstance(utc_time_str, datetime):
            utc_time = utc_time_str
        else:
            return "无效时间"
            
        # 如果没有时区信息，假设是UTC
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)
            
        # 转换为北京时间
        beijing_time = utc_time.astimezone(BEIJING_TZ)
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')
        
    except Exception as e:
        print(f"时间转换错误: {e}")
        return str(utc_time_str)

def format_beijing_time(dt=None, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化北京时间"""
    if dt is None:
        dt = get_beijing_now()
    elif isinstance(dt, str):
        # 如果是字符串，先转换
        return utc_to_beijing(dt)
    
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    beijing_dt = dt.astimezone(BEIJING_TZ)
    return beijing_dt.strftime(format_str)

def get_relative_time(time_str):
    """获取相对时间描述（如：2分钟前）"""
    try:
        if not time_str:
            return "未知"
            
        # 转换为北京时间的datetime对象
        if isinstance(time_str, str):
            # 先转换为UTC datetime
            utc_time_str = time_str.replace('Z', '+00:00')
            try:
                utc_time = datetime.fromisoformat(utc_time_str)
            except ValueError:
                try:
                    utc_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    utc_time = utc_time.replace(tzinfo=timezone.utc)
                except ValueError:
                    return time_str
        else:
            utc_time = time_str
            
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)
            
        # 转换为北京时间
        beijing_time = utc_time.astimezone(BEIJING_TZ)
        now_beijing = get_beijing_now()
        
        # 计算时间差
        diff = now_beijing - beijing_time
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
            
    except Exception as e:
        print(f"相对时间计算错误: {e}")
        return str(time_str)

def beijing_timestamp():
    """获取北京时间戳字符串"""
    return get_beijing_now().strftime('%Y-%m-%d %H:%M:%S')

def is_recent(time_str, minutes=5):
    """检查时间是否在最近几分钟内"""
    try:
        if not time_str:
            return False
            
        # 转换为UTC时间
        if isinstance(time_str, str):
            utc_time_str = time_str.replace('Z', '+00:00')
            try:
                utc_time = datetime.fromisoformat(utc_time_str)
            except ValueError:
                try:
                    utc_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    utc_time = utc_time.replace(tzinfo=timezone.utc)
                except ValueError:
                    return False
        else:
            utc_time = time_str
            
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)
            
        # 转换为北京时间
        beijing_time = utc_time.astimezone(BEIJING_TZ)
        now_beijing = get_beijing_now()
        
        # 检查是否在指定分钟内
        diff = now_beijing - beijing_time
        return diff.total_seconds() <= minutes * 60
        
    except Exception:
        return False

# 导出常用函数
__all__ = [
    'get_beijing_now',
    'utc_to_beijing', 
    'format_beijing_time',
    'get_relative_time',
    'beijing_timestamp',
    'is_recent',
    'BEIJING_TZ'
]
