#!/usr/bin/env python3
"""
直接测试分组API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app.routes.group import read_groups, read_group
from app.services.group import get_all_groups

def test_direct_groups():
    """直接测试分组函数"""
    print("直接测试分组函数...")
    
    try:
        db = next(get_db())
        
        # 1. 测试服务层
        print("1. 测试服务层...")
        groups = get_all_groups(db)
        print(f"✅ 服务层获取到 {len(groups)} 个分组")
        
        # 2. 测试路由函数
        print("2. 测试路由函数...")
        try:
            route_groups = read_groups(db=db)
            print(f"✅ 路由函数获取到 {len(route_groups)} 个分组")
            
            # 打印第一个分组的详细信息
            if route_groups:
                first_group = route_groups[0]
                print(f"   第一个分组: {first_group}")
                print(f"   类型: {type(first_group)}")
                
        except Exception as e:
            print(f"❌ 路由函数失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 3. 测试单个分组
        print("3. 测试单个分组...")
        try:
            if groups:
                group_id = groups[0].id
                single_group = read_group(group_id, db=db)
                print(f"✅ 获取单个分组成功: {single_group}")
        except Exception as e:
            print(f"❌ 获取单个分组失败: {e}")
            import traceback
            traceback.print_exc()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_serialization():
    """测试schema序列化"""
    print("\n测试schema序列化...")
    
    try:
        from app.schemas.group import GroupOut
        from app.models.group import Group
        
        db = next(get_db())
        
        # 获取一个分组
        group = db.query(Group).first()
        
        if group:
            print(f"原始分组对象: {group}")
            print(f"分组字段: id={group.id}, group_name={group.group_name}, description={group.description}")
            
            # 转换为GroupOut
            group_out = GroupOut.model_validate(group)
            print(f"✅ GroupOut对象: {group_out}")
            
            # 转换为字典
            group_dict = group_out.model_dump()
            print(f"✅ 字典格式: {group_dict}")
            
            # 测试JSON序列化
            import json
            group_json = json.dumps(group_dict, default=str)
            print(f"✅ JSON格式: {group_json}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 直接测试分组API...")
    print("="*50)
    
    # 1. 直接测试分组函数
    test_direct_groups()
    
    # 2. 测试序列化
    test_schema_serialization()
    
    print("\n" + "="*50)
    print("🎯 测试完成")

if __name__ == "__main__":
    main()
