#!/usr/bin/env python3
"""
测试优化后的任务创建功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_backend_apis():
    """测试后端API"""
    print("🔧 测试后端API...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试设备API
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ 设备API正常，返回 {len(devices)} 个设备")
        else:
            print(f"❌ 设备API失败: {response.status_code}")
            return False
            
        # 测试分组API
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            print(f"✅ 分组API正常，返回 {len(groups)} 个分组")
        else:
            print(f"❌ 分组API失败: {response.status_code}")
            return False
            
        # 测试任务创建API
        test_task = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "test_blogger_123",
                "like_id": "test_like_456"
            },
            "target_scope": "single",
            "target_id": 1,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        response = requests.post(f"{base_url}/tasks/", json=test_task, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建API正常，任务ID: {result.get('id')}")
            return True
        else:
            print(f"❌ 任务创建API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 后端API测试异常: {e}")
        return False

def test_frontend_config():
    """测试前端配置"""
    print("\n🖥️ 测试前端配置...")
    
    try:
        # 检查前端配置文件
        config_file = "frontend/config.py"
        if os.path.exists(config_file):
            print("✅ 前端配置文件存在")
            
            # 检查配置内容
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
                
            if 'TASK_TYPES' in config_content:
                print("✅ 任务类型配置存在")
            else:
                print("⚠️ 任务类型配置缺失")
                
            if 'TARGET_SCOPE' in config_content:
                print("✅ 目标范围配置存在")
            else:
                print("⚠️ 目标范围配置缺失")
                
            return True
        else:
            print("❌ 前端配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 前端配置测试异常: {e}")
        return False

def test_settings_functionality():
    """测试设置保存加载功能"""
    print("\n💾 测试设置功能...")
    
    try:
        # 创建测试设置
        test_settings = {
            "task_type": "点赞任务",
            "target_scope": "设备分组",
            "blogger_id": "test_blogger_789",
            "like_id": "test_like_101112",
            "delay_group": "3",
            "delay_like": "1.5"
        }
        
        # 确保设置目录存在
        settings_dir = "frontend/settings"
        if not os.path.exists(settings_dir):
            os.makedirs(settings_dir)
            print("✅ 设置目录已创建")
        else:
            print("✅ 设置目录已存在")
            
        # 保存测试设置
        settings_file = os.path.join(settings_dir, "quick_task_settings.json")
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✅ 测试设置已保存")
        
        # 加载测试设置
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
            
        if loaded_settings == test_settings:
            print("✅ 设置保存加载功能正常")
            return True
        else:
            print("❌ 设置保存加载不一致")
            return False
            
    except Exception as e:
        print(f"❌ 设置功能测试异常: {e}")
        return False

def test_task_creation_scenarios():
    """测试任务创建场景"""
    print("\n🎯 测试任务创建场景...")
    
    scenarios = [
        {
            "name": "点赞任务",
            "task_type": "like",
            "parameters": {
                "blogger_id": "scenario_blogger_1",
                "like_id": "scenario_like_1"
            }
        },
        {
            "name": "签到任务", 
            "task_type": "sign",
            "parameters": {
                "count": 1
            }
        },
        {
            "name": "主页关注任务",
            "task_type": "inex",
            "parameters": {
                "user_id": "scenario_user_1",
                "count": 1
            }
        }
    ]
    
    try:
        base_url = "http://localhost:8000"
        success_count = 0
        
        for scenario in scenarios:
            task_data = {
                "task_type": scenario["task_type"],
                "parameters": scenario["parameters"],
                "target_scope": "single",
                "target_id": 1,
                "delay_group": 2000,
                "delay_like": 1000
            }
            
            response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {scenario['name']} 创建成功 (ID: {result.get('id')})")
                success_count += 1
            else:
                print(f"❌ {scenario['name']} 创建失败: {response.status_code}")
                
        return success_count == len(scenarios)
        
    except Exception as e:
        print(f"❌ 任务创建场景测试异常: {e}")
        return False

def check_frontend_structure():
    """检查前端文件结构"""
    print("\n📁 检查前端文件结构...")
    
    required_files = [
        "frontend/main.py",
        "frontend/config.py",
        "frontend/widgets/task_manager.py",
        "frontend/utils/time_utils.py",
        "frontend/utils/api_client.py"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} 缺失")
            
    if not missing_files:
        print("✅ 前端文件结构完整")
        return True
    else:
        print(f"⚠️ 缺少 {len(missing_files)} 个文件")
        return False

def main():
    """主函数"""
    print("🧪 优化后的任务创建功能测试")
    print("="*60)
    
    tests = [
        ("前端文件结构", check_frontend_structure),
        ("后端API", test_backend_apis),
        ("前端配置", test_frontend_config),
        ("设置功能", test_settings_functionality),
        ("任务创建场景", test_task_creation_scenarios)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 所有测试通过！任务创建优化成功！")
        
        print("\n✅ 优化功能总结:")
        print("1. 简化的任务创建界面 - 直接在任务管理页面顶部")
        print("2. 统一的参数输入 - 所有任务都使用博主ID和点赞ID")
        print("3. 多分组选择 - 支持勾选多个分组批量创建任务")
        print("4. 设置保存加载 - 参数可以保存，下次打开自动加载")
        print("5. 北京时间显示 - 所有时间统一显示北京时间")
        print("6. 设备任务日志 - 任务管理页面显示设备执行日志")
        
        print("\n💡 使用说明:")
        print("1. 任务类型：支持签到、点赞、主页关注三种任务")
        print("2. 目标选择：可选择单个设备或多个分组")
        print("3. 参数输入：博主ID和点赞ID适用于所有任务类型")
        print("4. 延迟设置：分组延迟控制分组间间隔，操作延迟控制单次操作间隔")
        print("5. 设置保存：点击保存设置按钮保存当前配置")
        print("6. 批量创建：选择多个分组可以一次性为所有分组创建任务")
        
        print("\n🚀 新功能特点:")
        print("- 🎯 简洁易用：一键创建，无需弹窗")
        print("- 📋 参数统一：所有任务使用相同参数格式")
        print("- 🔄 设置持久：配置自动保存和加载")
        print("- 📊 批量操作：支持多分组同时创建")
        print("- ⏰ 北京时间：时间显示符合使用习惯")
        print("- 📝 执行日志：实时查看设备执行结果")
        
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        
        print("\n🔍 故障排除:")
        print("1. 确保后端服务器正在运行")
        print("2. 检查前端文件是否完整")
        print("3. 验证API端点是否正确")
        print("4. 查看设置目录权限")

if __name__ == "__main__":
    main()
