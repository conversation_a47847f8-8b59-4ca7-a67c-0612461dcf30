#!/usr/bin/env python3
"""
调试调度器状态
"""

import requests
import time
from datetime import datetime

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def check_scheduler_status():
    """检查调度器状态"""
    log_with_time("=== 调度器状态检查 ===")
    
    try:
        # 检查健康状态
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        log_with_time(f"健康检查: {health_response.status_code}")
        
        # 检查任务190状态
        task_response = requests.get("http://localhost:8000/tasks/190/status", timeout=5)
        if task_response.status_code == 200:
            task_data = task_response.json()
            log_with_time(f"任务190状态: {task_data.get('status')}")
            log_with_time(f"队列信息: {task_data.get('queues')}")
        
        # 检查设备9状态
        device_response = requests.get("http://localhost:8000/devices/9", timeout=5)
        if device_response.status_code == 200:
            device_data = device_response.json()
            log_with_time(f"设备9状态: 在线={device_data.get('online_status')}")
        
        return True
        
    except Exception as e:
        log_with_time(f"检查失败: {e}")
        return False

def create_simple_task():
    """创建一个简单的任务测试"""
    log_with_time("=== 创建简单测试任务 ===")
    
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test": "debug"},
        "target_scope": "single",
        "target_id": 9,  # 直接指定在线设备9
        "delay_group": 1000,  # 1秒分组延迟
        "delay_like": 500     # 0.5秒操作延迟
    }
    
    try:
        start_time = time.time()
        response = requests.post("http://localhost:8000/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ 测试任务创建成功，ID: {task_id}, 耗时: {creation_time:.1f}ms")
            
            # 监控任务状态
            log_with_time("👀 监控任务状态变化...")
            monitor_task(task_id, 30)
            
        else:
            log_with_time(f"❌ 任务创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            
    except Exception as e:
        log_with_time(f"❌ 创建任务失败: {e}")

def monitor_task(task_id, max_wait=30):
    """监控任务状态"""
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"http://localhost:8000/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status')
                
                if current_status != last_status:
                    elapsed = time.time() - start_time
                    log_with_time(f"📊 任务{task_id} 状态: {last_status} → {current_status} (耗时: {elapsed:.1f}s)")
                    last_status = current_status
                
                if current_status in ['done', 'failed', 'cancelled']:
                    log_with_time(f"🏁 任务{task_id} 完成，最终状态: {current_status}")
                    return current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 监控任务{task_id}出错: {e}")
        
        time.sleep(2)
    
    log_with_time(f"⏰ 任务{task_id} 监控超时，状态仍为: {last_status}")
    return last_status

def check_task_190():
    """专门检查任务190"""
    log_with_time("=== 检查任务190详情 ===")
    
    try:
        # 获取任务详情
        task_response = requests.get("http://localhost:8000/tasks/190", timeout=5)
        if task_response.status_code == 200:
            task_data = task_response.json()
            log_with_time(f"任务190类型: {task_data.get('task_type')}")
            log_with_time(f"目标范围: {task_data.get('target_scope')}")
            log_with_time(f"目标ID: {task_data.get('target_id')}")
            log_with_time(f"分组延迟: {task_data.get('delay_group')}ms")
            log_with_time(f"操作延迟: {task_data.get('delay_like')}ms")
            log_with_time(f"创建时间: {task_data.get('create_time')}")
        
        # 获取状态
        status_response = requests.get("http://localhost:8000/tasks/190/status", timeout=5)
        if status_response.status_code == 200:
            status_data = status_response.json()
            log_with_time(f"当前状态: {status_data.get('status')}")
            
            queues = status_data.get('queues', {})
            log_with_time(f"队列统计: 总计={queues.get('total')}, pending={queues.get('pending')}, running={queues.get('running')}")
        
    except Exception as e:
        log_with_time(f"检查任务190失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 调度器调试工具")
    print("=" * 60)
    
    # 1. 检查基本状态
    if not check_scheduler_status():
        log_with_time("❌ 基本状态检查失败，退出")
        return
    
    print("\n" + "-" * 40)
    
    # 2. 检查任务190
    check_task_190()
    
    print("\n" + "-" * 40)
    
    # 3. 创建简单测试任务
    create_simple_task()
    
    print("\n" + "=" * 60)
    log_with_time("🔍 调试完成")
    print("💡 如果任务一直是pending状态，可能的原因:")
    print("   1. 调度器没有启动")
    print("   2. 设备虽然在线但无法接收任务")
    print("   3. 任务分发逻辑有问题")
    print("   4. WebSocket连接问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
