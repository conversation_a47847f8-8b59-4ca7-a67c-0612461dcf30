#!/usr/bin/env python3
"""
测试WebSocket修复的脚本
验证Session管理和任务完成处理是否正常工作
"""

import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# 模拟数据库相关的导入
class MockTaskQueue:
    def __init__(self, task_id, device_id, status='pending'):
        self.task_id = task_id
        self.device_id = device_id
        self.status = status
        self.finish_time = None
        self.result_summary = None

class MockDevice:
    def __init__(self, id, device_number, has_task=False):
        self.id = id
        self.device_number = device_number
        self.has_task = has_task

def mock_get_db():
    """模拟数据库会话"""
    db = Mock()
    db.query.return_value.filter.return_value.first.return_value = MockTaskQueue(1, 1)
    db.commit = Mock()
    db.rollback = Mock()
    db.close = Mock()
    return db

async def test_task_completion_handling():
    """测试任务完成处理逻辑"""
    print("=== 测试任务完成处理逻辑 ===")
    
    # 导入修复后的模块
    try:
        from app.websocket.ws_manager import ConnectionManager
        print("✓ 成功导入 ConnectionManager")
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建连接管理器实例
    try:
        manager = ConnectionManager()
        print("✓ 成功创建 ConnectionManager 实例")
    except Exception as e:
        print(f"✗ 创建实例失败: {e}")
        return False
    
    # 模拟任务完成数据
    completion_data = {
        'task_id': 1,
        'device_id': 1,
        'status': 'success',
        'timestamp': datetime.utcnow().isoformat()
    }
    
    # 测试任务完成处理
    try:
        with patch('app.websocket.ws_manager.get_db', side_effect=lambda: mock_get_db()):
            await manager._handle_task_completion('test_device', completion_data)
        print("✓ 任务完成处理成功")
        return True
    except Exception as e:
        print(f"✗ 任务完成处理失败: {e}")
        return False

async def test_next_task_processing():
    """测试下一任务处理逻辑"""
    print("\n=== 测试下一任务处理逻辑 ===")
    
    try:
        from app.websocket.ws_manager import ConnectionManager
        manager = ConnectionManager()
        
        completion_data = {
            'task_id': 1,
            'device_id': 1,
            'status': 'success'
        }
        
        with patch('app.websocket.ws_manager.get_db', side_effect=lambda: mock_get_db()):
            await manager._process_next_task('test_device', completion_data)
        print("✓ 下一任务处理成功")
        return True
    except Exception as e:
        print(f"✗ 下一任务处理失败: {e}")
        return False

def test_session_management():
    """测试Session管理"""
    print("\n=== 测试Session管理 ===")
    
    try:
        # 验证没有重复的__init__方法
        from app.websocket.ws_manager import ConnectionManager
        import inspect
        
        # 获取类的所有方法
        methods = inspect.getmembers(ConnectionManager, predicate=inspect.isfunction)
        init_methods = [name for name, method in methods if name == '__init__']
        
        if len(init_methods) <= 1:
            print("✓ 没有重复的__init__方法")
            return True
        else:
            print(f"✗ 发现重复的__init__方法: {len(init_methods)}个")
            return False
    except Exception as e:
        print(f"✗ Session管理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试WebSocket修复...")
    
    tests = [
        test_session_management(),
        await test_task_completion_handling(),
        await test_next_task_processing()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！WebSocket修复成功")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    asyncio.run(main())
