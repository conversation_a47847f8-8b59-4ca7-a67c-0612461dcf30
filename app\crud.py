from sqlalchemy.orm import Session
from app import models, schemas
from datetime import datetime

# Device CRUD
def get_device(db: Session, device_id: int):
    return db.query(models.Device).filter(models.Device.id == device_id).first()

def get_device_by_number(db: Session, device_number: str):
    return db.query(models.Device).filter(models.Device.device_number == device_number).first()

def create_device(db: Session, device: schemas.device.DeviceCreate):
    db_device = models.Device(
        device_number=device.device_number,
        device_ip=device.device_ip,
        online_status="offline",
        account_status="not_logged_in"
    )
    db.add(db_device)
    db.commit()
    db.refresh(db_device)
    return db_device

def update_device(db: Session, device_id: int, device_update: schemas.device.DeviceUpdate):
    device = get_device(db, device_id)
    if not device:
        return None
    for var, value in vars(device_update).items():
        if value is not None:
            setattr(device, var, value)
    device.last_heartbeat = datetime.utcnow()
    db.commit()
    db.refresh(device)
    return device

# Task CRUD
def create_task(db: Session, task: schemas.task.TaskCreate):
    db_task = models.Task(
        task_type=task.task_type,
        parameters=task.parameters,
        target_scope=task.target_scope,
        target_id=task.target_id,
        delay_group=task.delay_group,
        delay_like=task.delay_like,
        status="pending"
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task

def get_task(db: Session, task_id: int):
    return db.query(models.Task).filter(models.Task.id == task_id).first()

def get_tasks(db: Session, skip: int = 0, limit: int = 1000):
    """获取任务列表"""
    return db.query(models.Task).order_by(models.Task.id.desc()).offset(skip).limit(limit).all()

def get_devices(db: Session, skip: int = 0, limit: int = 100):
    """获取设备列表"""
    return db.query(models.Device).offset(skip).limit(limit).all()

def update_task_status(db: Session, task_id: int, status: str):
    """更新任务状态"""
    task = get_task(db, task_id)
    if task:
        task.status = status
        task.update_time = datetime.utcnow()
        db.commit()
        db.refresh(task)
    return task

# Group CRUD
def get_groups(db: Session, skip: int = 0, limit: int = 100):
    """获取分组列表"""
    return db.query(models.Group).offset(skip).limit(limit).all()

def get_group(db: Session, group_id: int):
    """获取单个分组"""
    return db.query(models.Group).filter(models.Group.id == group_id).first()

def create_group(db: Session, group: schemas.group.GroupCreate):
    """创建分组"""
    db_group = models.Group(
        name=group.name,
        description=group.description
    )
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    return db_group

def update_group(db: Session, group_id: int, group_update: schemas.group.GroupUpdate):
    """更新分组"""
    group = get_group(db, group_id)
    if not group:
        return None
    for var, value in vars(group_update).items():
        if value is not None:
            setattr(group, var, value)
    db.commit()
    db.refresh(group)
    return group

def delete_group(db: Session, group_id: int):
    """删除分组"""
    group = get_group(db, group_id)
    if group:
        db.delete(group)
        db.commit()
        return True
    return False
