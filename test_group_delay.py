#!/usr/bin/env python3
"""
分组延迟功能测试脚本
用于验证分组延迟是否正常工作
"""

import asyncio
import sys
import os
import time
import requests
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# API配置
API_BASE_URL = "http://localhost:8000"

def log_info(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ℹ️  {message}")

def log_success(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ✅ {message}")

def log_error(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ❌ {message}")

def log_warn(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ⚠️  {message}")

def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            log_success("API连接正常")
            return True
        else:
            log_error(f"API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        log_error(f"API连接失败: {e}")
        return False

def get_devices():
    """获取设备列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            log_info(f"获取到 {len(devices)} 个设备")
            return devices
        else:
            log_error(f"获取设备列表失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取设备列表失败: {e}")
        return []

def get_groups():
    """获取分组列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/groups", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            log_info(f"获取到 {len(groups)} 个分组")
            return groups
        else:
            log_error(f"获取分组列表失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取分组列表失败: {e}")
        return []

def create_test_task(target_scope, target_id, delay_group_seconds, delay_like_seconds):
    """创建测试任务"""
    try:
        # 前端发送毫秒，所以这里转换
        delay_group_ms = int(delay_group_seconds * 1000)
        delay_like_ms = int(delay_like_seconds * 1000)
        
        task_data = {
            "task_type": "sign",
            "parameters": {"count": 1},
            "target_scope": target_scope,
            "target_id": target_id,
            "delay_group": delay_group_ms,  # 发送毫秒
            "delay_like": delay_like_ms     # 发送毫秒
        }
        
        log_info(f"创建测试任务: 分组延迟={delay_group_seconds}秒, 操作延迟={delay_like_seconds}秒")
        log_info(f"发送数据: delay_group={delay_group_ms}ms, delay_like={delay_like_ms}ms")
        
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_success(f"任务创建成功，ID: {task.get('id')}")
            return task
        else:
            log_error(f"任务创建失败，状态码: {response.status_code}")
            log_error(f"响应内容: {response.text}")
            return None
    except Exception as e:
        log_error(f"创建任务失败: {e}")
        return None

def monitor_task_execution(task_id, duration=60):
    """监控任务执行过程"""
    log_info(f"开始监控任务 {task_id} 的执行过程（{duration}秒）...")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < duration:
        try:
            # 获取任务状态
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                
                # 如果状态有变化，记录日志
                current_status = status.get('status')
                if current_status != last_status:
                    log_info(f"任务状态变化: {last_status} -> {current_status}")
                    last_status = current_status
                
                # 显示队列状态
                queues = status.get('queues', {})
                if queues:
                    total = queues.get('total', 0)
                    pending = queues.get('pending', 0)
                    running = queues.get('running', 0)
                    done = queues.get('done', 0)
                    failed = queues.get('failed', 0)
                    
                    log_info(f"队列状态: 总计={total}, 待处理={pending}, 执行中={running}, 完成={done}, 失败={failed}")
                
                # 如果任务完成，退出监控
                if current_status in ['done', 'failed', 'cancelled']:
                    log_success(f"任务执行完成，最终状态: {current_status}")
                    break
                    
            else:
                log_warn(f"获取任务状态失败，状态码: {response.status_code}")
                
        except Exception as e:
            log_warn(f"监控过程中出错: {e}")
        
        time.sleep(3)  # 每3秒检查一次
    
    log_info("任务监控结束")

def test_single_device_delay():
    """测试单设备延迟"""
    log_info("=== 测试单设备延迟 ===")
    
    devices = get_devices()
    if not devices:
        log_error("没有可用设备，跳过单设备测试")
        return False
    
    # 选择第一个设备
    device = devices[0]
    device_id = device.get('id')
    device_number = device.get('device_number', 'unknown')
    
    log_info(f"使用设备: {device_number} (ID: {device_id})")
    
    # 创建任务，设置5秒分组延迟，2秒操作延迟
    task = create_test_task("single", device_id, 5.0, 2.0)
    if not task:
        return False
    
    # 监控任务执行
    monitor_task_execution(task.get('id'), 30)
    return True

def test_group_delay():
    """测试分组延迟"""
    log_info("=== 测试分组延迟 ===")
    
    groups = get_groups()
    if not groups:
        log_error("没有可用分组，跳过分组测试")
        return False
    
    # 选择第一个分组
    group = groups[0]
    group_id = group.get('id')
    group_name = group.get('group_name', 'unknown')
    
    log_info(f"使用分组: {group_name} (ID: {group_id})")
    
    # 创建任务，设置3秒分组延迟，1秒操作延迟
    task = create_test_task("group", group_id, 3.0, 1.0)
    if not task:
        return False
    
    # 监控任务执行
    monitor_task_execution(task.get('id'), 60)
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 微博任务管理系统 - 分组延迟功能测试")
    print("=" * 60)
    
    # 测试API连接
    if not test_api_connection():
        log_error("API连接失败，请确保后端服务正在运行")
        return
    
    # 获取系统信息
    devices = get_devices()
    groups = get_groups()
    
    if not devices and not groups:
        log_error("系统中没有设备和分组，无法进行测试")
        return
    
    print("\n" + "=" * 40)
    print("📊 系统状态:")
    print(f"   设备数量: {len(devices)}")
    print(f"   分组数量: {len(groups)}")
    print("=" * 40)
    
    # 显示设备信息
    if devices:
        print("\n📱 设备列表:")
        for device in devices[:5]:  # 只显示前5个
            device_id = device.get('id')
            device_number = device.get('device_number', 'unknown')
            status = device.get('online_status', 'unknown')
            print(f"   - {device_number} (ID: {device_id}, 状态: {status})")
        if len(devices) > 5:
            print(f"   ... 还有 {len(devices) - 5} 个设备")
    
    # 显示分组信息
    if groups:
        print("\n👥 分组列表:")
        for group in groups[:5]:  # 只显示前5个
            group_id = group.get('id')
            group_name = group.get('group_name', 'unknown')
            print(f"   - {group_name} (ID: {group_id})")
        if len(groups) > 5:
            print(f"   ... 还有 {len(groups) - 5} 个分组")
    
    print("\n" + "=" * 40)
    print("🚀 开始延迟测试...")
    print("=" * 40)
    
    # 执行测试
    success_count = 0
    
    if devices:
        if test_single_device_delay():
            success_count += 1
        print()
    
    if groups:
        if test_group_delay():
            success_count += 1
    
    # 测试结果
    print("\n" + "=" * 40)
    print("📋 测试结果:")
    if success_count > 0:
        log_success(f"测试完成，成功执行 {success_count} 个测试")
        print("\n💡 观察要点:")
        print("   1. 查看日志中的延迟应用信息")
        print("   2. 分组延迟应该在设备间生效")
        print("   3. 操作延迟应该在任务内部生效")
        print("\n📝 查看详细日志:")
        print("   journalctl -u wb-system -f")
        print("   或使用: ./wb-system-ctl.sh logs")
    else:
        log_error("所有测试都失败了")
    print("=" * 40)

if __name__ == "__main__":
    main()
