#!/usr/bin/env python3
"""
分组延迟功能测试脚本
用于验证分组延迟是否正常工作
"""

import sys
import os
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# API配置
API_BASE_URL = "http://localhost:8000"

def log_info(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ℹ️  {message}")

def log_success(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ✅ {message}")

def log_error(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ❌ {message}")

def log_warn(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] ⚠️  {message}")

def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            log_success("API连接正常")
            return True
        else:
            log_error(f"API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        log_error(f"API连接失败: {e}")
        return False

def get_devices():
    """获取设备列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/devices", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            log_info(f"获取到 {len(devices)} 个设备")
            return devices
        else:
            log_error(f"获取设备列表失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取设备列表失败: {e}")
        return []

def get_groups():
    """获取分组列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/groups", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            log_info(f"获取到 {len(groups)} 个分组")
            return groups
        else:
            log_error(f"获取分组列表失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        log_error(f"获取分组列表失败: {e}")
        return []

def create_test_task(target_scope, target_id, delay_group_seconds, delay_like_seconds):
    """创建测试任务"""
    try:
        # 前端发送毫秒，所以这里转换
        delay_group_ms = int(delay_group_seconds * 1000)
        delay_like_ms = int(delay_like_seconds * 1000)
        
        task_data = {
            "task_type": "sign",
            "parameters": {"count": 1},
            "target_scope": target_scope,
            "target_id": target_id,
            "delay_group": delay_group_ms,  # 发送毫秒
            "delay_like": delay_like_ms     # 发送毫秒
        }
        
        log_info(f"创建测试任务: 分组延迟={delay_group_seconds}秒, 操作延迟={delay_like_seconds}秒")
        log_info(f"发送数据: delay_group={delay_group_ms}ms, delay_like={delay_like_ms}ms")
        
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_success(f"任务创建成功，ID: {task.get('id')}")
            return task
        else:
            log_error(f"任务创建失败，状态码: {response.status_code}")
            log_error(f"响应内容: {response.text}")
            return None
    except Exception as e:
        log_error(f"创建任务失败: {e}")
        return None

def monitor_task_execution(task_id, duration=60):
    """监控任务执行过程"""
    log_info(f"开始监控任务 {task_id} 的执行过程（{duration}秒）...")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < duration:
        try:
            # 获取任务状态
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                
                # 如果状态有变化，记录日志
                current_status = status.get('status')
                if current_status != last_status:
                    log_info(f"任务状态变化: {last_status} -> {current_status}")
                    last_status = current_status
                
                # 显示队列状态
                queues = status.get('queues', {})
                if queues:
                    total = queues.get('total', 0)
                    pending = queues.get('pending', 0)
                    running = queues.get('running', 0)
                    done = queues.get('done', 0)
                    failed = queues.get('failed', 0)
                    
                    log_info(f"队列状态: 总计={total}, 待处理={pending}, 执行中={running}, 完成={done}, 失败={failed}")
                
                # 如果任务完成，退出监控
                if current_status in ['done', 'failed', 'cancelled']:
                    log_success(f"任务执行完成，最终状态: {current_status}")
                    break
                    
            else:
                log_warn(f"获取任务状态失败，状态码: {response.status_code}")
                
        except Exception as e:
            log_warn(f"监控过程中出错: {e}")
        
        time.sleep(3)  # 每3秒检查一次
    
    log_info("任务监控结束")



def simple_delay_test():
    """简单的延迟测试"""
    print("=" * 50)
    print("🧪 分组延迟功能测试")
    print("=" * 50)

    # 测试API连接
    if not test_api_connection():
        log_error("API连接失败，请确保后端服务正在运行")
        log_info("请先启动后端服务: ./start_backend.sh")
        return

    # 获取系统信息
    devices = get_devices()
    groups = get_groups()

    print(f"\n📊 系统状态: 设备数量={len(devices)}, 分组数量={len(groups)}")

    # 选择测试目标
    target_scope = None
    target_id = None
    target_name = None

    if groups:
        # 优先使用分组测试
        group = groups[0]
        target_scope = "group"
        target_id = group.get('id')
        target_name = f"分组: {group.get('group_name')}"
        log_info(f"使用分组进行测试: {target_name} (ID: {target_id})")
    elif devices:
        # 使用单设备测试
        device = devices[0]
        target_scope = "single"
        target_id = device.get('id')
        target_name = f"设备: {device.get('device_number')}"
        log_info(f"使用设备进行测试: {target_name} (ID: {target_id})")
    else:
        log_error("系统中没有设备和分组，无法进行测试")
        return

    # 创建测试任务
    print(f"\n🚀 创建测试任务...")
    print(f"   目标: {target_name}")
    print(f"   分组延迟: 5秒")
    print(f"   操作延迟: 2秒")

    task = create_test_task(target_scope, target_id, 5.0, 2.0)
    if not task:
        log_error("任务创建失败")
        return

    task_id = task.get('id')
    log_success(f"任务创建成功，ID: {task_id}")

    # 监控任务执行
    print(f"\n👀 监控任务执行过程...")
    print(f"   任务ID: {task_id}")
    print(f"   监控时长: 60秒")
    print(f"   查看详细日志: journalctl -u wb-system -f")
    print()

    monitor_task_execution(task_id, 60)

    print("\n" + "=" * 50)
    print("✅ 测试完成")
    print("💡 在日志中查找以下关键信息:")
    print("   - ⏰ 应用分组延迟: 设备X 等待 Y秒")
    print("   - ⏰ 应用操作延迟: Z秒")
    print("   - 🚀 开始处理任务")
    print("   - ✅ 模拟任务执行完成")
    print("=" * 50)

def main():
    """主函数"""
    simple_delay_test()

if __name__ == "__main__":
    main()
