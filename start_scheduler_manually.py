#!/usr/bin/env python3
"""
手动启动调度器
"""

import sys
import asyncio
sys.path.append('.')

async def start_scheduler():
    """手动启动调度器"""
    print("🚀 手动启动调度器...")
    
    try:
        from app.services.optimized_scheduler import optimized_scheduler
        
        print(f"📊 启动前状态: 运行={optimized_scheduler.is_running}")
        
        # 手动启动调度器
        await optimized_scheduler.start()
        
        print(f"📊 启动后状态: 运行={optimized_scheduler.is_running}")
        print(f"📊 设备队列数量: {len(optimized_scheduler.device_queues)}")
        
        # 检查设备队列
        for device_id, queue in optimized_scheduler.device_queues.items():
            print(f"📊 设备{device_id}队列大小: {queue.qsize()}")
        
        print("✅ 调度器启动完成")
        
        # 等待一段时间让调度器工作
        print("⏳ 等待10秒让调度器处理任务...")
        await asyncio.sleep(10)
        
        # 再次检查队列状态
        print("📊 10秒后的队列状态:")
        for device_id, queue in optimized_scheduler.device_queues.items():
            print(f"📊 设备{device_id}队列大小: {queue.qsize()}")
        
    except Exception as e:
        print(f"❌ 启动调度器失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(start_scheduler())
