#!/usr/bin/env python3
"""
更新数据库中的任务类型枚举
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db, engine
from sqlalchemy import text

def update_task_type_enum():
    """更新任务类型枚举以包含inex"""
    print("更新数据库任务类型枚举...")
    
    try:
        # 使用engine直接执行SQL
        with engine.connect() as conn:
            # 检查当前枚举值
            print("1. 检查当前枚举值...")
            result = conn.execute(text("""
                SELECT COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'tasks' 
                AND COLUMN_NAME = 'task_type'
            """))
            
            current_enum = result.fetchone()
            if current_enum:
                print(f"   当前枚举: {current_enum[0]}")
                
                # 检查是否已包含inex
                if 'inex' in current_enum[0]:
                    print("✅ 枚举已包含 'inex'，无需更新")
                    return True
            
            # 更新枚举类型
            print("2. 更新枚举类型...")
            
            # 方法1: 直接修改枚举
            try:
                conn.execute(text("""
                    ALTER TABLE tasks 
                    MODIFY COLUMN task_type ENUM('sign', 'like', 'page_sign', 'inex')
                """))
                conn.commit()
                print("✅ 枚举更新成功")
                
            except Exception as e:
                print(f"方法1失败: {e}")
                
                # 方法2: 先删除约束，再重新创建
                print("3. 尝试方法2: 重新创建枚举...")
                
                # 添加临时列
                conn.execute(text("""
                    ALTER TABLE tasks 
                    ADD COLUMN task_type_new ENUM('sign', 'like', 'page_sign', 'inex')
                """))
                
                # 复制数据
                conn.execute(text("""
                    UPDATE tasks 
                    SET task_type_new = task_type
                """))
                
                # 删除旧列
                conn.execute(text("""
                    ALTER TABLE tasks 
                    DROP COLUMN task_type
                """))
                
                # 重命名新列
                conn.execute(text("""
                    ALTER TABLE tasks 
                    CHANGE COLUMN task_type_new task_type ENUM('sign', 'like', 'page_sign', 'inex') NOT NULL
                """))
                
                conn.commit()
                print("✅ 枚举重新创建成功")
            
            # 验证更新结果
            print("4. 验证更新结果...")
            result = conn.execute(text("""
                SELECT COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'tasks' 
                AND COLUMN_NAME = 'task_type'
            """))
            
            new_enum = result.fetchone()
            if new_enum:
                print(f"   更新后枚举: {new_enum[0]}")
                
                if 'inex' in new_enum[0]:
                    print("✅ 验证成功，枚举已包含 'inex'")
                    return True
                else:
                    print("❌ 验证失败，枚举仍不包含 'inex'")
                    return False
            
        return False
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inex_creation():
    """测试创建inex任务"""
    print("\n测试创建inex任务...")
    
    try:
        db = next(get_db())
        
        # 直接插入inex任务
        result = db.execute(text("""
            INSERT INTO tasks (task_type, parameters, target_scope, target_id, delay_group, delay_like, status)
            VALUES ('inex', '{"user_id": "test123", "count": 1}', 'single', 1, 1000, 500, 'pending')
        """))
        
        db.commit()
        
        # 获取插入的任务ID
        task_id = result.lastrowid
        print(f"✅ inex任务创建成功，ID: {task_id}")
        
        # 验证任务
        result = db.execute(text("SELECT * FROM tasks WHERE id = :task_id"), {"task_id": task_id})
        task = result.fetchone()
        
        if task:
            print(f"   任务类型: {task[1]}")  # task_type是第二列
            print(f"   参数: {task[2]}")      # parameters是第三列
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 更新数据库任务类型枚举...")
    print("="*50)
    
    # 1. 更新枚举
    if update_task_type_enum():
        # 2. 测试创建inex任务
        if test_inex_creation():
            print("\n" + "="*50)
            print("🎉 数据库更新完成！")
            print("现在可以创建inex任务了")
            print("\n重新测试API:")
            print("python test_inex_task.py")
        else:
            print("\n❌ 测试创建inex任务失败")
    else:
        print("\n❌ 更新枚举失败")

if __name__ == "__main__":
    main()
