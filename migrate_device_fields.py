#!/usr/bin/env python3
"""
数据库迁移脚本：为Device表添加新字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db import engine

def migrate_device_table():
    """为Device表添加新字段"""
    print("🔄 开始迁移Device表...")

    try:
        with engine.connect() as connection:
            # 检查字段是否已存在 (MySQL语法)
            result = connection.execute(text("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'devices'
            """))
            columns = [row[0] for row in result.fetchall()]

            print(f"   当前字段: {columns}")

            # 添加current_task_id字段
            if 'current_task_id' not in columns:
                print("   添加 current_task_id 字段...")
                connection.execute(text("""
                    ALTER TABLE devices
                    ADD COLUMN current_task_id INT NULL,
                    ADD FOREIGN KEY (current_task_id) REFERENCES tasks(id)
                """))
                connection.commit()
                print("   ✅ current_task_id 字段添加成功")
            else:
                print("   ✅ current_task_id 字段已存在")

            # 添加current_task_type字段
            if 'current_task_type' not in columns:
                print("   添加 current_task_type 字段...")
                connection.execute(text("""
                    ALTER TABLE devices
                    ADD COLUMN current_task_type VARCHAR(50) NULL
                """))
                connection.commit()
                print("   ✅ current_task_type 字段添加成功")
            else:
                print("   ✅ current_task_type 字段已存在")

            # 验证字段添加
            result = connection.execute(text("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'devices'
            """))
            new_columns = [row[0] for row in result.fetchall()]

            print(f"   更新后字段: {new_columns}")

            if 'current_task_id' in new_columns and 'current_task_type' in new_columns:
                print("✅ Device表迁移成功")
                return True
            else:
                print("❌ Device表迁移失败")
                return False
                
    except Exception as e:
        print(f"❌ 迁移异常: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        from sqlalchemy.orm import sessionmaker
        from app.models.device import Device
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 尝试查询设备
            devices = db.query(Device).limit(1).all()
            
            if devices:
                device = devices[0]
                print(f"   测试设备: {device.device_number}")
                print(f"   current_task_id: {device.current_task_id}")
                print(f"   current_task_type: {device.current_task_type}")
                print("✅ 新字段可以正常访问")
                return True
            else:
                print("⚠️ 没有设备数据，无法验证")
                return True
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

def main():
    """主函数"""
    print("🗄️ Device表字段迁移...")
    print("="*50)
    
    # 执行迁移
    if migrate_device_table():
        # 验证迁移
        if verify_migration():
            print("\n🎉 迁移完成！")
            print("\n📋 新增字段:")
            print("- current_task_id: 当前执行的任务ID")
            print("- current_task_type: 当前任务类型")
            print("\n💡 现在可以启动后端服务器了")
        else:
            print("\n⚠️ 迁移完成但验证失败")
    else:
        print("\n❌ 迁移失败")

if __name__ == "__main__":
    main()
