#!/usr/bin/env python3
"""
修复API问题脚本
检查并修复常见的API端点问题
"""

import requests
import time
import subprocess
import sys

BASE_URL = "http://localhost:8000"

def check_server_running():
    """检查服务器是否运行"""
    try:
        response = requests.get(f"{BASE_URL}/ip", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_tasks_endpoint():
    """测试任务端点"""
    try:
        response = requests.get(f"{BASE_URL}/tasks/", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    print("🔧 API问题修复脚本")
    print("="*50)
    
    # 检查服务器状态
    print("1. 检查服务器状态...")
    if check_server_running():
        print("   ✅ 服务器正在运行")
    else:
        print("   ❌ 服务器未运行或无法访问")
        print("   💡 请先启动后端服务器: uvicorn app.main:app --reload")
        return
    
    # 测试任务端点
    print("\n2. 测试任务端点...")
    if test_tasks_endpoint():
        print("   ✅ GET /tasks/ 端点正常")
    else:
        print("   ❌ GET /tasks/ 端点有问题")
        print("   💡 修复建议:")
        print("      - 重启后端服务器")
        print("      - 检查 app/routes/task.py 中的路由定义")
        print("      - 确保 app/crud.py 中有 get_tasks() 方法")
        
        # 尝试重新测试
        print("\n   🔄 等待5秒后重新测试...")
        time.sleep(5)
        
        if test_tasks_endpoint():
            print("   ✅ 重新测试成功")
        else:
            print("   ❌ 重新测试仍然失败")
    
    # 测试其他关键端点
    print("\n3. 测试其他关键端点...")
    endpoints = [
        ("/devices/", "设备列表"),
        ("/groups/", "分组列表"),
        ("/ip", "IP信息")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {name}: {endpoint}")
            else:
                print(f"   ❌ {name}: {endpoint} (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {name}: {endpoint} (错误: {e})")
    
    # 提供修复建议
    print("\n" + "="*50)
    print("🛠️ 修复建议:")
    print("1. 如果 GET /tasks/ 仍然返回405错误:")
    print("   - 重启后端服务器: Ctrl+C 然后重新运行")
    print("   - 确保修改已保存到 app/routes/task.py")
    print("   - 检查是否有语法错误")
    
    print("\n2. 如果前端仍然无法连接:")
    print("   - 检查前端配置中的API_BASE_URL")
    print("   - 确保后端服务器在正确的端口运行")
    print("   - 检查防火墙设置")
    
    print("\n3. 重启后端服务器的命令:")
    print("   cd 到后端项目目录")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n4. 启动前端的命令:")
    print("   cd frontend")
    print("   python run.py")

if __name__ == "__main__":
    main()
