#!/bin/bash

# 微博任务管理系统 - 永久启动配置脚本
# 使用方法: sudo ./setup_daemon_service.sh

echo "=== 微博任务管理系统 - 永久启动配置 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    echo "使用方法: sudo $0"
    exit 1
fi

# 获取当前目录和用户
CURRENT_DIR=$(pwd)
CURRENT_USER=$(logname 2>/dev/null || echo $SUDO_USER)

if [ -z "$CURRENT_USER" ]; then
    log_error "无法确定当前用户"
    exit 1
fi

log_info "当前目录: $CURRENT_DIR"
log_info "当前用户: $CURRENT_USER"

# 检查必要文件
log_step "1. 检查项目文件"
if [ ! -f "app/main.py" ]; then
    log_error "未找到 app/main.py，请在项目根目录运行此脚本"
    exit 1
fi

if [ ! -d "venv" ]; then
    log_error "未找到虚拟环境，请先创建虚拟环境"
    exit 1
fi

log_info "✅ 项目文件检查通过"

# 检查并创建.env文件
log_step "2. 检查环境配置"
if [ ! -f ".env" ]; then
    log_warn "未找到.env文件，将创建默认配置"
    cat > .env << 'EOF'
# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=wb

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
EOF
    chown $CURRENT_USER:$CURRENT_USER .env
    log_info "✅ 已创建默认.env文件，请根据需要修改数据库密码"
else
    log_info "✅ .env文件已存在"
fi

# 创建日志目录
log_step "3. 创建日志目录"
mkdir -p logs
chown $CURRENT_USER:$CURRENT_USER logs
log_info "✅ 日志目录已创建: $CURRENT_DIR/logs"

# 停止现有服务（如果存在）
log_step "4. 停止现有服务"
if systemctl is-active --quiet wb-system 2>/dev/null; then
    log_info "停止现有wb-system服务..."
    systemctl stop wb-system
fi

# 停止可能运行的进程
if pgrep -f "uvicorn.*app.main:app" > /dev/null; then
    log_info "停止现有uvicorn进程..."
    pkill -f "uvicorn.*app.main:app"
    sleep 2
fi

# 创建systemd服务文件
log_step "5. 创建systemd服务"
cat > /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博任务管理系统
Documentation=https://github.com/your-repo/wb-system
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=$CURRENT_DIR/.env
ExecStart=$CURRENT_DIR/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=3
StartLimitBurst=5
StartLimitInterval=60

# 输出和日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=wb-system

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$CURRENT_DIR

[Install]
WantedBy=multi-user.target
EOF

log_info "✅ systemd服务文件已创建: /etc/systemd/system/wb-system.service"

# 重载systemd配置
log_step "6. 重载systemd配置"
systemctl daemon-reload
log_info "✅ systemd配置已重载"

# 启用服务（开机自启）
log_step "7. 启用开机自启"
systemctl enable wb-system.service
log_info "✅ 服务已设置为开机自启"

# 创建管理脚本
log_step "8. 创建管理脚本"
cat > wb-system-ctl.sh << 'EOF'
#!/bin/bash

# 微博任务管理系统控制脚本

case "$1" in
    start)
        echo "启动微博任务管理系统..."
        sudo systemctl start wb-system
        ;;
    stop)
        echo "停止微博任务管理系统..."
        sudo systemctl stop wb-system
        ;;
    restart)
        echo "重启微博任务管理系统..."
        sudo systemctl restart wb-system
        ;;
    status)
        sudo systemctl status wb-system
        ;;
    logs)
        sudo journalctl -u wb-system -f
        ;;
    enable)
        echo "启用开机自启..."
        sudo systemctl enable wb-system
        ;;
    disable)
        echo "禁用开机自启..."
        sudo systemctl disable wb-system
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|enable|disable}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  status  - 查看状态"
        echo "  logs    - 查看实时日志"
        echo "  enable  - 启用开机自启"
        echo "  disable - 禁用开机自启"
        exit 1
        ;;
esac
EOF

chmod +x wb-system-ctl.sh
chown $CURRENT_USER:$CURRENT_USER wb-system-ctl.sh
log_info "✅ 管理脚本已创建: $CURRENT_DIR/wb-system-ctl.sh"

# 显示配置完成信息
log_step "9. 配置完成"
echo ""
echo "🎉 微博任务管理系统永久启动配置完成！"
echo ""
echo "📍 服务信息:"
echo "   服务名称: wb-system"
echo "   配置文件: /etc/systemd/system/wb-system.service"
echo "   工作目录: $CURRENT_DIR"
echo "   运行用户: $CURRENT_USER"
echo "   开机自启: 已启用"
echo ""
echo "🔧 管理命令:"
echo "   启动服务: systemctl start wb-system"
echo "   停止服务: systemctl stop wb-system"
echo "   重启服务: systemctl restart wb-system"
echo "   查看状态: systemctl status wb-system"
echo "   查看日志: journalctl -u wb-system -f"
echo "   禁用自启: systemctl disable wb-system"
echo ""
echo "🚀 快捷管理:"
echo "   使用管理脚本: ./wb-system-ctl.sh {start|stop|restart|status|logs}"
echo ""

# 询问是否立即启动
read -p "是否立即启动服务？(Y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Nn]$ ]]; then
    log_info "服务已配置但未启动"
    echo "手动启动: systemctl start wb-system"
    echo "或使用: ./wb-system-ctl.sh start"
else
    log_step "10. 启动服务"
    systemctl start wb-system
    
    # 等待启动
    sleep 3
    
    # 检查状态
    if systemctl is-active --quiet wb-system; then
        log_info "✅ 服务启动成功！"
        echo ""
        echo "🌐 访问地址: http://43.138.50.5:8000"
        echo "📖 API文档: http://43.138.50.5:8000/docs"
        echo ""
        echo "查看实时日志: journalctl -u wb-system -f"
        echo "或使用: ./wb-system-ctl.sh logs"
    else
        log_error "❌ 服务启动失败"
        echo "查看错误日志: journalctl -u wb-system --no-pager"
        echo "或使用: ./wb-system-ctl.sh status"
    fi
fi

echo ""
echo "💡 提示:"
echo "   - 服务会在系统重启后自动启动"
echo "   - 如果服务异常退出，systemd会自动重启"
echo "   - 日志会自动记录到系统日志中"
echo "   - 现在可以安全退出终端，服务将继续运行"
