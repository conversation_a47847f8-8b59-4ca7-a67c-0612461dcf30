#!/usr/bin/env python3
"""
完整系统测试
包含所有参数的完整测试，验证调度器稳定性和分组延迟功能
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class TestDevice:
    """测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []  # 记录任务接收时间
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                task_id = data.get('task_id')
                log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id}")
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                # 忽略ack消息的日志
                pass
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（2秒）
        execution_time = 2.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_complete_task(task_name, task_type, group_id, delay_group_sec=10, delay_like_sec=5):
    """创建完整参数的测试任务"""
    
    # 根据任务类型设置不同的参数
    if task_type == "like":
        parameters = {
            "count": 1,
            "test_name": task_name,
            "like_id": "test_like_123456"
        }
    elif task_type == "sign":
        parameters = {
            "count": 1,
            "test_name": task_name,
            "sign_type": "daily"
        }
    elif task_type == "page_sign":
        parameters = {
            "count": 1,
            "test_name": task_name,
            "blogger_id": "test_blogger_789",
            "like_id": "test_page_456"
        }
    elif task_type == "inex":
        parameters = {
            "count": 1,
            "test_name": task_name,
            "user_id": "test_user_999"
        }
    else:
        parameters = {
            "count": 1,
            "test_name": task_name
        }
    
    task_data = {
        "task_type": task_type,
        "parameters": parameters,
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 🔥 重要：前端秒转后端毫秒
        "delay_like": int(delay_like_sec * 1000)     # 🔥 重要：前端秒转后端毫秒
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            log_with_time(f"   类型: {task_type}, 分组延迟: {delay_group_sec}秒, 操作延迟: {delay_like_sec}秒")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def ensure_scheduler_running():
    """确保调度器运行"""
    try:
        import sys
        sys.path.append('.')
        from app.services.optimized_scheduler import optimized_scheduler
        
        log_with_time("🔍 检查调度器状态...")
        log_with_time(f"调度器运行状态: {optimized_scheduler.is_running}")
        
        if not optimized_scheduler.is_running:
            log_with_time("🚀 启动调度器...")
            await optimized_scheduler.start()
            log_with_time(f"启动后状态: {optimized_scheduler.is_running}")
            
            # 等待调度器稳定
            await asyncio.sleep(3)
        
        log_with_time(f"设备队列数量: {len(optimized_scheduler.device_queues)}")
        log_with_time(f"设备在线缓存: {optimized_scheduler.device_online_cache}")
        
        return optimized_scheduler.is_running
    except Exception as e:
        log_with_time(f"❌ 调度器检查失败: {e}")
        return False

async def test_complete_system():
    """完整系统测试"""
    log_with_time("=== 完整系统测试 ===")
    
    # 1. 确保调度器运行
    if not await ensure_scheduler_running():
        log_with_time("❌ 调度器启动失败，测试终止")
        return
    
    # 创建测试设备
    device = TestDevice("ceshi212")  # 使用分组5的设备
    
    try:
        # 2. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(3)
        
        # 3. 创建多个不同类型的任务
        delay_group_sec = 8  # 8秒分组延迟
        delay_like_sec = 3   # 3秒操作延迟
        
        log_with_time(f"🚀 创建多个任务，分组延迟: {delay_group_sec}秒，操作延迟: {delay_like_sec}秒")
        
        test_start_time = time.time()
        
        tasks = []
        task_types = ["like", "sign", "page_sign"]
        
        for i, task_type in enumerate(task_types):
            task_name = f"完整测试{i+1}_{task_type}"
            task = create_complete_task(task_name, task_type, 5, delay_group_sec, delay_like_sec)
            if task:
                tasks.append(task)
            time.sleep(1)  # 任务创建间隔1秒
        
        if len(tasks) < 3:
            log_with_time("❌ 任务创建失败，测试终止")
            return
        
        # 4. 等待所有任务完成
        expected_time = len(tasks) * (2 + delay_group_sec)  # 每个任务2秒执行 + 分组延迟
        log_with_time(f"⏳ 等待所有任务完成（预计需要约{expected_time}秒）...")
        await asyncio.sleep(expected_time + 10)  # 额外等待10秒
        
        # 5. 分析结果
        log_with_time("📊 分析测试结果...")
        
        if len(device.task_times) >= 2:
            log_with_time("任务接收时间间隔:")
            total_intervals = []
            
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                total_intervals.append(interval)
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                task1_type = device.received_tasks[i].get('type')
                task2_type = device.received_tasks[i + 1].get('type')
                
                log_with_time(f"   任务{task1_id}({task1_type}) → 任务{task2_id}({task2_type}): {interval:.1f}秒")
                
                # 检查是否符合预期（任务完成2秒 + 分组延迟8秒 = 约10秒间隔）
                expected_min = delay_group_sec + 1.5  # 8 + 1.5 = 9.5秒
                expected_max = delay_group_sec + 3.0  # 8 + 3.0 = 11秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
            
            # 总体分析
            if total_intervals:
                avg_interval = sum(total_intervals) / len(total_intervals)
                log_with_time(f"📊 平均间隔: {avg_interval:.1f}秒")
                
                if delay_group_sec + 1.5 <= avg_interval <= delay_group_sec + 3.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                else:
                    log_with_time("⚠️ 分组延迟功能可能有问题")
        else:
            log_with_time("❌ 收到的任务数量不足，无法分析间隔")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: {len(tasks)}")
        
        # 显示所有任务的详细信息
        if device.received_tasks:
            log_with_time("📋 任务详细信息:")
            for i, (task_data, receive_time) in enumerate(zip(device.received_tasks, device.task_times)):
                relative_time = receive_time - test_start_time
                task_id = task_data.get('task_id')
                task_type = task_data.get('type')
                log_with_time(f"   任务{task_id}({task_type}): 第{relative_time:.1f}秒接收")
        
        if len(device.received_tasks) == len(tasks):
            log_with_time("✅ 所有任务都被正确接收和处理")
        else:
            log_with_time(f"⚠️ 任务处理不完整: 预期{len(tasks)}个，实际{len(device.received_tasks)}个")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 完整系统测试")
    print("💡 包含所有参数的完整测试，验证调度器稳定性和分组延迟功能")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 50)
    print("🚀 开始测试")
    print("=" * 50)
    
    # 完整系统测试
    await test_complete_system()
    
    print("\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 测试要点:")
    print("   1. 调度器稳定性：调度器应该持续运行")
    print("   2. 延迟单位转换：前端秒 → 后端毫秒 → 执行时转回秒")
    print("   3. 分组延迟效果：任务间隔 = 执行时间 + 分组延迟")
    print("   4. 多任务类型：like, sign, page_sign 等不同类型")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
