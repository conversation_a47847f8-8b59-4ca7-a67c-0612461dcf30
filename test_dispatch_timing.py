#!/usr/bin/env python3
"""
详细测试分发时机
观察分发延迟的具体行为
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_precise_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_task_with_precise_timing(task_name, device_id, delay_group_sec):
    """创建任务并精确记录时间"""
    log_with_precise_time(f"🚀 开始创建任务: {task_name}")
    
    start_time = time.time()
    
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test_name": task_name},
        "target_scope": "single",
        "target_id": device_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": 100  # 0.1秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=15)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            duration = (end_time - start_time) * 1000
            log_with_precise_time(f"✅ 任务 {task_name} 创建完成，ID: {task.get('id')}, 耗时: {duration:.1f}ms")
            return task, duration
        else:
            log_with_precise_time(f"❌ 任务 {task_name} 创建失败: {response.status_code}")
            return None, (end_time - start_time) * 1000
    except Exception as e:
        end_time = time.time()
        log_with_precise_time(f"❌ 任务 {task_name} 创建异常: {e}")
        return None, (end_time - start_time) * 1000

def test_precise_dispatch_timing():
    """精确测试分发时机"""
    log_with_precise_time("=== 精确分发时机测试 ===")
    
    # 获取第一个设备
    devices_response = requests.get(f"{API_BASE_URL}/devices")
    if devices_response.status_code != 200:
        log_with_precise_time("❌ 无法获取设备列表")
        return
    
    devices = devices_response.json()
    if not devices:
        log_with_precise_time("❌ 没有可用设备")
        return
    
    device = devices[0]
    device_id = device.get('id')
    device_number = device.get('device_number')
    
    log_with_precise_time(f"📱 使用设备: {device_number} (ID: {device_id})")
    log_with_precise_time(f"⏰ 设置分组延迟: 5秒")
    
    # 连续创建3个任务，观察分发延迟
    tasks = []
    durations = []
    
    log_with_precise_time("🎬 开始连续创建任务...")
    
    for i in range(3):
        task_name = f"精确测试任务{i+1}"
        task, duration = create_task_with_precise_timing(task_name, device_id, 5.0)
        tasks.append(task)
        durations.append(duration)
        
        # 稍微间隔，避免完全同时
        time.sleep(0.05)
    
    log_with_precise_time("📊 分析结果:")
    for i, duration in enumerate(durations):
        expected_delay = i * 5000  # 预期的累积延迟（毫秒）
        log_with_precise_time(f"   任务{i+1}: {duration:.1f}ms (预期延迟: {expected_delay}ms)")
    
    # 分析延迟模式
    if len(durations) >= 3:
        delay1 = durations[1] - durations[0]  # 第2个任务相对第1个的额外延迟
        delay2 = durations[2] - durations[1]  # 第3个任务相对第2个的额外延迟
        
        log_with_precise_time("🔍 延迟分析:")
        log_with_precise_time(f"   任务1->2 额外延迟: {delay1:.1f}ms")
        log_with_precise_time(f"   任务2->3 额外延迟: {delay2:.1f}ms")
        
        if delay1 > 4000 and delay2 > 4000:  # 接近5秒延迟
            log_with_precise_time("✅ 分发延迟正常工作")
        else:
            log_with_precise_time("⚠️ 分发延迟可能未按预期工作")

def test_parallel_devices():
    """测试不同设备的并行分发"""
    log_with_precise_time("=== 并行设备分发测试 ===")
    
    # 获取设备列表
    devices_response = requests.get(f"{API_BASE_URL}/devices")
    devices = devices_response.json()
    
    if len(devices) < 2:
        log_with_precise_time("⚠️ 设备数量不足，跳过并行测试")
        return
    
    device1 = devices[0]
    device2 = devices[1]
    
    log_with_precise_time(f"📱 设备1: {device1.get('device_number')} (ID: {device1.get('id')})")
    log_with_precise_time(f"📱 设备2: {device2.get('device_number')} (ID: {device2.get('id')})")
    
    # 同时向两个设备发送任务
    log_with_precise_time("🎬 同时创建任务到不同设备...")
    
    start_time = time.time()
    
    # 设备1任务
    task1, duration1 = create_task_with_precise_timing("并行测试设备1", device1.get('id'), 3.0)
    
    # 立即创建设备2任务
    task2, duration2 = create_task_with_precise_timing("并行测试设备2", device2.get('id'), 3.0)
    
    total_time = time.time() - start_time
    
    log_with_precise_time("📊 并行分发结果:")
    log_with_precise_time(f"   设备1任务: {duration1:.1f}ms")
    log_with_precise_time(f"   设备2任务: {duration2:.1f}ms")
    log_with_precise_time(f"   总耗时: {total_time*1000:.1f}ms")
    
    # 分析并行效果
    if abs(duration1 - duration2) < 500:  # 时间差小于500ms
        log_with_precise_time("✅ 不同设备任务并行分发正常")
    else:
        log_with_precise_time("⚠️ 不同设备任务可能存在串行等待")

def main():
    """主函数"""
    print("=" * 60)
    print("🔬 精确分发时机测试")
    print("=" * 60)
    
    # 测试API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_precise_time("✅ API连接正常")
        else:
            log_with_precise_time("❌ API连接失败")
            return
    except:
        log_with_precise_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始精确时机测试")
    print("=" * 40)
    
    # 1. 精确分发时机测试
    test_precise_dispatch_timing()
    
    print("\n" + "-" * 40)
    
    # 2. 并行设备测试
    test_parallel_devices()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 观察要点:")
    print("   1. 同设备任务应该有明显的延迟间隔")
    print("   2. 不同设备任务应该能并行处理")
    print("   3. 分发延迟在API调用阶段体现")
    print("=" * 60)

if __name__ == "__main__":
    main()
