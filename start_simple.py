#!/usr/bin/env python3
"""
简化启动脚本
用于测试稳定版调度器
"""

import asyncio
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

async def test_stable_scheduler():
    """测试稳定版调度器"""
    log_with_time("=== 稳定版调度器简化测试 ===")
    
    try:
        # 导入稳定版调度器
        from app.services.stable_scheduler import stable_scheduler
        
        log_with_time("✅ 稳定版调度器导入成功")
        
        # 启动调度器
        log_with_time("🚀 启动稳定版调度器...")
        await stable_scheduler.start()
        
        if stable_scheduler.is_running:
            log_with_time("✅ 稳定版调度器启动成功")
            
            # 运行一段时间
            log_with_time("⏳ 运行测试10秒...")
            await asyncio.sleep(10)
            
            # 检查状态
            log_with_time(f"📊 调度器状态:")
            log_with_time(f"   运行状态: {stable_scheduler.is_running}")
            log_with_time(f"   错误次数: {stable_scheduler.error_count}")
            log_with_time(f"   设备队列数: {len(stable_scheduler.device_queues)}")
            
            log_with_time("✅ 稳定版调度器测试成功！")
        else:
            log_with_time("❌ 稳定版调度器启动失败")
        
        # 停止调度器
        await stable_scheduler.stop()
        log_with_time("🛑 稳定版调度器已停止")
        
    except Exception as e:
        log_with_time(f"❌ 测试失败: {e}")
        import traceback
        log_with_time(f"详细错误: {traceback.format_exc()}")

async def test_fastapi_startup():
    """测试FastAPI启动"""
    log_with_time("=== FastAPI启动测试 ===")
    
    try:
        # 模拟FastAPI启动过程
        from fastapi import FastAPI
        
        app = FastAPI(title="Test Server")
        log_with_time("✅ FastAPI创建成功")
        
        # 导入稳定版调度器
        from app.services.stable_scheduler import stable_scheduler
        log_with_time("✅ 稳定版调度器导入成功")
        
        # 启动调度器
        await stable_scheduler.start()
        
        if stable_scheduler.is_running:
            log_with_time("✅ 调度器在FastAPI环境中启动成功")
        else:
            log_with_time("❌ 调度器在FastAPI环境中启动失败")
        
        await stable_scheduler.stop()
        log_with_time("✅ FastAPI启动测试完成")
        
    except Exception as e:
        log_with_time(f"❌ FastAPI启动测试失败: {e}")
        import traceback
        log_with_time(f"详细错误: {traceback.format_exc()}")

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 稳定版调度器启动测试")
    print("💡 验证调度器能否正常启动和运行")
    print("=" * 60)
    
    # 测试1：独立调度器测试
    await test_stable_scheduler()
    
    print("\n" + "-" * 40)
    
    # 测试2：FastAPI环境测试
    await test_fastapi_startup()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果看到 '✅ 稳定版调度器启动成功'，说明调度器工作正常")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
