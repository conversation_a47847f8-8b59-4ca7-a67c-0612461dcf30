# WebSocket心跳检测优化方案

## 🔍 当前问题分析

### 客户端心跳机制
```javascript
ws.onOpen(function (ws1, code, msg) {
    logi("onOpen code " + code + "  msg:" + msg);
    //每3000 发送一次文本心跳数据
    ws.startHeartBeat(function () {
        return null;
    }, function () {
        return new Date().toISOString();
    }, 3000, true);
});
```

### 原服务端问题
1. **频率不匹配**：客户端3秒发送，服务端30秒检查
2. **双向心跳冲突**：服务端主动发送心跳，客户端也发送心跳
3. **数据库操作频繁**：每次心跳都要查询和更新数据库
4. **缺乏超时检测**：没有检测客户端心跳超时
5. **异常处理粗糙**：心跳失败直接断开连接

## 🛠️ 优化方案

### 1. **服务端优化**

#### A. 被动心跳检测
```python
# 优化后的心跳检测器
async def heartbeat_checker(self):
    """被动检测客户端心跳，不主动发送"""
    while True:
        await asyncio.sleep(5)  # 每5秒检查一次
        current_time = datetime.utcnow()
        
        for device_number in list(self.active_connections.keys()):
            last_heartbeat = self.device_last_heartbeat.get(device_number)
            
            if last_heartbeat:
                time_diff = (current_time - last_heartbeat).total_seconds()
                if time_diff > 15:  # 15秒超时
                    self.disconnect(device_number)
```

#### B. 轻量级心跳处理
```python
async def _handle_heartbeat(self, device_number: str, data: dict, websocket: WebSocket):
    """轻量级心跳处理，减少数据库操作"""
    # 只更新内存中的时间戳
    self.device_last_heartbeat[device_number] = datetime.utcnow()
    
    # 快速响应
    await websocket.send_json({
        "type": "heartbeat_ack",
        "timestamp": datetime.utcnow().isoformat(),
        "device_number": device_number
    })
```

#### C. 批量数据库更新
```python
async def _batch_update_heartbeat_status(self):
    """批量更新心跳状态，减少数据库压力"""
    # 每30秒批量更新一次数据库
    # 使用原生SQL提高性能
```

### 2. **客户端优化建议**

#### A. 优化心跳消息格式
```javascript
// 建议的心跳消息格式
function sendHeartbeat() {
    const heartbeatMsg = {
        type: "heartbeat",
        timestamp: new Date().toISOString(),
        device_number: deviceNumber,
        // 可选：添加设备状态信息
        status: {
            cpu_usage: getCpuUsage(),
            memory_usage: getMemoryUsage(),
            task_count: getCurrentTaskCount()
        }
    };
    
    ws.send(JSON.stringify(heartbeatMsg));
}
```

#### B. 智能心跳频率
```javascript
// 根据网络状况调整心跳频率
let heartbeatInterval = 3000; // 默认3秒
let missedHeartbeats = 0;

ws.onMessage = function(message) {
    const data = JSON.parse(message);
    
    if (data.type === "heartbeat_ack") {
        missedHeartbeats = 0;
        
        // 根据延迟调整心跳频率
        if (data.latency_ms > 1000) {
            heartbeatInterval = 5000; // 网络慢时降低频率
        } else {
            heartbeatInterval = 3000; // 网络好时恢复正常
        }
    }
};

// 检测心跳超时
setInterval(() => {
    missedHeartbeats++;
    if (missedHeartbeats > 3) {
        // 重连逻辑
        reconnectWebSocket();
    }
}, heartbeatInterval);
```

#### C. 断线重连机制
```javascript
function reconnectWebSocket() {
    console.log("检测到连接断开，开始重连...");
    
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    
    function attemptReconnect() {
        if (reconnectAttempts >= maxReconnectAttempts) {
            console.log("重连失败，达到最大重试次数");
            return;
        }
        
        reconnectAttempts++;
        console.log(`重连尝试 ${reconnectAttempts}/${maxReconnectAttempts}`);
        
        // 指数退避重连
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
        
        setTimeout(() => {
            try {
                initWebSocket();
            } catch (error) {
                console.log("重连失败:", error);
                attemptReconnect();
            }
        }, delay);
    }
    
    attemptReconnect();
}
```

## 📊 优化效果对比

### 优化前
- **心跳频率**：客户端3秒，服务端30秒检查
- **数据库操作**：每次心跳都查询+更新
- **超时检测**：无
- **网络开销**：双向心跳，冗余数据
- **连接稳定性**：差，容易误判断开

### 优化后
- **心跳频率**：客户端3秒，服务端5秒检查
- **数据库操作**：批量更新，减少90%操作
- **超时检测**：15秒超时，精确检测
- **网络开销**：单向心跳，最小化数据
- **连接稳定性**：好，智能重连机制

## 🚀 部署建议

### 1. **服务端配置**
```python
# 在ConnectionManager初始化中设置
self.heartbeat_timeout = 15  # 心跳超时时间（秒）
self.heartbeat_check_interval = 5  # 心跳检查间隔（秒）
self.batch_update_interval = 30  # 批量更新间隔（秒）
```

### 2. **客户端配置**
```javascript
// 心跳配置
const HEARTBEAT_INTERVAL = 3000;  // 3秒
const HEARTBEAT_TIMEOUT = 15000;  // 15秒超时
const MAX_RECONNECT_ATTEMPTS = 5;
```

### 3. **监控指标**
- 心跳延迟分布
- 连接断开频率
- 重连成功率
- 数据库心跳更新频率

## 🔧 实施步骤

### 阶段1：服务端优化（立即实施）
1. ✅ 实现被动心跳检测
2. ✅ 优化心跳处理逻辑
3. ✅ 添加批量数据库更新
4. ✅ 改进断开连接处理

### 阶段2：客户端优化（建议实施）
1. 🔄 优化心跳消息格式
2. 🔄 实现智能心跳频率
3. 🔄 添加断线重连机制
4. 🔄 增加网络状态监控

### 阶段3：监控和调优（持续优化）
1. 📊 添加心跳监控指标
2. 📊 分析连接稳定性
3. 📊 优化超时参数
4. 📊 性能调优

## 💡 最佳实践

1. **心跳频率**：客户端3-5秒，服务端检查频率为心跳频率的1.5-2倍
2. **超时设置**：超时时间为心跳间隔的3-5倍
3. **重连策略**：指数退避，最大延迟不超过30秒
4. **数据库优化**：批量更新，减少单次操作
5. **日志记录**：记录关键事件，便于问题排查
