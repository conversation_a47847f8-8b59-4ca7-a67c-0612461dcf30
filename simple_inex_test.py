#!/usr/bin/env python3
"""
简单的inex任务测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app import crud, models
from app.schemas.task import TaskCreate

def test_simple_inex():
    """简单测试inex任务"""
    print("简单测试inex任务...")
    
    try:
        # 创建TaskCreate对象
        task_data = TaskCreate(
            task_type="inex",
            parameters={"user_id": "test123", "count": 1},
            target_scope="single",
            target_id=1,
            delay_group=1000,
            delay_like=500
        )
        
        print(f"✅ TaskCreate对象创建成功: {task_data}")
        
        # 使用crud创建任务
        db = next(get_db())
        task = crud.create_task(db, task_data)
        
        print(f"✅ 任务创建成功:")
        print(f"   ID: {task.id}")
        print(f"   类型: {task.task_type}")
        print(f"   参数: {task.parameters}")
        print(f"   状态: {task.status}")
        
        # 测试获取任务
        retrieved_task = crud.get_task(db, task.id)
        print(f"✅ 任务获取成功: {retrieved_task.task_type}")
        
        # 测试获取所有任务
        all_tasks = crud.get_tasks(db)
        inex_tasks = [t for t in all_tasks if t.task_type == "inex"]
        print(f"✅ 找到 {len(inex_tasks)} 个inex任务")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 简单inex任务测试...")
    print("="*40)
    
    if test_simple_inex():
        print("\n✅ 所有测试通过!")
        print("inex任务类型在后端工作正常")
        print("\nAPI问题可能在于:")
        print("1. 服务器需要重启")
        print("2. 路由配置问题")
        print("3. 异步处理问题")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
