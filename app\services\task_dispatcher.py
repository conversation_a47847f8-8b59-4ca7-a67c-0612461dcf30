"""
优化版任务分发服务
负责任务的创建、验证和初始分发
"""
import logging
from typing import List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from app.db import get_db
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.models.group import Group, DeviceGroup
from app.schemas.task import TaskCreate
from app.utils.time_utils import get_beijing_now_naive

logger = logging.getLogger(__name__)

class TaskDispatchService:
    """任务分发服务"""
    
    @staticmethod
    async def create_and_dispatch_task(task_data: TaskCreate) -> Task:
        """创建并分发任务"""
        db = next(get_db())
        try:
            # 1. 验证任务数据
            validation_result = await TaskDispatchService._validate_task_data(task_data, db)
            if not validation_result['valid']:
                raise ValueError(f"任务验证失败: {validation_result['error']}")
            
            # 2. 创建主任务记录
            task = TaskDispatchService._create_main_task(task_data, db)
            
            # 3. 获取目标设备列表
            target_devices = await TaskDispatchService._get_target_devices(task_data, db)
            
            # 4. 创建任务队列记录
            await TaskDispatchService._create_task_queues(task, target_devices, db)

            # 5. 更新任务状态为pending（等待调度器处理）
            task.status = 'pending'
            db.commit()

            # 6. 通知优化版调度器处理新任务
            await TaskDispatchService._notify_scheduler(task, target_devices)
            
            logger.info(f"✅ 任务创建成功: ID={task.id}, 类型={task.task_type}, "
                       f"范围={task.target_scope}, 目标设备数={len(target_devices)}")
            
            return task
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 创建任务失败: {e}")
            raise
        finally:
            db.close()
    
    @staticmethod
    async def _validate_task_data(task_data: TaskCreate, db: Session) -> Dict[str, Any]:
        """验证任务数据"""
        try:
            # 验证任务类型
            valid_task_types = ['sign', 'like', 'page_sign', 'inex']
            if task_data.task_type not in valid_task_types:
                return {'valid': False, 'error': f'无效的任务类型: {task_data.task_type}'}
            
            # 验证目标范围
            valid_scopes = ['single', 'group', 'all']
            if task_data.target_scope not in valid_scopes:
                return {'valid': False, 'error': f'无效的目标范围: {task_data.target_scope}'}
            
            # 验证目标ID
            if task_data.target_scope == 'single':
                device = db.query(Device).filter(Device.id == task_data.target_id).first()
                if not device:
                    return {'valid': False, 'error': f'设备不存在: {task_data.target_id}'}
            elif task_data.target_scope == 'group':
                group = db.query(Group).filter(Group.id == task_data.target_id).first()
                if not group:
                    return {'valid': False, 'error': f'分组不存在: {task_data.target_id}'}
                
                # 检查分组是否有设备
                device_count = db.query(Device).join(DeviceGroup).filter(
                    DeviceGroup.group_id == task_data.target_id
                ).count()
                if device_count == 0:
                    return {'valid': False, 'error': f'分组 {task_data.target_id} 中没有设备'}
            
            # 验证任务参数
            if not task_data.parameters:
                return {'valid': False, 'error': '任务参数不能为空'}
            
            # 根据任务类型验证特定参数
            param_validation = TaskDispatchService._validate_task_parameters(
                task_data.task_type, task_data.parameters
            )
            if not param_validation['valid']:
                return param_validation
            
            return {'valid': True}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证过程出错: {str(e)}'}
    
    @staticmethod
    def _validate_task_parameters(task_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证任务参数"""
        try:
            if task_type == 'like':
                # 点赞任务只需要like_id
                if 'like_id' not in parameters:
                    return {'valid': False, 'error': '点赞任务缺少like_id参数'}
                if not parameters['like_id']:
                    return {'valid': False, 'error': 'like_id不能为空'}
                    
            elif task_type == 'page_sign':
                # 超话签到需要blogger_id和like_id
                required_params = ['blogger_id', 'like_id']
                for param in required_params:
                    if param not in parameters:
                        return {'valid': False, 'error': f'超话签到任务缺少{param}参数'}
                    if not parameters[param]:
                        return {'valid': False, 'error': f'{param}不能为空'}
                        
            elif task_type == 'inex':
                # 主页签到需要user_id
                if 'user_id' not in parameters:
                    return {'valid': False, 'error': '主页签到任务缺少user_id参数'}
                if not parameters['user_id']:
                    return {'valid': False, 'error': 'user_id不能为空'}
                    
            elif task_type == 'sign':
                # 普通签到任务
                pass  # 普通签到可能不需要特殊参数
            
            return {'valid': True}
            
        except Exception as e:
            return {'valid': False, 'error': f'参数验证出错: {str(e)}'}
    
    @staticmethod
    def _create_main_task(task_data: TaskCreate, db: Session) -> Task:
        """创建主任务记录"""
        task = Task(
            task_type=task_data.task_type,
            parameters=task_data.parameters,
            target_scope=task_data.target_scope,
            target_id=task_data.target_id,
            delay_group=task_data.delay_group,
            delay_like=task_data.delay_like,
            status="pending",
            create_time=get_beijing_now_naive()
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        
        logger.info(f"📝 主任务记录已创建: ID={task.id}")
        return task
    
    @staticmethod
    async def _get_target_devices(task_data: TaskCreate, db: Session) -> List[Device]:
        """获取目标设备列表"""
        devices = []
        
        if task_data.target_scope == 'single':
            # 单设备任务
            device = db.query(Device).filter(Device.id == task_data.target_id).first()
            if device:
                devices.append(device)
                
        elif task_data.target_scope == 'group':
            # 分组任务 - 获取分组内所有设备（包括离线设备）
            devices = db.query(Device).join(DeviceGroup).filter(
                DeviceGroup.group_id == task_data.target_id
            ).all()
            
        elif task_data.target_scope == 'all':
            # 全部设备任务
            devices = db.query(Device).all()
        
        logger.info(f"🎯 目标设备: {len(devices)} 个设备")
        for device in devices:
            logger.debug(f"  - 设备 {device.device_number} (ID:{device.id}, 状态:{device.online_status})")
        
        return devices
    
    @staticmethod
    async def _create_task_queues(task: Task, devices: List[Device], db: Session):
        """为所有目标设备创建任务队列记录"""
        created_count = 0
        
        for device in devices:
            try:
                # 检查是否已存在
                existing = db.query(TaskQueue).filter(
                    TaskQueue.task_id == task.id,
                    TaskQueue.device_id == device.id
                ).first()
                
                if existing:
                    logger.debug(f"📋 任务队列已存在: 任务{task.id}, 设备{device.id}")
                    continue
                
                # 创建新的任务队列记录
                task_queue = TaskQueue(
                    task_id=task.id,
                    device_id=device.id,
                    status='pending',
                    create_time=get_beijing_now_naive(),
                    group_id=task.target_id if task.target_scope == 'group' else None
                )
                db.add(task_queue)
                created_count += 1
                
                logger.debug(f"📋 创建任务队列: 任务{task.id}, 设备{device.device_number}")
                
            except Exception as e:
                logger.error(f"❌ 创建任务队列失败: 任务{task.id}, 设备{device.id}, 错误:{e}")
                raise
        
        db.commit()
        logger.info(f"✅ 任务队列创建完成: {created_count} 个队列记录")

    @staticmethod
    async def _notify_scheduler(task: Task, devices: List[Device]):
        """通知优化版调度器处理新任务"""
        try:
            logger.info(f"🔔 开始通知调度器处理任务 {task.id}")

            # 动态导入避免循环导入
            import importlib
            scheduler_module = importlib.import_module('app.services.optimized_scheduler')
            optimized_scheduler = scheduler_module.optimized_scheduler

            logger.info(f"📋 调度器状态: 运行={optimized_scheduler.is_running}, 设备队列数={len(optimized_scheduler.device_queues)}")

            # 不直接加入队列，让调度器的 _dispatch_new_tasks() 方法处理分发延迟
            # 这样可以确保分发延迟控制生效
            logger.info(f"✅ 任务 {task.id} 已通知调度器，等待调度器处理分发延迟（目标设备: {[d.device_number for d in devices]}）")

        except Exception as e:
            logger.error(f"❌ 通知调度器失败: {e}", exc_info=True)

    @staticmethod
    async def get_task_distribution_info(task_id: int) -> Dict[str, Any]:
        """获取任务分发信息"""
        db = next(get_db())
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return {'error': '任务不存在'}
            
            # 获取任务队列信息
            queues = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).all()
            
            # 统计状态
            status_counts = {
                'pending': 0,
                'running': 0,
                'done': 0,
                'failed': 0
            }
            
            device_details = []
            for queue in queues:
                status_counts[queue.status] += 1
                
                device = db.query(Device).filter(Device.id == queue.device_id).first()
                device_details.append({
                    'device_id': queue.device_id,
                    'device_number': device.device_number if device else 'Unknown',
                    'status': queue.status,
                    'create_time': queue.create_time,
                    'dispatch_time': queue.dispatch_time,
                    'finish_time': queue.finish_time,
                    'result_summary': queue.result_summary
                })
            
            return {
                'task_id': task.id,
                'task_type': task.task_type,
                'target_scope': task.target_scope,
                'target_id': task.target_id,
                'main_status': task.status,
                'total_devices': len(queues),
                'status_counts': status_counts,
                'device_details': device_details,
                'create_time': task.create_time
            }
            
        finally:
            db.close()
    
    @staticmethod
    async def cancel_task(task_id: int) -> bool:
        """取消任务"""
        db = next(get_db())
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return False
            
            # 更新主任务状态
            task.status = 'cancelled'
            
            # 取消所有pending状态的任务队列
            pending_queues = db.query(TaskQueue).filter(
                TaskQueue.task_id == task_id,
                TaskQueue.status == 'pending'
            ).all()
            
            for queue in pending_queues:
                queue.status = 'cancelled'
                queue.finish_time = get_beijing_now_naive()
                queue.result_summary = '任务已取消'
            
            db.commit()
            logger.info(f"✅ 任务 {task_id} 已取消，影响 {len(pending_queues)} 个队列项")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 取消任务失败: {e}")
            return False
        finally:
            db.close()

# 创建全局实例
task_dispatch_service = TaskDispatchService()
