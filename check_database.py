#!/usr/bin/env python3
"""
检查数据库中的设备分组情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.db import get_db, engine
from app.models.device import Device
from app.models.group import Group, DeviceGroup

def check_device_groups():
    """检查设备分组情况"""
    print("🔍 检查数据库中的设备分组情况...")
    
    # 创建数据库会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. 检查设备表
        print("\n📱 设备表 (devices):")
        devices = db.query(Device).all()
        
        for device in devices[:5]:  # 只显示前5个
            print(f"   设备ID: {device.id}")
            print(f"   设备编号: {device.device_number}")
            print(f"   分组ID: {device.group_id}")
            print(f"   在线状态: {device.online_status}")
            print()
            
        # 2. 检查分组表
        print("👥 分组表 (groups):")
        groups = db.query(Group).all()
        
        for group in groups:
            print(f"   分组ID: {group.id}")
            print(f"   分组名称: {group.group_name}")
            print(f"   描述: {group.description}")
            
            # 统计该分组的设备数量（通过Device.group_id）
            device_count = db.query(Device).filter(Device.group_id == group.id).count()
            print(f"   设备数量(通过group_id): {device_count}")
            
            # 统计该分组的设备数量（通过DeviceGroup表）
            device_group_count = db.query(DeviceGroup).filter(DeviceGroup.group_id == group.id).count()
            print(f"   设备数量(通过DeviceGroup): {device_group_count}")
            print()
            
        # 3. 检查设备分组关联表
        print("🔗 设备分组关联表 (device_groups):")
        device_groups = db.query(DeviceGroup).all()
        
        for dg in device_groups:
            print(f"   关联ID: {dg.id}")
            print(f"   设备ID: {dg.device_id}")
            print(f"   分组ID: {dg.group_id}")
            print(f"   创建时间: {dg.create_time}")
            print()
            
        # 4. 检查数据一致性
        print("🔍 数据一致性检查:")
        
        # 检查有group_id但没有DeviceGroup记录的设备
        devices_with_group_id = db.query(Device).filter(Device.group_id.isnot(None)).all()
        print(f"   有group_id的设备数量: {len(devices_with_group_id)}")
        
        for device in devices_with_group_id:
            device_group_exists = db.query(DeviceGroup).filter(
                DeviceGroup.device_id == device.id,
                DeviceGroup.group_id == device.group_id
            ).first()
            
            if not device_group_exists:
                print(f"   ⚠️ 设备 {device.device_number} (ID: {device.id}) 有group_id={device.group_id}，但DeviceGroup表中没有对应记录")
            else:
                print(f"   ✅ 设备 {device.device_number} (ID: {device.id}) 数据一致")
                
        # 检查有DeviceGroup记录但设备group_id为空的情况
        device_groups = db.query(DeviceGroup).all()
        print(f"\n   DeviceGroup记录数量: {len(device_groups)}")
        
        for dg in device_groups:
            device = db.query(Device).filter(Device.id == dg.device_id).first()
            if device and device.group_id != dg.group_id:
                print(f"   ⚠️ DeviceGroup记录 (设备ID: {dg.device_id}, 分组ID: {dg.group_id})，但设备的group_id={device.group_id}")
            elif device:
                print(f"   ✅ DeviceGroup记录 (设备ID: {dg.device_id}, 分组ID: {dg.group_id}) 数据一致")
                
    except Exception as e:
        print(f"❌ 数据库检查异常: {e}")
        
    finally:
        db.close()

def test_add_device_to_group():
    """测试添加设备到分组"""
    print("\n🔧 测试添加设备到分组...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 选择第一个设备和第一个分组
        device = db.query(Device).first()
        group = db.query(Group).first()
        
        if not device or not group:
            print("❌ 没有设备或分组数据")
            return
            
        print(f"   测试设备: {device.device_number} (ID: {device.id})")
        print(f"   测试分组: {group.group_name} (ID: {group.id})")
        print(f"   设备当前group_id: {device.group_id}")
        
        # 手动执行分组分配逻辑
        from app.services.group import add_device_to_group
        from app.schemas.group import GroupDevice
        
        group_device = GroupDevice(device_id=device.id, group_id=group.id)
        result = add_device_to_group(db, group_device)
        
        if result:
            print("✅ 添加设备到分组成功")
            
            # 重新查询设备
            updated_device = db.query(Device).filter(Device.id == device.id).first()
            print(f"   设备更新后的group_id: {updated_device.group_id}")
            
            # 检查DeviceGroup表
            device_group = db.query(DeviceGroup).filter(
                DeviceGroup.device_id == device.id,
                DeviceGroup.group_id == group.id
            ).first()
            
            if device_group:
                print("✅ DeviceGroup表中有对应记录")
            else:
                print("❌ DeviceGroup表中没有对应记录")
                
        else:
            print("❌ 添加设备到分组失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

def main():
    """主函数"""
    print("🔍 数据库设备分组检查...")
    print("="*50)
    
    check_device_groups()
    test_add_device_to_group()

if __name__ == "__main__":
    main()
