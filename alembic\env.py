from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# 导入模型Base和所有模型
from app.db import Base
from app.models.device import Device
from app.models.group import DeviceGroup
from app.models.task import Task, TaskQueue
from app.models.group_task_status import GroupTaskStatus

# 这是Alembic Config对象，提供对.ini文件中值的访问
config = context.config

# 设置日志记录
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置target_metadata
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """在'offline'模式下运行迁移"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """在'online'模式下运行迁移"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
