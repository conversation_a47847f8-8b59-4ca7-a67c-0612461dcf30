from fastapi import WebSocket, APIRouter
from typing import Dict
import asyncio
import json
from datetime import datetime
from app.db import get_db
from app.models.device import Device
from app.models.device_status import DeviceStatus
from app.models.task import Task, TaskQueue
from app.models.group_task_status import GroupTaskStatus
from app.utils.time_utils import get_beijing_now_naive, beijing_timestamp

router = APIRouter()

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.db_query_queue = asyncio.Queue()
        self.db_insert_queue = asyncio.Queue()
        self.device_task_queues: Dict[str, asyncio.Queue] = {}  # 设备任务队列
        self.device_current_tasks: Dict[str, asyncio.Future] = {}  # 设备当前任务Future

        # 心跳管理
        self.device_last_heartbeat: Dict[str, datetime] = {}  # 设备最后心跳时间
        self.heartbeat_timeout = 15  # 心跳超时时间（秒）
        self.heartbeat_check_interval = 5  # 心跳检查间隔（秒）

        # 🔥 修复：延迟启动后台处理线程，避免导入时的事件循环问题
        self._background_tasks_started = False

    async def _ensure_background_tasks_started(self):
        """确保后台任务已启动"""
        if not self._background_tasks_started:
            asyncio.create_task(self._process_db_queries())
            asyncio.create_task(self._process_db_inserts())
            self._background_tasks_started = True

    async def _process_db_queries(self):
        """处理数据库查询的后台线程"""
        while True:
            query_data = await self.db_query_queue.get()
            try:
                db = next(get_db())
                if query_data['type'] == 'check_device':
                    device = db.query(Device).filter(
                        Device.device_number == query_data['device_number']
                    ).first()
                    query_data['future'].set_result(device)
            except Exception as e:
                query_data['future'].set_exception(e)
            finally:
                self.db_query_queue.task_done()

    async def _process_db_inserts(self):
        """处理数据库插入的后台线程"""
        while True:
            insert_data = await self.db_insert_queue.get()
            try:
                db = next(get_db())
                if insert_data['type'] == 'create_device':
                    device = Device(**insert_data['data'])
                    db.add(device)
                    db.commit()
                    db.refresh(device)
                    insert_data['future'].set_result(device)
                elif insert_data['type'] == 'update_status':
                    device_status = db.query(DeviceStatus).filter(
                        DeviceStatus.device_id == insert_data['device_id']
                    ).first()
                    if not device_status:
                        device_status = DeviceStatus(device_id=insert_data['device_id'])
                        db.add(device_status)
                    device_status.is_online = True
                    device_status.last_heartbeat = get_beijing_now_naive()
                    db.commit()
                    insert_data['future'].set_result(True)
            except Exception as e:
                insert_data['future'].set_exception(e)
            finally:
                self.db_insert_queue.task_done()

    async def connect(self, device_number: str, websocket: WebSocket, client_ip: str = None):
        try:
            # 🔥 修复：确保后台任务已启动
            await self._ensure_background_tasks_started()

            print(f"设备连接中: {device_number} (IP: {client_ip})")
            await websocket.accept()
            self.active_connections[device_number] = websocket

            # 初始化心跳时间
            self.device_last_heartbeat[device_number] = get_beijing_now_naive()
            print(f"✓ 设备 {device_number} 心跳监控已启动")

            # 查询设备 - 同步处理确保设备记录存在
            db = next(get_db())
            device = db.query(Device).filter(
                Device.device_number == device_number
            ).first()

            if not device:
                # 创建新设备
                device = Device(
                    device_number=device_number,
                    device_ip=client_ip,
                    online_status='online',
                    account_status='normal',
                    last_heartbeat=get_beijing_now_naive()
                )
                db.add(device)
                db.commit()
                db.refresh(device)
                print(f"创建新设备记录: ID={device.id}")

            # 更新设备状态
            device_status = db.query(DeviceStatus).filter(
                DeviceStatus.device_id == device.id
            ).first()
            if not device_status:
                device_status = DeviceStatus(device_id=device.id)
                db.add(device_status)
            device_status.is_online = True
            device_status.last_heartbeat = get_beijing_now_naive()
            db.commit()

            print(f"设备连接处理完成: {device_number}, 设备ID: {device.id}")

            # 更新状态 - 异步处理
            status_future = asyncio.Future()
            await self.db_insert_queue.put({
                'type': 'update_status',
                'device_id': device.id,
                'future': status_future
            })
            await status_future

            print(f"设备连接处理完成: {device_number}")
            
        except Exception as e:
            print(f"设备连接处理失败: {str(e)}")  # 错误日志
            try:
                db = next(get_db())
                db.rollback()
            except:
                pass
            raise

    def disconnect(self, device_number: str):
        websocket = self.active_connections.pop(device_number, None)

        # 清理心跳记录
        self.device_last_heartbeat.pop(device_number, None)

        if websocket:
            print(f"→ 设备 {device_number} 断开连接，清理心跳监控")

            # 更新设备状态为离线
            try:
                db = next(get_db())
                device = db.query(Device).filter(Device.device_number == device_number).first()
                if device:
                    device_status = db.query(DeviceStatus).filter(DeviceStatus.device_id == device.id).first()
                    if device_status:
                        device_status.is_online = False
                        device.online_status = "offline"  # 同时更新Device表状态
                        db.commit()
                        print(f"✓ 设备 {device_number} 状态已更新为离线")
            except Exception as e:
                print(f"! 更新设备 {device_number} 离线状态失败: {e}")
            finally:
                if 'db' in locals():
                    db.close()

    async def send_task(self, device_number: str, message: dict):
        """发送任务到指定设备，并等待完成"""
        if device_number not in self.device_task_queues:
            self.device_task_queues[device_number] = asyncio.Queue()
            
        # 将任务放入设备队列
        task_future = asyncio.Future()
        await self.device_task_queues[device_number].put({
            'message': message,
            'future': task_future
        })
        
        # 如果没有当前任务，立即开始处理
        if device_number not in self.device_current_tasks:
            asyncio.create_task(self._process_device_tasks(device_number))
            
        return await task_future


    async def broadcast(self, message: dict):
        for connection in self.active_connections.values():
            await connection.send_json(message)

    async def _process_device_tasks(self, device_number: str):
        """处理设备任务队列，按顺序执行"""
        while True:
            try:
                # 从队列获取任务
                task_data = await self.device_task_queues[device_number].get()
                message = task_data['message']
                task_future = task_data['future']
                
                # 标记当前任务
                self.device_current_tasks[device_number] = task_future
                
                # 发送任务
                websocket = self.active_connections.get(device_number)
                if not websocket:
                    task_future.set_exception(Exception("设备未连接"))
                    continue
                    
                await websocket.send_json(message)
                print(f"[WebSocket] 发送任务到设备 {device_number}: {message}")
                
                # 等待任务完成(通过task_completion消息处理)
                try:
                    await asyncio.wait_for(task_future, timeout=300)  # 5分钟超时
                except asyncio.TimeoutError:
                    task_future.set_exception(Exception("任务超时"))
                    
            except Exception as e:
                print(f"[WebSocket] 处理设备任务失败: {str(e)}")
                if device_number in self.device_current_tasks:
                    self.device_current_tasks[device_number].set_exception(e)
            finally:
                if device_number in self.device_current_tasks:
                    del self.device_current_tasks[device_number]

    async def _handle_task_completion(self, device_number: str, result: dict):
        """处理任务完成消息"""
        db = None
        try:
            print(f"← 收到设备 {device_number} 任务完成消息: {result}")

            task_id = result.get('task_id')
            device_id = result.get('device_id')
            status = 'done' if result.get('status') == 'success' else 'failed'

            if not task_id or not device_id:
                print(f"! 任务完成消息缺少必要字段: task_id={task_id}, device_id={device_id}")
                return

            # 使用单一Session处理整个流程
            db = next(get_db())

            # 更新任务状态
            task_queue = db.query(TaskQueue).filter(
                TaskQueue.task_id == task_id,
                TaskQueue.device_id == device_id
            ).first()

            if task_queue:
                task_queue.status = status
                task_queue.finish_time = get_beijing_now_naive()
                task_queue.result_summary = json.dumps(result)
                db.commit()
                print(f"✓ 更新任务状态: 任务 {task_id} 设备 {device_number} 状态: {status}")
            else:
                print(f"! 未找到任务队列记录: task_id={task_id}, device_id={device_id}")
                return

            # 通知任务完成
            if device_number in self.device_current_tasks:
                task_future = self.device_current_tasks[device_number]
                if not task_future.done():
                    task_future.set_result(result)
                    print(f"✓ 通知任务完成: 设备 {device_number} 任务 {task_id}")

        except Exception as e:
            print(f"! 处理任务完成消息失败: {str(e)}")
            if db:
                try:
                    db.rollback()
                except:
                    pass
            if device_number in self.device_current_tasks:
                task_future = self.device_current_tasks[device_number]
                if not task_future.done():
                    task_future.set_exception(e)
        finally:
            if db:
                try:
                    db.close()
                except:
                    pass

    async def _process_next_task(self, device_number: str, completion_data: dict):
        """处理下一个任务 - 记录任务完成时间，分组延迟由调度器处理"""
        try:
            task_id = completion_data.get('task_id')
            device_id = completion_data.get('device_id')

            if not task_id or not device_id:
                print(f"[下一任务] 缺少必要参数: task_id={task_id}, device_id={device_id}")
                return

            print(f"[下一任务] 🚀 设备 {device_number} (ID:{device_id}) 任务 {task_id} 完成")

            # 🔥 修复：只记录任务完成时间，让调度器处理分组延迟
            from app.services.optimized_scheduler import optimized_scheduler
            from app.utils.time_utils import get_beijing_now_naive

            # 记录设备最后任务完成时间
            optimized_scheduler.device_last_task_time[device_id] = get_beijing_now_naive()
            print(f"[下一任务] ✅ 设备 {device_id} 任务完成时间已记录，调度器将处理分组延迟")

        except Exception as e:
            print(f"! 处理下一任务失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 🔥 删除：无效的分组延迟函数，分组延迟现在由调度器统一处理

    async def _poll_task_status(self, device_id: int, group_id: int):
        """轮询任务状态"""
        while True:
            try:
                db = next(get_db())
                # 查询所有未完成的任务
                pending_tasks = db.query(GroupTaskStatus).filter(
                    GroupTaskStatus.device_id == device_id,
                    GroupTaskStatus.group_id == group_id,
                    GroupTaskStatus.status != 'completed'
                ).all()
                
                if not pending_tasks:
                    print(f"设备 {device_id} 的所有任务已完成")
                    break
                    
                print(f"轮询设备 {device_id} 的任务状态 - 待完成任务: {len(pending_tasks)}")
                
                # 检查每个任务状态
                for task in pending_tasks:
                    # 这里可以添加具体的状态检查逻辑
                    # 例如查询设备返回的任务状态
                    pass
                    
                await asyncio.sleep(5)  # 每5秒轮询一次
                
            except Exception as e:
                print(f"任务状态轮询异常: {str(e)}")
                await asyncio.sleep(10)  # 出错时延长等待时间

    async def heartbeat_checker(self):
        """优化的心跳检测器 - 被动检测客户端心跳"""
        while True:
            await asyncio.sleep(self.heartbeat_check_interval)
            current_time = get_beijing_now_naive()

            # 检查所有连接的设备心跳状态
            disconnected_devices = []

            for device_number in list(self.active_connections.keys()):
                last_heartbeat = self.device_last_heartbeat.get(device_number)

                if last_heartbeat:
                    # 计算心跳超时
                    time_diff = (current_time - last_heartbeat).total_seconds()

                    if time_diff > self.heartbeat_timeout:
                        print(f"! 设备 {device_number} 心跳超时 ({time_diff:.1f}秒)")
                        disconnected_devices.append(device_number)
                    else:
                        print(f"✓ 设备 {device_number} 心跳正常 (上次: {time_diff:.1f}秒前)")
                else:
                    # 新连接设备，给予一次机会
                    print(f"? 设备 {device_number} 首次心跳检查，设置初始时间")
                    self.device_last_heartbeat[device_number] = current_time

            # 断开超时的设备
            for device_number in disconnected_devices:
                print(f"→ 断开心跳超时设备: {device_number}")
                self.disconnect(device_number)

            # 批量更新数据库中的在线状态
            if self.active_connections:
                await self._batch_update_heartbeat_status()

    async def _batch_update_heartbeat_status(self):
        """批量更新设备心跳状态到数据库"""
        try:
            db = next(get_db())
            current_time = get_beijing_now_naive()

            # 批量更新在线设备的心跳时间
            online_devices = list(self.active_connections.keys())
            if online_devices:
                # 使用原生SQL批量更新，提高性能
                from sqlalchemy import text

                # 1. 更新device_status表
                update_status_sql = text("""
                    UPDATE device_status ds
                    JOIN devices d ON ds.device_id = d.id
                    SET ds.last_heartbeat = :current_time,
                        ds.is_online = 1,
                        ds.status = 'online',
                        ds.last_update = :current_time
                    WHERE d.device_number IN :device_numbers
                """)

                db.execute(update_status_sql, {
                    'current_time': current_time,
                    'device_numbers': tuple(online_devices)
                })

                # 2. 同时更新devices表
                update_devices_sql = text("""
                    UPDATE devices
                    SET last_heartbeat = :current_time,
                        online_status = 'online'
                    WHERE device_number IN :device_numbers
                """)

                db.execute(update_devices_sql, {
                    'current_time': current_time,
                    'device_numbers': tuple(online_devices)
                })

                db.commit()

                print(f"✓ 批量更新 {len(online_devices)} 个设备心跳状态")

        except Exception as e:
            print(f"! 批量更新心跳状态失败: {e}")
            if db:
                db.rollback()
        finally:
            if db:
                db.close()

    async def _handle_heartbeat(self, device_number: str, data: dict, websocket: WebSocket):
        """静默心跳处理 - 不返回响应"""
        try:
            # 更新内存中的心跳时间
            self.device_last_heartbeat[device_number] = get_beijing_now_naive()

            # 静默处理，不发送任何响应
            # 减少日志频率 - 只在调试模式下打印
            if hasattr(self, 'debug_heartbeat') and self.debug_heartbeat:
                print(f"← 设备 {device_number} 心跳 (静默处理)")

        except Exception as e:
            print(f"! 处理设备 {device_number} 心跳失败: {e}")

    async def _handle_special_heartbeat(self, device_number: str, raw_text: str, websocket: WebSocket):
        """处理特殊格式的心跳消息"""
        try:
            # 检查是否是ISO时间戳格式的心跳
            if self._is_iso_timestamp(raw_text.strip()):
                # 静默处理，只更新心跳时间
                self.device_last_heartbeat[device_number] = get_beijing_now_naive()

                # 调试模式下才打印日志
                if hasattr(self, 'debug_heartbeat') and self.debug_heartbeat:
                    print(f"← 设备 {device_number} ISO时间戳心跳: {raw_text.strip()}")

                return True

            # 检查是否是Date对象字符串格式的心跳
            elif raw_text.strip().startswith("new Date-"):
                # 静默处理，只更新心跳时间
                self.device_last_heartbeat[device_number] = get_beijing_now_naive()

                # 调试模式下才打印日志
                if hasattr(self, 'debug_heartbeat') and self.debug_heartbeat:
                    print(f"← 设备 {device_number} Date对象心跳: {raw_text.strip()[:50]}...")

                return True

            # 检查是否是纯时间戳数字
            elif raw_text.strip().isdigit():
                timestamp = int(raw_text.strip())
                # 检查是否是合理的时间戳（10位或13位）
                if 1000000000 <= timestamp <= 9999999999999:
                    # 静默处理，只更新心跳时间
                    self.device_last_heartbeat[device_number] = get_beijing_now_naive()

                    # 调试模式下才打印日志
                    if hasattr(self, 'debug_heartbeat') and self.debug_heartbeat:
                        print(f"← 设备 {device_number} 数字时间戳心跳: {timestamp}")

                    return True

            return False

        except Exception as e:
            print(f"! 处理特殊心跳格式失败: {e}")
            return False

    def _is_iso_timestamp(self, text: str) -> bool:
        """检查是否是ISO时间戳格式"""
        try:
            # 尝试解析ISO格式时间戳
            from datetime import datetime as dt
            dt.fromisoformat(text.replace('Z', '+00:00'))
            return True
        except:
            return False

    async def _fill_device_id(self, device_number: str, data: dict):
        """根据设备号自动填充 device_id"""
        db = None
        try:
            db = next(get_db())

            # 根据设备号查找设备ID
            device = db.query(Device).filter(Device.device_number == device_number).first()
            if device:
                # 创建新的数据副本并填充 device_id
                data_with_device_id = data.copy()
                data_with_device_id['device_id'] = device.id

                print(f"✓ 自动填充设备ID: {device_number} -> {device.id}")
                return data_with_device_id
            else:
                print(f"! 未找到设备: {device_number}")
                return None

        except Exception as e:
            print(f"! 填充设备ID失败: {str(e)}")
            return None
        finally:
            if db:
                db.close()

manager = ConnectionManager()

@router.get("/test")
async def websocket_test():
    """WebSocket连接测试端点"""
    return {
        "status": "WebSocket服务运行中",
        "connection_url": "ws://[服务器IP]:8000/ws/[设备号]",
        "example": "ws://*************:8000/ws/test101"
    }

@router.websocket("/{device_number}")
async def websocket_endpoint(websocket: WebSocket, device_number: str):
    client_ip = websocket.client.host if websocket.client else None
    print(f"→ 设备 {device_number} 正在连接, IP: {client_ip}")
    print(f"→ 完整WebSocket URL: ws://{client_ip}:8000/ws/{device_number}")
    await manager.connect(device_number, websocket, client_ip)
    
    try:
        while True:
            try:
                # 接收客户端消息(增加超时) - 先接收原始消息
                message = await asyncio.wait_for(
                    websocket.receive(),
                    timeout=300.0  # 5分钟超时
                )

                # 处理不同类型的消息
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        # 文本消息，尝试解析JSON
                        raw_text = message['text']
                        print(f"← 收到设备 {device_number} 文本消息: {raw_text}")

                        try:
                            data = json.loads(raw_text)
                        except json.JSONDecodeError as e:
                            # 检查是否是心跳消息的特殊格式
                            if await manager._handle_special_heartbeat(device_number, raw_text, websocket):
                                continue

                            print(f"! 设备 {device_number} JSON解析失败: {str(e)}")
                            await websocket.send_json({
                                "type": "error",
                                "message": "invalid_json",
                                "details": str(e),
                                "received_text": raw_text[:100]  # 只显示前100个字符
                            })
                            continue

                    elif 'bytes' in message:
                        # 二进制消息 - 静默忽略，减少日志噪音
                        binary_data = message['bytes']

                        # 检查是否是心跳相关的二进制数据
                        if len(binary_data) < 100:  # 小的二进制数据可能是心跳
                            try:
                                # 尝试解码为文本
                                text_data = binary_data.decode('utf-8', errors='ignore')
                                if text_data.strip():
                                    print(f"← 设备 {device_number} 二进制心跳数据: {text_data[:50]}...")
                                    # 尝试作为特殊心跳处理
                                    if await manager._handle_special_heartbeat(device_number, text_data, websocket):
                                        continue
                            except:
                                pass

                        # 对于非心跳的二进制数据，静默忽略
                        print(f"← 设备 {device_number} 忽略二进制消息 ({len(binary_data)} bytes)")
                        continue
                    else:
                        print(f"! 设备 {device_number} 发送了未知格式消息: {message}")
                        continue

                elif message['type'] == 'websocket.disconnect':
                    print(f"← 设备 {device_number} 主动断开连接")
                    break
                else:
                    print(f"! 设备 {device_number} 未知消息类型: {message['type']}")
                    continue

                # 消息格式验证
                if not isinstance(data, dict):
                    print(f"! 设备 {device_number} 消息格式错误: 非字典类型")
                    await websocket.send_json({
                        "type": "error",
                        "message": "invalid_message_format",
                        "expected": "JSON object",
                        "received": str(type(data).__name__)
                    })
                    continue
                    
                # 统一消息类型字段处理 (兼容type和msg_type)
                msg_type = data.get("msg_type") or data.get("type")
                if not msg_type:
                    print(f"! 设备 {device_number} 消息缺少类型字段")
                    await websocket.send_json({
                        "type": "error",
                        "message": "missing_msg_type"
                    })
                    continue
                
                # 处理心跳消息
                if msg_type == "heartbeat":
                    await manager._handle_heartbeat(device_number, data, websocket)
                
                # 处理任务完成消息
                elif msg_type == "task_completion":
                    # 只需要 task_id 和 status，device_id 由服务端自动填充
                    required_fields = ["task_id", "status"]
                    if all(field in data for field in required_fields):
                        print(f"← 设备 {device_number} 任务完成: {data}")

                        # 自动填充 device_id
                        data_with_device_id = await manager._fill_device_id(device_number, data)
                        if data_with_device_id:
                            # 调用任务完成处理方法
                            await manager._handle_task_completion(device_number, data_with_device_id)

                            # 简化的任务完成后处理逻辑
                            await manager._process_next_task(device_number, data_with_device_id)

                            # 返回确认响应
                            await websocket.send_json({
                                "type": "ack",
                                "task_id": data["task_id"],
                                "status": "received",
                                "timestamp": beijing_timestamp()
                            })
                        else:
                            print(f"! 设备 {device_number} 不存在或未找到")
                            await websocket.send_json({
                                "type": "error",
                                "message": "device_not_found",
                                "device_number": device_number
                            })
                    else:
                        print(f"! 设备 {device_number} 任务完成消息缺少必要字段")
                        await websocket.send_json({
                            "type": "error",
                            "message": "missing_required_fields",
                            "required": required_fields,
                            "received": list(data.keys())
                        })
                
                # 处理其他消息类型
                else:
                    print(f"← 设备 {device_number} 未知消息类型: {msg_type}")
                    await websocket.send_json({
                        "type": "error",
                        "message": "unknown_message_type",
                        "received_type": msg_type
                    })
                    
            except asyncio.TimeoutError:
                print(f"! 设备 {device_number} 消息接收超时")
                await websocket.send_json({
                    "type": "error",
                    "message": "timeout"
                })
                continue
                
    except Exception as e:
        print(f"! 设备 {device_number} 连接异常: {type(e).__name__}: {str(e)}")
    finally:
        manager.disconnect(device_number)
        print(f"→ 设备 {device_number} 连接已关闭")
