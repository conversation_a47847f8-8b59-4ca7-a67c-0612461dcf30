#!/usr/bin/env python3
"""
检查WebSocket连接状态
"""

import requests
import json

def check_websocket_connections():
    """检查WebSocket连接状态"""
    print("🔗 检查WebSocket连接状态...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 检查WebSocket管理器状态
        response = requests.get(f"{base_url}/ws/test", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ WebSocket服务正常")
            print(f"   状态: {result.get('status')}")
            print(f"   连接URL: {result.get('connection_url')}")
        else:
            print(f"❌ WebSocket服务异常: {response.status_code}")
            return False
        
        # 获取设备列表
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"\n📱 设备状态检查:")
            print(f"   总设备数: {len(devices)}")
            
            online_devices = []
            offline_devices = []
            
            for device in devices:
                device_number = device.get('device_number')
                online_status = device.get('online_status')
                last_heartbeat = device.get('last_heartbeat')
                
                if online_status == 'online':
                    online_devices.append(device)
                    print(f"   🟢 {device_number}: 在线 (心跳: {last_heartbeat})")
                else:
                    offline_devices.append(device)
                    print(f"   🔴 {device_number}: 离线 (心跳: {last_heartbeat})")
            
            print(f"\n📊 统计:")
            print(f"   在线设备: {len(online_devices)}个")
            print(f"   离线设备: {len(offline_devices)}个")
            
            return online_devices
        else:
            print(f"❌ 获取设备列表失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 检查WebSocket连接异常: {e}")
        return []

def check_scheduler_device_queues():
    """检查调度器设备队列状态"""
    print("\n⚙️ 检查调度器设备队列状态...")
    
    # 这里需要一个API来获取调度器内部状态
    # 暂时通过任务队列状态来推断
    try:
        base_url = "http://localhost:8000"
        
        # 获取运行中任务信息
        response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
        if response.status_code == 200:
            running_info = response.json()
            tasks = running_info.get('tasks', [])
            
            print(f"   运行中任务: {len(tasks)}个")
            
            if tasks:
                print("   任务详情:")
                for task in tasks:
                    task_id = task.get('task_id')
                    device_id = task.get('device_id')
                    runtime = task.get('runtime_minutes', 0)
                    
                    print(f"     - 任务{task_id} -> 设备{device_id}: 运行{runtime}分钟")
            else:
                print("   ⚠️ 没有运行中的任务")
            
            return len(tasks) > 0
        else:
            print(f"   ❌ 获取运行任务失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查调度器异常: {e}")
        return False

def check_task_queue_records():
    """检查TaskQueue记录"""
    print("\n📋 检查TaskQueue记录...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取所有任务
        response = requests.get(f"{base_url}/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            
            # 统计任务状态
            pending_tasks = [t for t in tasks if t.get('status') == 'pending']
            running_tasks = [t for t in tasks if t.get('status') == 'running']
            
            print(f"   总任务数: {len(tasks)}")
            print(f"   pending任务: {len(pending_tasks)}")
            print(f"   running任务: {len(running_tasks)}")
            
            # 显示最近的pending任务
            if pending_tasks:
                print("   最近的pending任务:")
                for task in pending_tasks[-5:]:
                    task_id = task.get('id')
                    task_type = task.get('task_type')
                    target_scope = task.get('target_scope')
                    target_id = task.get('target_id')
                    
                    print(f"     - 任务{task_id}: {task_type} -> {target_scope}({target_id})")
            
            return pending_tasks, running_tasks
        else:
            print(f"   ❌ 获取任务列表失败: {response.status_code}")
            return [], []
            
    except Exception as e:
        print(f"   ❌ 检查任务队列异常: {e}")
        return [], []

def simulate_websocket_connection():
    """模拟WebSocket连接测试"""
    print("\n🧪 模拟WebSocket连接测试...")
    
    try:
        import websocket
        import threading
        import time
        
        # WebSocket连接URL
        ws_url = "ws://localhost:8000/ws/test001"  # 使用test001设备
        
        def on_message(ws, message):
            print(f"   📨 收到消息: {message}")
        
        def on_error(ws, error):
            print(f"   ❌ WebSocket错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"   🔌 WebSocket连接关闭: {close_status_code} - {close_msg}")
        
        def on_open(ws):
            print(f"   ✅ WebSocket连接成功")
            
            # 发送心跳
            def send_heartbeat():
                for i in range(3):
                    if ws.sock and ws.sock.connected:
                        heartbeat_msg = {
                            "type": "heartbeat",
                            "timestamp": time.time(),
                            "device_id": "test001"
                        }
                        ws.send(json.dumps(heartbeat_msg))
                        print(f"   💓 发送心跳 {i+1}")
                        time.sleep(2)
                    else:
                        break
                ws.close()
            
            threading.Thread(target=send_heartbeat, daemon=True).start()
        
        print(f"   🔗 尝试连接: {ws_url}")
        
        # 创建WebSocket连接
        ws = websocket.WebSocketApp(ws_url,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 运行连接（超时10秒）
        ws.run_forever(ping_interval=30, ping_timeout=10)
        
        return True
        
    except ImportError:
        print("   ⚠️ 需要安装websocket-client: pip install websocket-client")
        return False
    except Exception as e:
        print(f"   ❌ WebSocket连接测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 WebSocket连接状态诊断")
    print("="*50)
    
    # 检查WebSocket连接
    online_devices = check_websocket_connections()
    
    # 检查调度器状态
    scheduler_running = check_scheduler_device_queues()
    
    # 检查任务队列
    pending_tasks, running_tasks = check_task_queue_records()
    
    # 模拟WebSocket连接
    websocket_test = simulate_websocket_connection()
    
    print("\n" + "="*50)
    print("📊 诊断结果:")
    
    if not online_devices:
        print("❌ 主要问题: 没有在线设备")
        print("   可能原因:")
        print("   1. 客户端没有连接WebSocket")
        print("   2. 心跳机制不工作")
        print("   3. WebSocket连接断开")
        
        print("\n   解决方案:")
        print("   1. 检查客户端WebSocket连接代码")
        print("   2. 确认WebSocket URL正确")
        print("   3. 检查网络连接")
        print("   4. 查看客户端日志")
        
    elif pending_tasks and not running_tasks:
        print("❌ 主要问题: 有pending任务但没有running任务")
        print("   可能原因:")
        print("   1. 调度器没有正确处理设备队列")
        print("   2. WebSocket连接状态检查有问题")
        print("   3. 任务分发逻辑有bug")
        
        print("\n   解决方案:")
        print("   1. 检查调度器的_process_device_queue方法")
        print("   2. 确认WebSocket active_connections状态")
        print("   3. 检查任务分发日志")
        
    elif online_devices and pending_tasks:
        print("⚠️ 设备在线但任务未执行")
        print(f"   在线设备: {len(online_devices)}个")
        print(f"   待执行任务: {len(pending_tasks)}个")
        
        print("\n   建议检查:")
        print("   1. WebSocket连接是否真正建立")
        print("   2. 调度器是否正确识别在线设备")
        print("   3. 任务分发到设备队列是否成功")
        
    else:
        print("ℹ️ 系统状态:")
        print(f"   - 在线设备: {len(online_devices)}个")
        print(f"   - 待执行任务: {len(pending_tasks)}个")
        print(f"   - 运行中任务: {len(running_tasks)}个")
        print(f"   - 调度器运行: {'是' if scheduler_running else '否'}")
        print(f"   - WebSocket测试: {'成功' if websocket_test else '失败'}")

if __name__ == "__main__":
    main()
