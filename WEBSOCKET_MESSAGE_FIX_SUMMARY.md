# WebSocket消息处理修复总结

## 🚨 问题描述

设备 `devi201` 连接后出现以下错误：
```
设备连接处理完成: devi201
! 设备 devi201 消息JSON解析失败: Expecting value: line 1 column 1 (char 0)
! 设备 devi201 连接异常: KeyError: 'text'
→ 设备 devi201 断开连接，清理心跳监控
```

## 🔍 根本原因分析

### 1. **错误的消息接收方式**

**问题代码：**
```python
# 直接使用 receive_json() 假设所有消息都是JSON
data = await websocket.receive_json()
```

**问题：**
- 客户端可能发送非JSON格式的文本消息
- `receive_json()` 遇到非JSON文本会抛出异常
- 异常处理不当导致 `KeyError: 'text'`

### 2. **消息类型检测缺失**

**原代码问题：**
- 没有检查消息是文本还是二进制
- 没有区分WebSocket消息类型
- 直接假设所有消息都是有效JSON

### 3. **异常处理不完整**

**原代码问题：**
- JSON解析异常处理在错误的位置
- 没有处理不同类型的WebSocket消息
- 错误信息不够详细，难以调试

## 🛠️ 修复方案

### 1. **改进消息接收逻辑**

**修复前：**
```python
data = await websocket.receive_json()  # 直接解析JSON
```

**修复后：**
```python
# 先接收原始消息
message = await websocket.receive()

# 检查消息类型
if message['type'] == 'websocket.receive':
    if 'text' in message:
        # 文本消息，尝试解析JSON
        raw_text = message['text']
        try:
            data = json.loads(raw_text)
        except json.JSONDecodeError as e:
            # 详细的错误处理
            await websocket.send_json({
                "type": "error",
                "message": "invalid_json",
                "details": str(e),
                "received_text": raw_text[:100]
            })
            continue
    elif 'bytes' in message:
        # 二进制消息处理
        await websocket.send_json({
            "type": "error",
            "message": "binary_not_supported"
        })
        continue
```

### 2. **完善消息类型检测**

**新增功能：**
```python
# 支持不同的WebSocket消息类型
elif message['type'] == 'websocket.disconnect':
    print(f"← 设备 {device_number} 主动断开连接")
    break
else:
    print(f"! 设备 {device_number} 未知消息类型: {message['type']}")
    continue
```

### 3. **增强错误响应**

**改进的错误响应：**
```python
# 提供详细的错误信息
await websocket.send_json({
    "type": "error",
    "message": "invalid_message_format",
    "expected": "JSON object",
    "received": str(type(data).__name__),
    "received_text": raw_text[:100]  # 调试信息
})
```

## 📊 修复效果

### 修复前的问题：
- ❌ 非JSON消息导致连接断开
- ❌ 错误信息不明确
- ❌ 无法处理不同类型的消息
- ❌ 调试困难

### 修复后的改进：
- ✅ 支持各种消息格式
- ✅ 详细的错误信息和调试信息
- ✅ 优雅处理异常情况
- ✅ 连接稳定性提升

## 🔧 关键修改

### 1. **app/websocket/ws_manager.py**

#### A. 消息接收逻辑重构
```python
# 第467-521行：完全重写消息接收逻辑
- 使用 websocket.receive() 替代 receive_json()
- 添加消息类型检测
- 改进JSON解析错误处理
```

#### B. 移除重复异常处理
```python
# 第574-580行：移除重复的JSON异常处理
- 删除了冗余的 json.JSONDecodeError 处理
- 统一在消息接收阶段处理JSON错误
```

## 🚀 支持的消息类型

### 1. **有效JSON消息**
```json
{
    "type": "heartbeat",
    "timestamp": "2024-01-01T12:00:00Z"
}
```
**处理：** 正常解析和处理

### 2. **无效JSON文本**
```
这不是JSON格式的文本
```
**处理：** 返回详细错误信息，保持连接

### 3. **二进制消息**
```
Binary data bytes
```
**处理：** 返回不支持错误，保持连接

### 4. **空消息**
```
""
```
**处理：** 返回JSON解析错误，保持连接

## 🎯 客户端适配建议

### 1. **确保发送有效JSON**
```javascript
// 正确的消息发送方式
const message = {
    type: "heartbeat",
    timestamp: new Date().toISOString(),
    device_number: deviceNumber
};
ws.send(JSON.stringify(message));
```

### 2. **处理服务器错误响应**
```javascript
ws.onmessage = function(event) {
    try {
        const data = JSON.parse(event.data);
        
        if (data.type === "error") {
            console.error("服务器错误:", data.message);
            console.error("详细信息:", data.details);
            
            // 根据错误类型进行处理
            switch(data.message) {
                case "invalid_json":
                    console.log("发送的消息不是有效JSON");
                    break;
                case "binary_not_supported":
                    console.log("不支持二进制消息");
                    break;
                // ... 其他错误处理
            }
        }
    } catch (e) {
        console.error("解析服务器响应失败:", e);
    }
};
```

### 3. **添加消息验证**
```javascript
function sendMessage(messageObj) {
    try {
        // 验证消息格式
        if (!messageObj.type) {
            throw new Error("消息缺少type字段");
        }
        
        const jsonString = JSON.stringify(messageObj);
        ws.send(jsonString);
        
    } catch (e) {
        console.error("发送消息失败:", e);
    }
}
```

## 📈 预期效果

修复后，系统应该表现为：

1. **客户端发送任何格式的消息**
2. **服务器正确识别消息类型**
3. **对于非JSON消息，返回详细错误但保持连接**
4. **对于有效JSON消息，正常处理**
5. **提供详细的调试信息便于问题排查**

这将显著提高WebSocket连接的稳定性和容错性，减少因消息格式问题导致的连接断开。
