#!/usr/bin/env python3
"""
分组延迟WebSocket测试
连接真实设备测试分组延迟功能
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class GroupDelayTestDevice:
    """分组延迟测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                task_id = data.get('task_id')
                task_type = data.get('type')
                log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id} ({task_type})")
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                pass  # 忽略ack消息
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('type')
        
        # 模拟任务执行时间（2秒）
        execution_time = 2.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id} ({task_type})")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行{task_type}成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_group_delay_task(task_name, group_id, delay_group_sec=8):
    """创建分组延迟测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": f"test_{int(time.time())}"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 秒转毫秒
        "delay_like": 1000  # 1秒操作延迟
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            log_with_time(f"   分组延迟: {delay_group_sec}秒")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_group_delay_with_websocket():
    """使用WebSocket设备测试分组延迟"""
    log_with_time("=== 分组延迟WebSocket测试 ===")
    
    # 创建测试设备（使用分组5中的设备）
    device = GroupDelayTestDevice("ceshi212")  # 设备12，属于分组5
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(3)
        
        # 2. 创建分组延迟测试任务
        delay_seconds = 8  # 8秒分组延迟
        log_with_time(f"🚀 开始创建分组延迟测试任务（{delay_seconds}秒延迟）")
        
        test_start_time = time.time()
        
        # 创建第一个任务
        task1 = create_group_delay_task("分组延迟测试1", 5, delay_seconds)
        if not task1:
            log_with_time("❌ 第一个任务创建失败")
            return
        
        await asyncio.sleep(2)
        
        # 创建第二个任务
        task2 = create_group_delay_task("分组延迟测试2", 5, delay_seconds)
        if not task2:
            log_with_time("❌ 第二个任务创建失败")
            return
        
        await asyncio.sleep(2)
        
        # 创建第三个任务
        task3 = create_group_delay_task("分组延迟测试3", 5, delay_seconds)
        if not task3:
            log_with_time("❌ 第三个任务创建失败")
            return
        
        # 3. 等待所有任务完成
        expected_time = 3 * (2 + delay_seconds)  # 3个任务 * (2秒执行 + 8秒延迟)
        log_with_time(f"⏳ 等待所有任务完成（预计需要约{expected_time}秒）...")
        await asyncio.sleep(expected_time + 10)  # 额外等待10秒
        
        # 4. 分析分组延迟效果
        log_with_time("📊 分析分组延迟效果...")
        
        if len(device.task_times) >= 2:
            log_with_time("任务接收时间间隔分析:")
            
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期（任务完成2秒 + 分组延迟8秒 = 约10秒间隔）
                expected_min = delay_seconds + 1.5  # 8 + 1.5 = 9.5秒
                expected_max = delay_seconds + 3.0  # 8 + 3.0 = 11秒
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
            
            # 总体分析
            if len(device.task_times) >= 2:
                intervals = []
                for i in range(len(device.task_times) - 1):
                    intervals.append(device.task_times[i + 1] - device.task_times[i])
                
                avg_interval = sum(intervals) / len(intervals)
                log_with_time(f"📊 平均间隔: {avg_interval:.1f}秒")
                
                if delay_seconds + 1.5 <= avg_interval <= delay_seconds + 3.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                else:
                    log_with_time("⚠️ 分组延迟功能可能有问题")
        else:
            log_with_time("❌ 收到的任务数量不足，无法分析间隔")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: 3")
        
        if len(device.received_tasks) == 3:
            log_with_time("✅ 所有任务都被正确接收和处理")
        else:
            log_with_time(f"⚠️ 任务处理不完整: 预期3个，实际{len(device.received_tasks)}个")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 70)
    print("🧪 分组延迟WebSocket测试")
    print("💡 使用真实WebSocket连接测试分组延迟功能")
    print("=" * 70)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 50)
    print("🚀 开始测试")
    print("=" * 50)
    
    # 分组延迟WebSocket测试
    await test_group_delay_with_websocket()
    
    print("\n" + "=" * 70)
    print("📋 测试完成")
    print("💡 测试要点:")
    print("   1. 设备通过WebSocket连接到服务器")
    print("   2. 创建多个分组延迟任务")
    print("   3. 验证任务按顺序执行，有延迟间隔")
    print("   4. 分组延迟功能应该正常工作")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
