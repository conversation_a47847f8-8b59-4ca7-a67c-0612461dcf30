"""
任务超时监控API路由
"""

from fastapi import APIRouter
from app.services.task_timeout_monitor import get_timeout_stats, get_running_tasks_info, timeout_monitor

router = APIRouter(prefix="/timeout-monitor", tags=["Timeout Monitor"])

@router.get("/stats")
def get_timeout_statistics():
    """获取超时统计信息"""
    return get_timeout_stats()

@router.get("/running-tasks")
def get_running_tasks():
    """获取当前运行中的任务信息"""
    return get_running_tasks_info()

@router.get("/status")
def get_monitor_status():
    """获取监控器状态"""
    stats = get_timeout_stats()
    running_info = get_running_tasks_info()
    
    return {
        "monitor_status": "active" if stats.get("is_monitoring") else "inactive",
        "timeout_threshold_minutes": stats.get("timeout_threshold_minutes", 5),
        "running_tasks_count": running_info.get("running_count", 0),
        "timeout_stats_24h": {
            "timeout_count": stats.get("timeout_count_24h", 0),
            "total_tasks": stats.get("total_tasks_24h", 0),
            "timeout_rate": stats.get("timeout_rate_24h", 0)
        },
        "near_timeout_tasks": [
            task for task in running_info.get("tasks", [])
            if task.get("is_near_timeout", False)
        ]
    }

@router.post("/cleanup-stale-tasks")
async def cleanup_stale_tasks():
    """手动清理异常长时间运行的任务"""
    try:
        await timeout_monitor.cleanup_stale_tasks()
        return {"message": "异常任务清理完成", "status": "success"}
    except Exception as e:
        return {"message": f"清理失败: {str(e)}", "status": "error"}

@router.get("/debug-info")
def get_debug_info():
    """获取调试信息"""
    running_info = get_running_tasks_info()
    stats = get_timeout_stats()

    return {
        "monitor_active": stats.get("is_monitoring", False),
        "timeout_threshold_minutes": stats.get("timeout_threshold_minutes", 5),
        "running_tasks": running_info.get("tasks", []),
        "running_count": running_info.get("running_count", 0),
        "recent_stats": {
            "timeout_count_24h": stats.get("timeout_count_24h", 0),
            "total_tasks_24h": stats.get("total_tasks_24h", 0),
            "timeout_rate_24h": stats.get("timeout_rate_24h", 0)
        }
    }

@router.post("/start-next-task/{device_id}")
async def start_next_task_for_device(device_id: int):
    """手动为指定设备启动下一个任务"""
    try:
        from app.db import get_db
        from app.models.task import TaskQueue

        db = next(get_db())
        try:
            # 查找设备的下一个待执行任务
            next_task = db.query(TaskQueue).filter(
                TaskQueue.device_id == device_id,
                TaskQueue.status == 'pending'
            ).order_by(TaskQueue.id).first()

            if next_task:
                # 创建一个模拟的已完成任务来触发下一个任务
                class MockCompletedTask:
                    def __init__(self, device_id):
                        self.device_id = device_id
                        self.task_id = 0  # 模拟任务ID

                mock_task = MockCompletedTask(device_id)
                await timeout_monitor.start_next_task_if_needed(db, mock_task)

                return {
                    "message": f"已尝试启动设备 {device_id} 的下一个任务",
                    "next_task_id": next_task.task_id,
                    "status": "success"
                }
            else:
                return {
                    "message": f"设备 {device_id} 没有待执行的任务",
                    "status": "no_tasks"
                }
        finally:
            db.close()

    except Exception as e:
        return {
            "message": f"启动下一个任务失败: {str(e)}",
            "status": "error"
        }
