#!/usr/bin/env python3
"""
测试未分组设备的分组分配
"""

import requests
import json

def test_fresh_device_group_assignment():
    """测试未分组设备的分组分配"""
    print("🔧 测试未分组设备的分组分配...")
    
    try:
        # 1. 获取设备和分组
        devices_response = requests.get("http://localhost:8000/devices/", timeout=10)
        groups_response = requests.get("http://localhost:8000/groups/", timeout=10)
        
        if devices_response.status_code != 200 or groups_response.status_code != 200:
            print("❌ 无法获取设备或分组数据")
            return False
            
        devices = devices_response.json()
        groups = groups_response.json()
        
        # 2. 找到一个未分组的设备
        unassigned_device = None
        for device in devices:
            if device.get('group_id') is None:
                unassigned_device = device
                break
                
        if not unassigned_device:
            print("❌ 没有找到未分组的设备")
            return False
            
        # 3. 选择第一个分组
        if not groups:
            print("❌ 没有分组数据")
            return False
            
        test_group = groups[0]
        
        device_id = unassigned_device.get('id')
        group_id = test_group.get('id')
        
        print(f"   测试设备: {unassigned_device.get('device_number')} (ID: {device_id})")
        print(f"   当前分组ID: {unassigned_device.get('group_id')}")
        print(f"   目标分组: {test_group.get('group_name')} (ID: {group_id})")
        
        # 4. 添加设备到分组
        data = {'device_id': device_id, 'group_id': group_id}
        response = requests.post(f"http://localhost:8000/groups/{group_id}/devices", json=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ 设备添加到分组API调用成功")
            
            # 5. 验证分组分配
            updated_response = requests.get("http://localhost:8000/devices/", timeout=10)
            if updated_response.status_code == 200:
                updated_devices = updated_response.json()
                updated_device = next((d for d in updated_devices if d.get('id') == device_id), None)
                
                if updated_device:
                    current_group_id = updated_device.get('group_id')
                    print(f"   设备更新后的分组ID: {current_group_id}")
                    
                    if current_group_id == group_id:
                        print("✅ 数据库验证成功：设备已正确分配到分组")
                        
                        # 6. 验证分组设备数量
                        group_device_count = len([d for d in updated_devices if d.get('group_id') == group_id])
                        print(f"   分组 '{test_group.get('group_name')}' 现在有 {group_device_count} 个设备")
                        
                        return True
                    else:
                        print("❌ 数据库验证失败：设备分组未更新")
                        return False
                else:
                    print("❌ 找不到更新后的设备")
                    return False
            else:
                print("❌ 无法验证设备更新")
                return False
        else:
            print(f"❌ 设备添加到分组失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_task_creation_with_group():
    """测试使用分组创建任务"""
    print("\n📋 测试使用分组创建任务...")
    
    try:
        # 获取分组
        response = requests.get("http://localhost:8000/groups/", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取分组数据")
            return False
            
        groups = response.json()
        if not groups:
            print("❌ 没有分组数据")
            return False
            
        test_group = groups[0]
        group_id = test_group.get('id')
        
        print(f"   使用分组: {test_group.get('group_name')} (ID: {group_id})")
        
        # 创建分组任务
        task_data = {
            "task_type": "inex",
            "parameters": {
                "user_id": "group_test_user",
                "count": 1
            },
            "target_scope": "group",
            "target_id": group_id,
            "delay_group": 2000,  # 2秒
            "delay_like": 1000    # 1秒
        }
        
        response = requests.post("http://localhost:8000/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 分组任务创建成功")
            print(f"   任务ID: {result.get('id')}")
            print(f"   目标范围: {result.get('target_scope')}")
            print(f"   目标ID: {result.get('target_id')}")
            print(f"   延迟设置: 分组{result.get('delay_group')}ms, 点赞{result.get('delay_like')}ms")
            return True
        else:
            print(f"❌ 分组任务创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 分组任务创建异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试设备分组和任务创建...")
    print("="*50)
    
    tests = [
        ("未分组设备分组分配", test_fresh_device_group_assignment),
        ("分组任务创建", test_task_creation_with_group)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
            
    print("="*50)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 所有功能测试通过！")
        print("\n✅ 验证的功能:")
        print("1. 设备分组分配 - 正确更新数据库")
        print("2. 分组任务创建 - 支持分组目标")
        print("3. 延迟参数转换 - 秒转毫秒正确")
        print("4. 点赞任务优化 - 移除count参数")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
