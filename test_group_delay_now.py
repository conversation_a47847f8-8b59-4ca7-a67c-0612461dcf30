#!/usr/bin/env python3
"""
测试当前的分组延迟功能
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_like_task(task_name, group_id, delay_group_sec=10, delay_like_sec=2):
    """创建点赞任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "5139005100786544",
            "blogger_id": "5136084399294965"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def monitor_task_status(task_id, task_name, max_wait=60):
    """监控任务状态变化"""
    log_with_time(f"👀 监控 {task_name} (ID: {task_id})")
    
    start_time = time.time()
    last_status = None
    status_changes = []
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status != last_status:
                    elapsed = time.time() - start_time
                    status_changes.append((elapsed, last_status, current_status))
                    log_with_time(f"📊 {task_name} 状态: {last_status} → {current_status} (第{elapsed:.1f}秒)")
                    last_status = current_status
                
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 完成，状态: {current_status}, 总耗时: {total_time:.1f}s")
                    return completion_time, current_status, status_changes
                    
        except Exception as e:
            log_with_time(f"⚠️ 监控 {task_name} 出错: {e}")
        
        time.sleep(2)  # 每2秒检查一次
    
    log_with_time(f"⏰ {task_name} 监控超时")
    return None, 'timeout', status_changes

def test_group_delay_with_monitoring():
    """测试分组延迟并监控整个过程"""
    log_with_time("=== 测试分组延迟功能（带监控） ===")
    
    group_id = 1
    delay_group = 10  # 10秒分组延迟
    delay_like = 2    # 2秒操作延迟
    
    log_with_time(f"📱 使用分组ID: {group_id}")
    log_with_time(f"⏰ 设置: 分组延迟={delay_group}秒, 操作延迟={delay_like}秒")
    
    test_start_time = time.time()
    
    # 创建两个连续任务
    log_with_time("🚀 创建第一个任务...")
    task1 = create_like_task("分组延迟测试1", group_id, delay_group, delay_like)
    if not task1:
        return
    
    time.sleep(1)  # 等待1秒
    
    log_with_time("🚀 创建第二个任务...")
    task2 = create_like_task("分组延迟测试2", group_id, delay_group, delay_like)
    if not task2:
        return
    
    log_with_time("📋 开始监控任务执行...")
    
    # 并行监控两个任务
    import threading
    
    results = {}
    
    def monitor_task(task, task_name):
        task_id = task.get('id')
        completion_time, status, changes = monitor_task_status(task_id, task_name, 120)
        results[task_name] = {
            'completion_time': completion_time,
            'status': status,
            'changes': changes
        }
    
    # 启动监控线程
    thread1 = threading.Thread(target=monitor_task, args=(task1, "分组延迟测试1"))
    thread2 = threading.Thread(target=monitor_task, args=(task2, "分组延迟测试2"))
    
    thread1.start()
    thread2.start()
    
    # 等待监控完成
    thread1.join()
    thread2.join()
    
    # 分析结果
    log_with_time("📊 执行结果分析:")
    
    task1_result = results.get("分组延迟测试1")
    task2_result = results.get("分组延迟测试2")
    
    if task1_result and task2_result:
        if task1_result['completion_time'] and task2_result['completion_time']:
            interval = task2_result['completion_time'] - task1_result['completion_time']
            log_with_time(f"   任务1完成 → 任务2完成: {interval:.1f}秒")
            
            # 预期：任务1完成 + 10秒分组延迟 + 2秒操作延迟 = 12秒间隔
            expected_min = 10.0  # 至少10秒分组延迟
            expected_max = 15.0  # 允许一些误差
            
            if expected_min <= interval <= expected_max:
                log_with_time(f"   ✅ 分组延迟正常工作 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
            else:
                log_with_time(f"   ⚠️ 分组延迟可能异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
        
        # 显示状态变化
        log_with_time("📊 任务状态变化:")
        for task_name, result in results.items():
            log_with_time(f"   {task_name}:")
            for elapsed, old_status, new_status in result['changes']:
                log_with_time(f"     第{elapsed:.1f}秒: {old_status} → {new_status}")
    
    total_test_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_test_time:.1f}秒")

def check_current_tasks():
    """检查当前任务状态"""
    log_with_time("=== 检查当前任务状态 ===")
    
    import sys
    sys.path.append('.')
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    from datetime import datetime, timedelta
    
    db = next(get_db())
    try:
        # 检查最近5分钟的任务
        recent_time = datetime.now() - timedelta(minutes=5)
        recent_tasks = db.query(Task).filter(
            Task.create_time >= recent_time
        ).order_by(Task.create_time.desc()).limit(10).all()
        
        log_with_time(f"最近5分钟任务: {len(recent_tasks)}个")
        
        for task in recent_tasks:
            log_with_time(f"任务{task.id}: {task.task_type}, 状态={task.status}, 分组延迟={task.delay_group}ms")
            
            # 检查队列状态
            queues = db.query(TaskQueue).filter(TaskQueue.task_id == task.id).all()
            for queue in queues:
                log_with_time(f"  设备{queue.device_id}: {queue.status}")
        
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 分组延迟功能测试")
    print("💡 验证10秒分组延迟是否正常工作")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 1. 检查当前任务状态
    check_current_tasks()
    
    print("\n" + "-" * 40)
    
    # 2. 测试分组延迟
    test_group_delay_with_monitoring()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果分组延迟正常工作，应该看到:")
    print("   1. 任务1立即开始执行")
    print("   2. 任务2等待任务1完成后再等待10秒才开始")
    print("   3. 两个任务完成时间间隔约12秒（10秒延迟+2秒操作）")
    print("=" * 60)

if __name__ == "__main__":
    main()
