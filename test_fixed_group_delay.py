#!/usr/bin/env python3
"""
测试修复后的分组延迟功能
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_like_task(task_name, group_id, delay_group_sec=5, delay_like_sec=1):
    """创建点赞任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "5139005100786544",
            "blogger_id": "5136084399294965"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def wait_for_task_completion(task_id, task_name, max_wait=30):
    """等待任务完成"""
    log_with_time(f"⏳ 等待 {task_name} (ID: {task_id}) 完成...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 完成，状态: {current_status}, 耗时: {total_time:.1f}s")
                    return completion_time, current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 检查 {task_name} 状态出错: {e}")
        
        time.sleep(1)
    
    log_with_time(f"⏰ {task_name} 等待超时")
    return None, 'timeout'

def test_quick_group_delay():
    """快速测试分组延迟"""
    log_with_time("=== 快速分组延迟测试 ===")
    
    group_id = 1
    delay_group = 5  # 5秒分组延迟
    delay_like = 1   # 1秒操作延迟
    
    log_with_time(f"📱 使用分组ID: {group_id}")
    log_with_time(f"⏰ 设置: 分组延迟={delay_group}秒, 操作延迟={delay_like}秒")
    
    test_start_time = time.time()
    
    # 创建两个任务
    log_with_time("🚀 创建第一个任务...")
    task1 = create_like_task("修复测试1", group_id, delay_group, delay_like)
    if not task1:
        return
    
    time.sleep(0.5)
    
    log_with_time("🚀 创建第二个任务...")
    task2 = create_like_task("修复测试2", group_id, delay_group, delay_like)
    if not task2:
        return
    
    log_with_time("📋 开始监控任务完成...")
    
    # 等待任务完成
    completion1, status1 = wait_for_task_completion(task1.get('id'), "修复测试1", 60)
    completion2, status2 = wait_for_task_completion(task2.get('id'), "修复测试2", 60)
    
    # 分析结果
    if completion1 and completion2:
        interval = completion2 - completion1
        log_with_time(f"📊 任务间隔: {interval:.1f}秒")
        
        # 预期：任务1完成 + 5秒分组延迟 + 1秒操作延迟 = 6秒间隔
        expected_min = 5.0
        expected_max = 8.0
        
        if expected_min <= interval <= expected_max:
            log_with_time(f"✅ 分组延迟修复成功！({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
        else:
            log_with_time(f"⚠️ 分组延迟还有问题 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
    
    total_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_time:.1f}秒")

def check_database_final():
    """检查数据库最终状态"""
    log_with_time("=== 检查数据库最终状态 ===")
    
    import sys
    sys.path.append('.')
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    from datetime import datetime, timedelta
    
    db = next(get_db())
    try:
        # 检查最近2分钟的任务
        recent_time = datetime.now() - timedelta(minutes=2)
        recent_tasks = db.query(Task).filter(
            Task.create_time >= recent_time,
            Task.parameters.like('%修复测试%')
        ).order_by(Task.create_time.asc()).all()
        
        log_with_time(f"最近修复测试任务: {len(recent_tasks)}个")
        
        completion_times = []
        for task in recent_tasks:
            task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task.id).first()
            if task_queue and task_queue.finish_time:
                completion_times.append((task.id, task_queue.finish_time))
                log_with_time(f"任务{task.id}: 完成时间 {task_queue.finish_time}")
        
        # 计算实际间隔
        if len(completion_times) >= 2:
            log_with_time("数据库中的实际任务间隔:")
            for i in range(len(completion_times) - 1):
                task1_id, time1 = completion_times[i]
                task2_id, time2 = completion_times[i + 1]
                interval = (time2 - time1).total_seconds()
                log_with_time(f"  任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
        
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 修复后分组延迟测试")
    print("💡 验证分组延迟计算修复效果")
    print("=" * 50)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 30)
    print("🚀 开始测试")
    print("=" * 30)
    
    # 1. 快速分组延迟测试
    test_quick_group_delay()
    
    print("\n" + "-" * 30)
    
    # 2. 检查数据库状态
    check_database_final()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 修复要点:")
    print("   1. 移除了分发时错误的时间更新")
    print("   2. 只在任务真正完成时更新设备完成时间")
    print("   3. 分组延迟基于正确的完成时间计算")
    print("=" * 50)

if __name__ == "__main__":
    main()
