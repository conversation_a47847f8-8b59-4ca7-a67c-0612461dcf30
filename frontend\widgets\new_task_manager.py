#!/usr/bin/env python3
"""
新的任务管理器 - 专注于任务创建、删除和管理
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading
import json

from config import Config

class NewTaskManagerFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.tasks_data = []
        self.groups_data = []
        self.devices_data = []

        self.create_widgets()

        # 初始化数据
        self.refresh_data()

        # 启动自动刷新
        self.start_auto_refresh()

    def start_auto_refresh(self):
        """启动自动刷新"""
        self.refresh()
        # 每3秒刷新一次
        self.after(3000, self.start_auto_refresh)

    def create_widgets(self):
        """创建界面组件"""
        # 任务管理区域（上半部分）
        self.create_task_management_section()

        # 分隔线
        separator = ttk.Separator(self, orient='horizontal')
        separator.pack(fill=tk.X, padx=10, pady=5)

        # 快速添加任务区域（下半部分）
        self.create_quick_add_section()

    def create_quick_add_section(self):
        """创建快速添加任务区域"""
        # 主框架 - 确保有足够的空间显示内容
        add_frame = ttk.LabelFrame(self, text="🚀 快速添加任务")
        add_frame.pack(fill=tk.X, padx=10, pady=(2, 5))

        # 第一行：基本信息 - 紧凑布局
        row1 = ttk.Frame(add_frame)
        row1.pack(fill=tk.X, padx=5, pady=3)

        # 任务类型
        ttk.Label(row1, text="任务类型:", font=('Segoe UI', 8, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 3))
        self.task_type = ttk.Combobox(row1, values=["签到任务", "点赞任务", "超话签到任务", "主页关注任务"], width=10)
        self.task_type.set("点赞任务")
        self.task_type.grid(row=0, column=1, padx=3)
        self.task_type.bind('<<ComboboxSelected>>', self.on_task_type_changed)

        # 博主ID
        ttk.Label(row1, text="博主ID(可选):", font=('Segoe UI', 8)).grid(row=0, column=2, sticky=tk.W, padx=(10, 3))
        self.blogger_id = tk.StringVar()
        blogger_entry = ttk.Entry(row1, textvariable=self.blogger_id, width=15)
        blogger_entry.grid(row=0, column=3, padx=3)

        # 点赞ID
        ttk.Label(row1, text="点赞ID(可选):", font=('Segoe UI', 8)).grid(row=0, column=4, sticky=tk.W, padx=(10, 3))
        self.like_id = tk.StringVar()
        like_entry = ttk.Entry(row1, textvariable=self.like_id, width=15)
        like_entry.grid(row=0, column=5, padx=3)

        # 第二行：延迟设置 - 紧凑布局
        row2 = ttk.Frame(add_frame)
        row2.pack(fill=tk.X, padx=5, pady=3)

        # 点赞延迟
        ttk.Label(row2, text="点赞延迟:", font=('Segoe UI', 8, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 3))
        self.click_delay = tk.StringVar(value="0.5")
        ttk.Entry(row2, textvariable=self.click_delay, width=6).grid(row=0, column=1, padx=3)
        ttk.Label(row2, text="秒", font=('Segoe UI', 8)).grid(row=0, column=2, sticky=tk.W, padx=(1, 10))

        # 分组间隔
        ttk.Label(row2, text="分组间隔:", font=('Segoe UI', 8, 'bold')).grid(row=0, column=3, sticky=tk.W, padx=(0, 3))
        self.group_interval = tk.StringVar(value="2")
        ttk.Entry(row2, textvariable=self.group_interval, width=6).grid(row=0, column=4, padx=3)
        ttk.Label(row2, text="秒", font=('Segoe UI', 8)).grid(row=0, column=5, sticky=tk.W, padx=(1, 10))

        # 操作延迟
        ttk.Label(row2, text="操作延迟:", font=('Segoe UI', 8, 'bold')).grid(row=0, column=6, sticky=tk.W, padx=(0, 3))
        self.operation_delay = tk.StringVar(value="1")
        ttk.Entry(row2, textvariable=self.operation_delay, width=6).grid(row=0, column=7, padx=3)
        ttk.Label(row2, text="秒", font=('Segoe UI', 8)).grid(row=0, column=8, sticky=tk.W, padx=(1, 0))

        # 第三行：目标选择 - 紧凑布局
        row3 = ttk.Frame(add_frame)
        row3.pack(fill=tk.X, padx=5, pady=3)

        # 目标类型选择
        ttk.Label(row3, text="执行目标:", font=('Segoe UI', 8, 'bold')).pack(side=tk.LEFT)

        self.target_type = tk.StringVar(value="group")
        ttk.Radiobutton(row3, text="选择分组", variable=self.target_type, value="group",
                       command=self.on_target_type_changed).pack(side=tk.LEFT, padx=(5, 15))
        ttk.Radiobutton(row3, text="单独设备", variable=self.target_type, value="device",
                       command=self.on_target_type_changed).pack(side=tk.LEFT, padx=(0, 15))

        # 第四行：目标选择区域 - 紧凑布局
        self.target_selection_frame = ttk.Frame(add_frame)
        self.target_selection_frame.pack(fill=tk.X, padx=5, pady=3)

        # 第五行：操作按钮 - 紧凑布局
        row5 = ttk.Frame(add_frame)
        row5.pack(fill=tk.X, padx=5, pady=3)

        # 使用更紧凑的按钮布局
        ttk.Button(row5, text="🚀 创建任务", command=self.create_task,
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row5, text="🔄 刷新数据", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row5, text="💾 保存模板", command=self.save_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row5, text="📂 加载模板", command=self.load_template).pack(side=tk.LEFT)

        # 初始化目标选择
        self.on_target_type_changed()

        # 添加提示标签 - 更紧凑
        self.hint_label = ttk.Label(add_frame, text="", font=('Segoe UI', 7), foreground='#7f8c8d')
        self.hint_label.pack(fill=tk.X, padx=5, pady=1)

        # 初始化提示
        self.update_task_hint()

    def create_task_management_section(self):
        """创建任务管理区域"""
        # 主框架 - 限制高度，为快速创建区域留出空间
        mgmt_frame = ttk.LabelFrame(self, text="📋 任务管理")
        mgmt_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 2))

        # 工具栏
        toolbar = ttk.Frame(mgmt_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # 左侧统计信息
        stats_frame = ttk.Frame(toolbar)
        stats_frame.pack(side=tk.LEFT)

        # 总任务统计
        self.total_stats_label = ttk.Label(stats_frame, text="总任务: 0",
                                         font=('Segoe UI', 9, 'bold'))
        self.total_stats_label.pack(side=tk.LEFT, padx=(0, 10))

        # 成功任务统计
        self.success_stats_label = ttk.Label(stats_frame, text="成功: 0",
                                           font=('Segoe UI', 9), foreground='#27ae60')
        self.success_stats_label.pack(side=tk.LEFT, padx=(0, 10))

        # 失败任务统计
        self.failed_stats_label = ttk.Label(stats_frame, text="失败: 0",
                                          font=('Segoe UI', 9), foreground='#e74c3c')
        self.failed_stats_label.pack(side=tk.LEFT, padx=(0, 10))

        # 运行中任务统计
        self.running_stats_label = ttk.Label(stats_frame, text="运行中: 0",
                                           font=('Segoe UI', 9), foreground='#3498db')
        self.running_stats_label.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧操作按钮
        btn_frame = ttk.Frame(toolbar)
        btn_frame.pack(side=tk.RIGHT)

        ttk.Button(btn_frame, text="🗑️ 删除选中", command=self.delete_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="⏸️ 暂停选中", command=self.pause_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="▶️ 恢复选中", command=self.resume_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="🔄 刷新列表", command=self.refresh).pack(side=tk.LEFT, padx=2)

        # 任务列表
        list_frame = ttk.Frame(mgmt_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview - 大幅减少高度，确保下方按钮可见
        columns = ('id', 'type', 'status', 'blogger_id', 'like_id', 'device_stats', 'target', 'create_time')
        self.task_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=6)

        # 设置列标题
        headers = {
            'id': '🆔 ID',
            'type': '📝 类型',
            'status': '📊 状态',
            'blogger_id': '👤 博主ID',
            'like_id': '❤️ 点赞ID',
            'device_stats': '📊 设备统计',
            'target': '🎯 目标',
            'create_time': '⏰ 创建时间'
        }

        for col, header in headers.items():
            self.task_tree.heading(col, text=header)

        # 设置列宽
        self.task_tree.column('id', width=60, minwidth=50)
        self.task_tree.column('type', width=100, minwidth=80)
        self.task_tree.column('status', width=80, minwidth=70)
        self.task_tree.column('blogger_id', width=100, minwidth=80)
        self.task_tree.column('like_id', width=100, minwidth=80)
        self.task_tree.column('device_stats', width=150, minwidth=120)
        self.task_tree.column('target', width=100, minwidth=80)
        self.task_tree.column('create_time', width=160, minwidth=140)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.task_tree.xview)
        self.task_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.task_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)

        # 绑定事件
        self.task_tree.bind('<<TreeviewSelect>>', self.on_task_selected)
        self.task_tree.bind('<Double-1>', self.on_task_double_click)
        self.task_tree.bind('<Button-3>', self.show_context_menu)

        # 创建右键菜单
        self.create_context_menu()

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="📊 查看详情", command=self.show_task_details)
        self.context_menu.add_command(label="⏸️ 暂停任务", command=self.pause_selected_task)
        self.context_menu.add_command(label="▶️ 恢复任务", command=self.resume_selected_task)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 复制任务ID", command=self.copy_task_id)
        self.context_menu.add_command(label="🗑️ 删除任务", command=self.delete_selected_task)

    def on_target_type_changed(self):
        """目标类型变化"""
        # 清空目标选择区域
        for widget in self.target_selection_frame.winfo_children():
            widget.destroy()

        if self.target_type.get() == "group":
            self.create_group_selection()
        else:
            self.create_device_selection()

    def create_group_selection(self):
        """创建分组选择界面"""
        ttk.Label(self.target_selection_frame, text="选择分组:", font=('Segoe UI', 8, 'bold')).pack(side=tk.LEFT)

        # 分组选择容器 - 更紧凑的布局
        groups_container = ttk.Frame(self.target_selection_frame)
        groups_container.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 控制按钮 - 更小的按钮
        control_frame = ttk.Frame(groups_container)
        control_frame.pack(fill=tk.X, pady=1)

        ttk.Button(control_frame, text="全选", command=self.select_all_groups, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="全不选", command=self.deselect_all_groups, width=6).pack(side=tk.LEFT, padx=1)

        # 分组复选框容器 - 更紧凑
        groups_frame = ttk.Frame(groups_container)
        groups_frame.pack(fill=tk.X, pady=1)

        # 分组复选框
        self.group_vars = {}
        if not self.groups_data:
            ttk.Label(groups_frame, text="暂无分组数据，请点击'刷新数据'", foreground='red').pack()
        else:
            # 使用网格布局显示分组，每行显示3个
            for i, group in enumerate(self.groups_data):
                group_id = group.get('id')
                group_name = group.get('group_name', '')

                # 计算设备数量
                device_count = len([d for d in self.devices_data if d.get('group_id') == group_id])

                var = tk.BooleanVar()
                self.group_vars[group_id] = var

                cb = ttk.Checkbutton(
                    groups_frame,
                    text=f"{group_name} ({device_count}设备)",
                    variable=var
                )
                # 网格布局：每行4个，更紧凑
                row = i // 4
                col = i % 4
                cb.grid(row=row, column=col, sticky=tk.W, padx=3, pady=1)

            # 配置网格权重 - 每行4个
            for col in range(4):
                groups_frame.grid_columnconfigure(col, weight=1)

    def create_device_selection(self):
        """创建设备选择界面"""
        ttk.Label(self.target_selection_frame, text="选择设备:", font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)

        # 设备选择下拉框
        device_values = []
        self.device_map = {}

        if not self.devices_data:
            device_values = ["暂无设备数据，请点击'刷新数据'"]
            self.device_map = {}
        else:
            for device in self.devices_data:
                device_id = device.get('id')
                device_number = device.get('device_number', '')
                online_status = "🟢" if device.get('online_status') == 'online' else "🔴"

                display_text = f"{online_status} {device_number}"
                device_values.append(display_text)
                self.device_map[display_text] = device_id

        self.device_combo = ttk.Combobox(self.target_selection_frame, values=device_values, width=30)
        self.device_combo.pack(side=tk.LEFT, padx=10)

        if device_values and not device_values[0].startswith("暂无"):
            self.device_combo.set(device_values[0])  # 默认选择第一个设备

    def select_all_groups(self):
        """全选分组"""
        for var in self.group_vars.values():
            var.set(True)

    def deselect_all_groups(self):
        """全不选分组"""
        for var in self.group_vars.values():
            var.set(False)

    def on_task_type_changed(self, event=None):
        """任务类型变化时更新提示"""
        self.update_task_hint()

    def update_task_hint(self):
        """更新任务提示信息"""
        task_type = self.task_type.get()
        hints = {
            "签到任务": "💡 签到任务：博主ID和点赞ID可不填，系统将执行默认签到",
            "点赞任务": "💡 点赞任务：建议填写博主ID和点赞ID，不填则执行通用点赞",
            "超话签到任务": "💡 超话签到：建议填写博主ID（超话ID），点赞ID可选",
            "主页关注任务": "💡 主页关注：建议填写博主ID（用户ID），点赞ID可不填"
        }

        hint_text = hints.get(task_type, "💡 请选择任务类型")
        self.hint_label.configure(text=hint_text)

    def refresh_data(self):
        """刷新数据"""
        def fetch_data():
            try:
                self.groups_data = self.api_client.get_groups() or []
                self.devices_data = self.api_client.get_devices() or []
                print(f"📊 数据加载完成: {len(self.groups_data)}个分组, {len(self.devices_data)}台设备")
                self.winfo_toplevel().after(0, self.on_target_type_changed)
            except Exception as e:
                print(f"❌ 刷新数据失败: {e}")

        threading.Thread(target=fetch_data, daemon=True).start()

    def create_task(self):
        """创建任务"""
        try:
            # 获取任务类型
            task_type_name = self.task_type.get()
            task_type_map = {
                "签到任务": "sign",
                "点赞任务": "like",
                "超话签到任务": "page_sign",
                "主页关注任务": "inex"
            }
            task_type = task_type_map.get(task_type_name)

            # 根据任务类型验证必要字段
            blogger_id = self.blogger_id.get().strip()
            like_id = self.like_id.get().strip()

            if task_type in ["like", "page_sign"] and not blogger_id:
                messagebox.showerror("错误", f"{task_type_name}需要填写博主ID")
                return
            if task_type == "like" and not like_id:
                messagebox.showerror("错误", f"{task_type_name}需要填写点赞ID")
                return
            if task_type == "inex" and not blogger_id:
                messagebox.showerror("错误", f"{task_type_name}需要填写用户ID")
                return

            delay_click_ms = int(float(self.click_delay.get()) * 1000)

            # 构建参数 - 根据任务类型和输入情况
            parameters = {"delay_click": delay_click_ms}

            if task_type == "sign":
                parameters["count"] = 1
            elif task_type == "like":
                if blogger_id:
                    parameters["blogger_id"] = blogger_id
                if like_id:
                    parameters["like_id"] = like_id
            elif task_type == "page_sign":
                if blogger_id:
                    parameters["blogger_id"] = blogger_id
                    parameters["page_url"] = f"https://weibo.com/p/{blogger_id}"
                if like_id:
                    parameters["like_id"] = like_id
            elif task_type == "inex":
                if blogger_id:
                    parameters["user_id"] = blogger_id
                parameters["count"] = 1

            # 获取目标
            if self.target_type.get() == "group":
                # 分组模式
                selected_groups = []
                for group_id, var in self.group_vars.items():
                    if var.get():
                        selected_groups.append(group_id)

                if not selected_groups:
                    messagebox.showerror("错误", "请至少选择一个分组")
                    return

                # 创建多个任务
                self.create_group_tasks(task_type, parameters, selected_groups)
            else:
                # 单设备模式
                device_display = self.device_combo.get()
                if not device_display:
                    messagebox.showerror("错误", "请选择一个设备")
                    return

                device_id = self.device_map.get(device_display)
                if not device_id:
                    messagebox.showerror("错误", "无效的设备选择")
                    return

                # 创建单个任务
                self.create_device_task(task_type, parameters, device_id)

        except Exception as e:
            messagebox.showerror("错误", f"创建任务失败: {e}")

    def create_group_tasks(self, task_type, parameters, group_ids):
        """为分组创建任务"""
        try:
            delay_group = int(float(self.group_interval.get()) * 1000)
            delay_like = int(float(self.operation_delay.get()) * 1000)

            created_count = 0
            failed_count = 0

            for group_id in group_ids:
                task_data = {
                    "task_type": task_type,
                    "parameters": parameters,
                    "target_scope": "group",
                    "target_id": group_id,
                    "delay_group": delay_group,
                    "delay_like": delay_like
                }

                result = self.api_client.create_task(task_data)
                if result:
                    created_count += 1
                else:
                    failed_count += 1

            # 显示结果
            if created_count > 0:
                message = f"成功创建 {created_count} 个任务"
                if failed_count > 0:
                    message += f"，失败 {failed_count} 个"
                messagebox.showinfo("创建完成", message)
                self.refresh()
            else:
                messagebox.showerror("创建失败", "所有任务创建失败")

        except Exception as e:
            messagebox.showerror("错误", f"批量创建任务失败: {e}")

    def create_device_task(self, task_type, parameters, device_id):
        """为单个设备创建任务"""
        try:
            delay_like = int(float(self.operation_delay.get()) * 1000)

            task_data = {
                "task_type": task_type,
                "parameters": parameters,
                "target_scope": "single",
                "target_id": device_id,
                "delay_like": delay_like
            }

            result = self.api_client.create_task(task_data)
            if result:
                messagebox.showinfo("创建成功", f"任务创建成功，任务ID: {result.get('id')}")
                self.refresh()
            else:
                messagebox.showerror("创建失败", "任务创建失败")

        except Exception as e:
            messagebox.showerror("错误", f"创建任务失败: {e}")

    def save_template(self):
        """保存任务模板"""
        try:
            template = {
                "task_type": self.task_type.get(),
                "blogger_id": self.blogger_id.get(),
                "like_id": self.like_id.get(),
                "click_delay": self.click_delay.get(),
                "group_interval": self.group_interval.get(),
                "operation_delay": self.operation_delay.get(),
                "target_type": self.target_type.get()
            }

            import os
            settings_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "settings")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)

            template_file = os.path.join(settings_dir, "task_template.json")
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("保存成功", "任务模板已保存")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存模板失败: {e}")

    def load_template(self):
        """加载任务模板"""
        try:
            import os
            settings_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "settings")
            template_file = os.path.join(settings_dir, "task_template.json")

            if not os.path.exists(template_file):
                messagebox.showwarning("文件不存在", "没有找到保存的模板")
                return

            with open(template_file, 'r', encoding='utf-8') as f:
                template = json.load(f)

            # 加载模板数据
            self.task_type.set(template.get("task_type", "点赞任务"))
            self.blogger_id.set(template.get("blogger_id", ""))
            self.like_id.set(template.get("like_id", ""))
            self.click_delay.set(template.get("click_delay", "0.5"))
            self.group_interval.set(template.get("group_interval", "2"))
            self.operation_delay.set(template.get("operation_delay", "1"))
            self.target_type.set(template.get("target_type", "group"))

            self.on_target_type_changed()
            self.update_task_hint()  # 更新提示
            messagebox.showinfo("加载成功", "任务模板已加载")

        except Exception as e:
            messagebox.showerror("加载失败", f"加载模板失败: {e}")

    def refresh(self):
        """刷新任务列表"""
        def fetch_data():
            try:
                # 获取任务列表
                tasks = self.api_client.get_tasks()

                # 获取任务设备统计
                import requests
                try:
                    response = requests.get(f"{self.api_client.base_url}/tasks/stats/device-summary", timeout=10)
                    if response.status_code == 200:
                        device_stats = response.json()
                        print(f"✅ 获取设备统计成功，共{len(device_stats)}个任务的统计数据")
                    else:
                        device_stats = {}
                        print(f"❌ 获取设备统计失败，状态码: {response.status_code}")
                except Exception as e:
                    device_stats = {}
                    print(f"❌ 获取设备统计异常: {e}")

                self.winfo_toplevel().after(0, lambda: self.update_task_list(tasks, device_stats))
            except Exception as e:
                print(f"获取任务列表失败: {e}")
                self.winfo_toplevel().after(0, lambda: self.update_task_list([], {}))

        threading.Thread(target=fetch_data, daemon=True).start()

    def update_task_list(self, tasks, device_stats=None):
        """更新任务列表显示"""
        # 清空现有数据
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)

        self.tasks_data = tasks or []
        device_stats = device_stats or {}

        # 统计任务状态
        status_counts = {'pending': 0, 'running': 0, 'done': 0, 'failed': 0, 'paused': 0}

        # 添加任务数据
        for task in self.tasks_data:
            # 统计状态
            status = task.get('status', 'pending')
            if status in status_counts:
                status_counts[status] += 1

            # 格式化创建时间 - 直接使用服务器返回的时间
            create_time = task.get('create_time', '')
            if create_time:
                # 服务器返回格式：2025-06-09T11:27:02
                # 转换为显示格式：2025-06-09 11:27
                try:
                    from datetime import datetime
                    # 处理ISO格式时间
                    if 'T' in create_time:
                        dt = datetime.fromisoformat(create_time.replace('Z', ''))
                    else:
                        dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    create_time = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    # 如果解析失败，使用原始格式
                    create_time = str(create_time)

            # 获取参数中的博主ID和点赞ID
            parameters = task.get('parameters', {})
            blogger_id = parameters.get('blogger_id', parameters.get('user_id', ''))
            like_id = parameters.get('like_id', '')

            # 格式化显示
            task_type_display = Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))
            status_display = Config.TASK_STATUS.get(task.get('status', ''), task.get('status', ''))
            target_display = Config.TARGET_SCOPE.get(task.get('target_scope', ''), task.get('target_scope', ''))

            # 状态图标
            status_icon = self.get_status_icon(status)

            # 获取设备统计信息
            task_id = task.get('id')
            task_device_stats = device_stats.get(str(task_id), {})

            # 格式化设备统计显示
            total_devices = task_device_stats.get('total', 0)
            success_devices = task_device_stats.get('done', 0)
            failed_devices = task_device_stats.get('failed', 0)
            running_devices = task_device_stats.get('running', 0)
            pending_devices = task_device_stats.get('pending', 0)

            if total_devices > 0:
                device_stats_display = f"✅{success_devices} ❌{failed_devices}"
                if running_devices > 0:
                    device_stats_display += f" 🔄{running_devices}"
                if pending_devices > 0:
                    device_stats_display += f" ⏳{pending_devices}"
                device_stats_display += f" (共{total_devices}台)"

                # 调试信息：显示前几个任务的统计
                if task_id <= 5:
                    print(f"📊 任务{task_id}设备统计: {device_stats_display}")
            else:
                device_stats_display = "无设备"

            values = (
                task.get('id', ''),
                task_type_display,
                f"{status_icon} {status_display}",
                blogger_id,
                like_id,
                device_stats_display,
                target_display,
                create_time
            )

            self.task_tree.insert('', tk.END, values=values)

        # 更新统计信息
        total_tasks = len(self.tasks_data)
        running_tasks = status_counts['running']
        completed_tasks = status_counts['done']
        failed_tasks = status_counts['failed']
        pending_tasks = status_counts['pending']

        # 更新各个统计标签
        self.total_stats_label.configure(text=f"总任务: {total_tasks}")
        self.success_stats_label.configure(text=f"成功: {completed_tasks}")
        self.failed_stats_label.configure(text=f"失败: {failed_tasks}")
        self.running_stats_label.configure(text=f"运行中: {running_tasks}")

        # 如果有等待中的任务，也显示
        if pending_tasks > 0:
            self.running_stats_label.configure(text=f"运行中: {running_tasks} | 等待中: {pending_tasks}")

    def get_status_icon(self, status):
        """获取状态图标"""
        icons = {
            'pending': '⏳',
            'running': '🔄',
            'done': '✅',
            'failed': '❌',
            'paused': '⏸️',
            'cancelled': '🚫'
        }
        return icons.get(status, '❓')

    def calculate_progress(self, task):
        """计算任务进度"""
        status = task.get('status', 'pending')
        if status == 'done':
            return "100%"
        elif status == 'running':
            return "执行中"
        elif status == 'failed':
            return "失败"
        elif status == 'paused':
            return "暂停"
        else:
            return "等待中"

    def get_selected_task(self):
        """获取选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            return None

        item = selection[0]
        values = self.task_tree.item(item, 'values')
        if not values:
            return None

        task_id = values[0]
        for task in self.tasks_data:
            if str(task.get('id')) == str(task_id):
                return task
        return None

    def delete_selected_task(self):
        """删除选中的任务"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择", "请先选择要删除的任务")
            return

        if messagebox.askyesno("确认删除", f"确定要删除任务 {task['id']} 吗？"):
            try:
                result = self.api_client.delete_task(task['id'])
                if result:
                    messagebox.showinfo("删除成功", "任务已删除")
                    self.refresh()
                else:
                    messagebox.showerror("删除失败", "删除任务失败")
            except Exception as e:
                messagebox.showerror("删除异常", f"删除任务时发生错误: {e}")

    def pause_selected_task(self):
        """暂停选中的任务"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择", "请先选择要暂停的任务")
            return

        try:
            result = self.api_client.pause_task(task['id'])
            if result:
                messagebox.showinfo("暂停成功", "任务已暂停")
                self.refresh()
            else:
                messagebox.showerror("暂停失败", "暂停任务失败")
        except Exception as e:
            messagebox.showerror("暂停异常", f"暂停任务时发生错误: {e}")

    def resume_selected_task(self):
        """恢复选中的任务"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择", "请先选择要恢复的任务")
            return

        try:
            result = self.api_client.resume_task(task['id'])
            if result:
                messagebox.showinfo("恢复成功", "任务已恢复")
                self.refresh()
            else:
                messagebox.showerror("恢复失败", "恢复任务失败")
        except Exception as e:
            messagebox.showerror("恢复异常", f"恢复任务时发生错误: {e}")

    def on_task_selected(self, event):
        """任务选择事件"""
        pass

    def on_task_double_click(self, event):
        """任务双击事件"""
        self.show_task_details()

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_task_details(self):
        """显示任务详情"""
        task = self.get_selected_task()
        if not task:
            messagebox.showwarning("未选择", "请先选择一个任务")
            return

        # 构建详情信息
        details = f"任务ID: {task['id']}\n"
        details += f"类型: {Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))}\n"
        details += f"状态: {Config.TASK_STATUS.get(task.get('status', ''), task.get('status', ''))}\n"
        details += f"目标: {Config.TARGET_SCOPE.get(task.get('target_scope', ''), task.get('target_scope', ''))}\n"

        create_time = task.get('create_time', '')
        if create_time:
            # 直接使用服务器返回的时间，格式化为可读格式
            try:
                from datetime import datetime
                if 'T' in create_time:
                    dt = datetime.fromisoformat(create_time.replace('Z', ''))
                else:
                    dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                details += f"创建时间: {formatted_time}\n"
            except:
                details += f"创建时间: {create_time}\n"

        parameters = task.get('parameters', {})
        if parameters:
            details += f"\n参数:\n"
            for key, value in parameters.items():
                details += f"  {key}: {value}\n"

        messagebox.showinfo("任务详情", details)

    def copy_task_id(self):
        """复制任务ID"""
        task = self.get_selected_task()
        if task:
            self.clipboard_clear()
            self.clipboard_append(str(task['id']))
            messagebox.showinfo("复制成功", f"任务ID {task['id']} 已复制到剪贴板")