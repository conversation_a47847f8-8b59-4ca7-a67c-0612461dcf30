"""
API客户端
处理与后端API的所有通信
"""

import requests
import json
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging

from config import Config

class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = Config.REQUEST_TIMEOUT
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        self.logger = logging.getLogger(__name__)
        
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict]:
        """发送HTTP请求的通用方法"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            if response.content:
                return response.json()
            return {}
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败: {method} {url} - {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
            
    def check_connection(self) -> bool:
        """检查API连接状态"""
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
            
    # ==================== 设备管理 API ====================
    
    def get_devices(self) -> List[Dict]:
        """获取所有设备列表"""
        result = self._make_request('GET', '/devices/')
        return result if result else []
        
    def get_device(self, device_id: int) -> Optional[Dict]:
        """获取单个设备信息"""
        return self._make_request('GET', f'/devices/{device_id}')
        
    def create_device(self, device_data: Dict) -> Optional[Dict]:
        """创建新设备"""
        return self._make_request('POST', '/devices/', json=device_data)
        
    def update_device(self, device_id: int, device_data: Dict) -> Optional[Dict]:
        """更新设备信息"""
        return self._make_request('PUT', f'/devices/{device_id}', json=device_data)
        
    def delete_device(self, device_id: int) -> bool:
        """删除设备"""
        result = self._make_request('DELETE', f'/devices/{device_id}')
        return result is not None
        
    def get_online_device_count(self) -> int:
        """获取在线设备数量"""
        devices = self.get_devices()
        if devices:
            return sum(1 for device in devices if device.get('online_status') == 'online')
        return 0
        
    # ==================== 任务管理 API ====================
    
    def get_tasks(self) -> List[Dict]:
        """获取所有任务列表"""
        result = self._make_request('GET', '/tasks/')
        return result if result else []
        
    def get_task(self, task_id: int) -> Optional[Dict]:
        """获取单个任务信息"""
        return self._make_request('GET', f'/tasks/{task_id}')
        
    def create_task(self, task_data: Dict) -> Optional[Dict]:
        """创建新任务"""
        return self._make_request('POST', '/tasks/', json=task_data)
        
    def pause_task(self, task_id: int) -> bool:
        """暂停任务"""
        result = self._make_request('POST', f'/tasks/{task_id}/pause')
        return result is not None
        
    def resume_task(self, task_id: int) -> bool:
        """恢复任务"""
        result = self._make_request('POST', f'/tasks/{task_id}/resume')
        return result is not None
        
    def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        return self._make_request('GET', f'/tasks/{task_id}/status')
        
    def complete_task(self, task_id: int, device_id: int, result: Dict) -> Optional[Dict]:
        """标记任务完成"""
        data = {
            'result': result,
            'device_id': device_id
        }
        return self._make_request('POST', f'/tasks/{task_id}/complete', json=data)
        
    def pause_all_tasks(self) -> bool:
        """暂停所有任务"""
        result = self._make_request('POST', '/tasks/pause-all')
        return result is not None

    def resume_all_tasks(self) -> bool:
        """恢复所有任务"""
        result = self._make_request('POST', '/tasks/resume-all')
        return result is not None
        
    # ==================== 分组管理 API ====================
    
    def get_groups(self) -> List[Dict]:
        """获取所有分组列表"""
        result = self._make_request('GET', '/groups/')
        return result if result else []
        
    def get_group(self, group_id: int) -> Optional[Dict]:
        """获取单个分组信息"""
        return self._make_request('GET', f'/groups/{group_id}')
        
    def create_group(self, group_data: Dict) -> Optional[Dict]:
        """创建新分组"""
        return self._make_request('POST', '/groups/', json=group_data)
        
    def update_group(self, group_id: int, group_data: Dict) -> Optional[Dict]:
        """更新分组信息"""
        return self._make_request('PUT', f'/groups/{group_id}', json=group_data)
        
    def delete_group(self, group_id: int) -> bool:
        """删除分组"""
        result = self._make_request('DELETE', f'/groups/{group_id}')
        return result is not None
        
    def add_device_to_group(self, group_id: int, device_id: int) -> bool:
        """添加设备到分组"""
        data = {'device_id': device_id, 'group_id': group_id}
        result = self._make_request('POST', f'/groups/{group_id}/devices', json=data)
        return result is not None
        
    def remove_device_from_group(self, group_id: int, device_id: int) -> bool:
        """从分组中移除设备"""
        result = self._make_request('DELETE', f'/groups/{group_id}/devices/{device_id}')
        return result is not None
        
    # ==================== WebSocket监控 API ====================
    
    def get_websocket_connections(self) -> List[Dict]:
        """获取WebSocket连接状态"""
        # 这个可能需要通过设备API来获取在线状态
        devices = self.get_devices()
        if not devices:
            return []
            
        connections = []
        for device in devices:
            if device.get('online_status') == 'online':
                connections.append({
                    'device_number': device.get('device_number'),
                    'device_ip': device.get('device_ip'),
                    'last_heartbeat': device.get('last_heartbeat'),
                    'status': 'connected'
                })
                
        return connections
        
    def get_heartbeat_status(self) -> Dict:
        """获取心跳状态统计"""
        devices = self.get_devices()
        if not devices:
            return {'total': 0, 'online': 0, 'offline': 0}
            
        total = len(devices)
        online = sum(1 for device in devices if device.get('online_status') == 'online')
        offline = total - online
        
        return {
            'total': total,
            'online': online,
            'offline': offline,
            'last_update': datetime.now().isoformat()
        }
        
    # ==================== 统计信息 API ====================
    
    def get_dashboard_stats(self) -> Dict:
        """获取仪表板统计信息"""
        try:
            devices = self.get_devices()
            tasks = self.get_tasks()
            groups = self.get_groups()
            
            # 设备统计
            device_stats = {
                'total': len(devices) if devices else 0,
                'online': sum(1 for d in devices if d.get('online_status') == 'online') if devices else 0,
                'offline': sum(1 for d in devices if d.get('online_status') == 'offline') if devices else 0
            }
            
            # 任务统计
            task_stats = {
                'total': len(tasks) if tasks else 0,
                'pending': sum(1 for t in tasks if t.get('status') == 'pending') if tasks else 0,
                'running': sum(1 for t in tasks if t.get('status') == 'running') if tasks else 0,
                'done': sum(1 for t in tasks if t.get('status') == 'done') if tasks else 0,
                'failed': sum(1 for t in tasks if t.get('status') == 'failed') if tasks else 0
            }
            
            # 分组统计
            group_stats = {
                'total': len(groups) if groups else 0
            }
            
            return {
                'devices': device_stats,
                'tasks': task_stats,
                'groups': group_stats,
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {
                'devices': {'total': 0, 'online': 0, 'offline': 0},
                'tasks': {'total': 0, 'pending': 0, 'running': 0, 'done': 0, 'failed': 0},
                'groups': {'total': 0},
                'last_update': datetime.now().isoformat()
            }
