#!/usr/bin/env python3
"""
修复数据库中的枚举值问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app.models import Task
from sqlalchemy import text

def fix_invalid_task_types():
    """修复无效的任务类型"""
    print("修复数据库中的无效任务类型...")
    
    try:
        db = next(get_db())
        
        # 查找所有无效的任务类型
        print("1. 查找无效的任务类型...")
        result = db.execute(text("SELECT id, task_type FROM tasks WHERE task_type NOT IN ('sign', 'like', 'page_sign')"))
        invalid_tasks = result.fetchall()
        
        if not invalid_tasks:
            print("✅ 没有发现无效的任务类型")
            return True
            
        print(f"发现 {len(invalid_tasks)} 个无效的任务:")
        for task_id, task_type in invalid_tasks:
            print(f"  任务ID {task_id}: '{task_type}'")
            
        # 修复无效的任务类型
        print("\n2. 修复无效的任务类型...")
        for task_id, task_type in invalid_tasks:
            # 根据任务类型猜测正确的值
            if 'sign' in task_type.lower():
                new_type = 'sign'
            elif 'like' in task_type.lower():
                new_type = 'like'
            elif 'page' in task_type.lower():
                new_type = 'page_sign'
            else:
                # 默认设为sign
                new_type = 'sign'
                
            print(f"  修复任务ID {task_id}: '{task_type}' -> '{new_type}'")
            db.execute(text("UPDATE tasks SET task_type = :new_type WHERE id = :task_id"), 
                      {"new_type": new_type, "task_id": task_id})
                      
        db.commit()
        print("✅ 所有无效任务类型已修复")
        
        # 验证修复结果
        print("\n3. 验证修复结果...")
        result = db.execute(text("SELECT COUNT(*) FROM tasks WHERE task_type NOT IN ('sign', 'like', 'page_sign')"))
        invalid_count = result.scalar()
        
        if invalid_count == 0:
            print("✅ 验证成功，没有无效的任务类型")
        else:
            print(f"❌ 仍有 {invalid_count} 个无效的任务类型")
            
        db.close()
        return invalid_count == 0
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tasks_after_fix():
    """修复后测试任务查询"""
    print("\n测试修复后的任务查询...")
    
    try:
        from app import crud
        db = next(get_db())
        
        tasks = crud.get_tasks(db)
        print(f"✅ 成功获取 {len(tasks)} 个任务")
        
        # 显示前几个任务
        for i, task in enumerate(tasks[:3]):
            print(f"任务 {i+1}: ID={task.id}, 类型={task.task_type}, 状态={task.status}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复数据库枚举值问题...")
    print("="*50)
    
    # 修复无效的任务类型
    if fix_invalid_task_types():
        # 测试修复结果
        test_tasks_after_fix()
        
        print("\n" + "="*50)
        print("🎉 修复完成！现在可以重新测试API端点了")
        print("运行: python test_api_endpoints.py")
    else:
        print("\n❌ 修复失败，请检查数据库连接和权限")

if __name__ == "__main__":
    main()
