#!/usr/bin/env python3
"""
测试不同格式的心跳消息
验证服务端是否能正确识别各种心跳格式
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class HeartbeatFormatTester:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} 连接成功")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def test_standard_json_heartbeat(self):
        """测试标准JSON格式心跳"""
        print("\n📤 测试标准JSON格式心跳...")
        
        heartbeat = {
            "type": "heartbeat",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "device_number": self.device_number,
            "status": {
                "battery": 85,
                "signal": "strong"
            }
        }
        
        try:
            await self.websocket.send(json.dumps(heartbeat))
            print(f"✅ 发送标准JSON心跳: {heartbeat}")
            
            # 接收响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
            response_data = json.loads(response)
            print(f"📨 收到响应: {response_data}")
            
            return response_data.get("type") == "heartbeat_ack"
            
        except Exception as e:
            print(f"❌ 标准JSON心跳测试失败: {e}")
            return False
            
    async def test_iso_timestamp_heartbeat(self):
        """测试ISO时间戳格式心跳"""
        print("\n📤 测试ISO时间戳格式心跳...")
        
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        try:
            await self.websocket.send(timestamp)
            print(f"✅ 发送ISO时间戳心跳: {timestamp}")
            
            # 接收响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
            response_data = json.loads(response)
            print(f"📨 收到响应: {response_data}")
            
            return (response_data.get("type") == "heartbeat_ack" and 
                    response_data.get("received_format") == "iso_timestamp")
            
        except Exception as e:
            print(f"❌ ISO时间戳心跳测试失败: {e}")
            return False
            
    async def test_date_string_heartbeat(self):
        """测试Date对象字符串格式心跳"""
        print("\n📤 测试Date对象字符串格式心跳...")
        
        # 模拟客户端发送的Date对象字符串
        date_string = f"new Date-{datetime.now().strftime('%a %b %d %Y %H:%M:%S')} GMT+0800 (GMT+08:00)"
        
        try:
            await self.websocket.send(date_string)
            print(f"✅ 发送Date字符串心跳: {date_string}")
            
            # 接收响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
            response_data = json.loads(response)
            print(f"📨 收到响应: {response_data}")
            
            return (response_data.get("type") == "heartbeat_ack" and 
                    response_data.get("received_format") == "date_string")
            
        except Exception as e:
            print(f"❌ Date字符串心跳测试失败: {e}")
            return False
            
    async def test_numeric_timestamp_heartbeat(self):
        """测试数字时间戳格式心跳"""
        print("\n📤 测试数字时间戳格式心跳...")
        
        timestamp = str(int(time.time() * 1000))  # 13位毫秒时间戳
        
        try:
            await self.websocket.send(timestamp)
            print(f"✅ 发送数字时间戳心跳: {timestamp}")
            
            # 接收响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
            response_data = json.loads(response)
            print(f"📨 收到响应: {response_data}")
            
            return (response_data.get("type") == "heartbeat_ack" and 
                    response_data.get("received_format") == "numeric_timestamp")
            
        except Exception as e:
            print(f"❌ 数字时间戳心跳测试失败: {e}")
            return False
            
    async def test_invalid_heartbeat(self):
        """测试无效心跳格式"""
        print("\n📤 测试无效心跳格式...")
        
        invalid_messages = [
            "invalid heartbeat",
            '{"type": "not_heartbeat"}',
            "12345",  # 太短的数字
            ""
        ]
        
        success_count = 0
        
        for msg in invalid_messages:
            try:
                await self.websocket.send(msg)
                print(f"📤 发送无效消息: {msg}")
                
                # 接收响应
                response = await asyncio.wait_for(self.websocket.recv(), timeout=3)
                response_data = json.loads(response)
                print(f"📨 收到错误响应: {response_data}")
                
                # 检查是否正确返回错误
                if response_data.get("type") == "error":
                    success_count += 1
                    
            except asyncio.TimeoutError:
                print("⏰ 无响应（预期行为）")
                success_count += 1
            except Exception as e:
                print(f"❌ 处理无效消息异常: {e}")
                
        return success_count == len(invalid_messages)

async def test_heartbeat_formats():
    """测试所有心跳格式"""
    print("🧪 测试心跳格式识别...")
    
    tester = HeartbeatFormatTester("test_heartbeat_device")
    
    try:
        # 连接到服务器
        if not await tester.connect():
            return False
            
        # 运行各种心跳格式测试
        tests = [
            ("标准JSON心跳", tester.test_standard_json_heartbeat),
            ("ISO时间戳心跳", tester.test_iso_timestamp_heartbeat),
            ("Date字符串心跳", tester.test_date_string_heartbeat),
            ("数字时间戳心跳", tester.test_numeric_timestamp_heartbeat),
            ("无效心跳处理", tester.test_invalid_heartbeat)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*40}")
            print(f"🧪 {test_name}")
            print('='*40)
            
            try:
                result = await test_func()
                if result:
                    passed += 1
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                
            await asyncio.sleep(1)  # 等待1秒
            
        print(f"\n📊 心跳格式测试结果: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ 心跳格式测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def test_continuous_heartbeat():
    """测试连续心跳"""
    print("\n🧪 测试连续心跳...")
    
    tester = HeartbeatFormatTester("test_continuous_device")
    
    try:
        if not await tester.connect():
            return False
            
        print("📤 开始连续发送心跳（10次）...")
        
        success_count = 0
        
        for i in range(10):
            # 交替使用不同格式
            if i % 4 == 0:
                # 标准JSON格式
                heartbeat = {
                    "type": "heartbeat",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "sequence": i
                }
                await tester.websocket.send(json.dumps(heartbeat))
                format_type = "JSON"
            elif i % 4 == 1:
                # ISO时间戳格式
                timestamp = datetime.utcnow().isoformat() + "Z"
                await tester.websocket.send(timestamp)
                format_type = "ISO"
            elif i % 4 == 2:
                # Date字符串格式
                date_string = f"new Date-{datetime.now().strftime('%a %b %d %Y %H:%M:%S')} GMT+0800"
                await tester.websocket.send(date_string)
                format_type = "Date"
            else:
                # 数字时间戳格式
                timestamp = str(int(time.time() * 1000))
                await tester.websocket.send(timestamp)
                format_type = "Numeric"
                
            print(f"📤 心跳 #{i+1} ({format_type})")
            
            # 接收响应
            try:
                response = await asyncio.wait_for(tester.websocket.recv(), timeout=3)
                response_data = json.loads(response)
                
                if response_data.get("type") == "heartbeat_ack":
                    success_count += 1
                    print(f"✅ 心跳 #{i+1} 确认")
                else:
                    print(f"❌ 心跳 #{i+1} 响应异常: {response_data}")
                    
            except asyncio.TimeoutError:
                print(f"⏰ 心跳 #{i+1} 响应超时")
            except Exception as e:
                print(f"❌ 心跳 #{i+1} 处理异常: {e}")
                
            await asyncio.sleep(0.5)  # 每0.5秒发送一次
            
        print(f"\n📊 连续心跳测试结果: {success_count}/10 成功")
        return success_count >= 8  # 允许少量失败
        
    except Exception as e:
        print(f"❌ 连续心跳测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def main():
    """主测试函数"""
    print("🚀 心跳格式修复测试开始...\n")
    
    tests = [
        ("心跳格式识别测试", test_heartbeat_formats),
        ("连续心跳测试", test_continuous_heartbeat)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待3秒后继续...")
        await asyncio.sleep(3)
    
    print(f"\n📊 总体测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！心跳格式修复成功")
        print("\n📋 修复要点:")
        print("1. ✅ 支持标准JSON格式心跳")
        print("2. ✅ 兼容ISO时间戳格式心跳")
        print("3. ✅ 兼容Date字符串格式心跳")
        print("4. ✅ 兼容数字时间戳格式心跳")
        print("5. ✅ 正确处理无效心跳格式")
        
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
