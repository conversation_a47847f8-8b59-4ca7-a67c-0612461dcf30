# 分组延迟功能测试指南

## 🎯 问题说明

分组延迟功能用于控制设备间任务执行的时间间隔，避免所有设备同时执行任务。

**修复的问题**：
- 前端发送秒，转换为毫秒
- 后端接收毫秒，再次除以1000
- 导致实际延迟时间变成用户输入的1/1000

**修复后**：
- 前端：用户输入秒 → 转换为毫秒发送
- 后端：接收毫秒 → 除以1000转换为秒使用
- 结果：延迟时间正确

## 🚀 快速测试

### 1. 启动后端服务
```bash
# 使用你的启动命令
chmod +x deploy_db_only.sh
sudo ./deploy_db_only.sh
./start_backend.sh
```

### 2. 运行延迟测试
```bash
# 给测试脚本执行权限
chmod +x test_delay_simple.py

# 运行测试
python3 test_delay_simple.py
```

### 3. 查看日志
在另一个终端窗口查看实时日志：
```bash
# 如果使用systemd服务
journalctl -u wb-system -f

# 如果直接运行
tail -f logs/app.log
```

## 📊 测试内容

测试脚本会：

1. **检查API连接** - 确保后端服务正常
2. **获取设备/分组** - 自动选择测试目标
3. **创建测试任务** - 设置5秒分组延迟，2秒操作延迟
4. **监控执行过程** - 显示任务状态变化
5. **提供日志查看指导** - 告诉你如何查看详细日志

## 🔍 关键日志信息

在后端日志中查找以下信息：

### 延迟应用日志
```
⏰ 应用分组延迟: 设备1 等待 5.0秒 (任务间延迟5.0秒)
⏰ 应用操作延迟: 2.0秒
```

### 任务执行日志
```
🚀 开始处理任务: 任务ID=123, 设备ID=1
✅ 模拟任务 123 执行完成: done, 耗时3.45秒
```

### 设备状态日志
```
⏰ 设备1 首次执行任务, 无需延迟
⏰ 分组延迟已满足: 设备2 距离上次任务 6.2秒 >= 5.0秒
```

## 📋 测试场景

### 场景1：单设备测试
- 如果系统只有设备没有分组
- 测试设备任务间的延迟

### 场景2：分组测试
- 如果系统有分组
- 测试分组内多设备间的延迟

## ✅ 验证标准

### 延迟时间正确
- 用户输入5秒，实际延迟应该是5秒左右
- 不应该是0.005秒（修复前的错误）

### 日志信息完整
- 能看到延迟应用的日志
- 能看到等待时间的计算
- 能看到任务执行的过程

### 任务正常完成
- 任务状态从pending → running → done
- 队列状态正常更新

## 🛠️ 故障排查

### API连接失败
```
❌ API连接失败: Connection refused
```
**解决**：确保后端服务正在运行

### 没有设备/分组
```
❌ 系统中没有设备和分组
```
**解决**：先添加设备或创建分组

### 任务创建失败
```
❌ 任务创建失败，状态码: 500
```
**解决**：检查后端日志，可能是数据库连接问题

### 延迟不生效
**检查**：
1. 后端日志中是否有延迟应用信息
2. 延迟时间是否正确（秒而不是毫秒）
3. 设备是否在线

## 💡 使用建议

### 测试不同延迟时间
修改 `test_delay_simple.py` 中的延迟参数：
```python
# 修改这一行的延迟时间
task = create_delay_test_task(target_scope, target_id, 10.0, 3.0)  # 10秒分组延迟，3秒操作延迟
```

### 测试多个任务
连续运行测试脚本，观察设备间的延迟效果

### 实时监控
同时打开多个终端窗口：
- 一个运行测试脚本
- 一个查看后端日志
- 一个查看系统状态

## 📞 技术支持

如果测试中遇到问题：

1. 检查后端服务是否正常运行
2. 确认数据库连接正常
3. 查看详细的错误日志
4. 验证设备/分组数据是否存在

---

**注意**：测试脚本会创建真实的任务，请在测试环境中运行。
