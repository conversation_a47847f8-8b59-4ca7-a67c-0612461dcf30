#!/usr/bin/env python3
"""
调度器识别测试
验证设备连接后调度器是否能正确识别并分发任务
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class TestDevice:
    """测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                self.received_tasks.append(data)
                task_id = data.get('task_id')
                log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id}")
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                # 忽略ack消息的日志
                pass
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        log_with_time(f"🚀 设备 {self.device_number} 开始执行任务 {task_id}")
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_test_task(task_name, group_id, delay_group_sec=20):
    """创建测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "test123"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),
        "delay_like": 1000
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def check_scheduler_status():
    """检查调度器状态"""
    try:
        import sys
        sys.path.append('.')
        from app.services.optimized_scheduler import optimized_scheduler
        
        log_with_time(f"调度器运行状态: {optimized_scheduler.is_running}")
        log_with_time(f"设备在线缓存: {optimized_scheduler.device_online_cache}")
        log_with_time(f"设备队列数量: {len(optimized_scheduler.device_queues)}")
        
        return optimized_scheduler.is_running
    except Exception as e:
        log_with_time(f"检查调度器状态失败: {e}")
        return False

async def test_scheduler_recognition():
    """测试调度器识别功能"""
    log_with_time("=== 调度器识别测试 ===")
    
    # 创建测试设备
    device = TestDevice("ceshi212")
    
    try:
        # 1. 检查初始调度器状态
        log_with_time("📊 检查初始调度器状态")
        await check_scheduler_status()
        
        # 2. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(3)
        
        # 3. 检查连接后调度器状态
        log_with_time("📊 检查连接后调度器状态")
        await check_scheduler_status()
        
        # 4. 创建测试任务
        log_with_time("🚀 创建测试任务")
        task = create_test_task("调度器识别测试", 5, 20)
        if not task:
            log_with_time("❌ 任务创建失败")
            return
        
        # 5. 等待任务分发
        log_with_time("⏳ 等待任务分发...")
        await asyncio.sleep(10)
        
        # 6. 检查结果
        if device.received_tasks:
            log_with_time(f"✅ 设备收到 {len(device.received_tasks)} 个任务")
            for task_data in device.received_tasks:
                log_with_time(f"   任务ID: {task_data.get('task_id')}")
        else:
            log_with_time("❌ 设备没有收到任何任务")
            log_with_time("可能的原因:")
            log_with_time("   1. 调度器没有识别到设备在线")
            log_with_time("   2. 任务分发逻辑有问题")
            log_with_time("   3. 设备分组配置错误")
        
        # 7. 最终调度器状态检查
        log_with_time("📊 最终调度器状态")
        await check_scheduler_status()
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 调度器识别测试")
    print("💡 验证设备连接后调度器是否能正确识别并分发任务")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 测试调度器识别功能
    await test_scheduler_recognition()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果调度器正常工作:")
    print("   1. 设备连接后应该被调度器识别")
    print("   2. 创建任务后应该立即分发到设备")
    print("   3. 设备应该能收到并执行任务")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
