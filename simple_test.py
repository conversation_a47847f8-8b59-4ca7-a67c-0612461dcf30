#!/usr/bin/env python3
"""
简单测试脚本
"""

import requests
import json

def test_api_connection():
    """测试API连接"""
    print("🔗 测试API连接...")
    
    try:
        # 测试基本连接
        response = requests.get("http://localhost:8000/ip", timeout=10)
        if response.status_code == 200:
            print("✅ API服务器连接正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ API连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API连接异常: {e}")
        return False
        
    return True

def test_devices_api():
    """测试设备API"""
    print("\n📱 测试设备API...")
    
    try:
        response = requests.get("http://localhost:8000/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ 获取设备成功，共 {len(devices)} 个设备")
            
            # 显示前3个设备
            for i, device in enumerate(devices[:3]):
                print(f"   设备{i+1}: {device.get('device_number')} - {device.get('online_status')}")
                
            return devices
        else:
            print(f"❌ 获取设备失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 设备API异常: {e}")
        return None

def test_groups_api():
    """测试分组API"""
    print("\n👥 测试分组API...")
    
    try:
        response = requests.get("http://localhost:8000/groups/", timeout=10)
        if response.status_code == 200:
            groups = response.json()
            print(f"✅ 获取分组成功，共 {len(groups)} 个分组")
            
            # 显示所有分组
            for i, group in enumerate(groups):
                print(f"   分组{i+1}: {group.get('group_name')} (ID: {group.get('id')})")
                
            return groups
        else:
            print(f"❌ 获取分组失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 分组API异常: {e}")
        return None

def test_device_group_assignment():
    """测试设备分组分配"""
    print("\n🔧 测试设备分组分配...")
    
    try:
        # 获取设备和分组
        devices = test_devices_api()
        groups = test_groups_api()
        
        if not devices or not groups:
            print("❌ 缺少设备或分组数据")
            return False
            
        # 选择第一个设备和第一个分组
        test_device = devices[0]
        test_group = groups[0]
        
        device_id = test_device.get('id')
        group_id = test_group.get('id')
        
        print(f"\n   测试设备: {test_device.get('device_number')} (ID: {device_id})")
        print(f"   测试分组: {test_group.get('group_name')} (ID: {group_id})")
        
        # 添加设备到分组
        data = {'device_id': device_id, 'group_id': group_id}
        response = requests.post(f"http://localhost:8000/groups/{group_id}/devices", json=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ 设备添加到分组成功")
            
            # 验证分组分配
            response = requests.get("http://localhost:8000/devices/", timeout=10)
            if response.status_code == 200:
                updated_devices = response.json()
                updated_device = next((d for d in updated_devices if d.get('id') == device_id), None)
                
                if updated_device:
                    current_group_id = updated_device.get('group_id')
                    print(f"   设备当前分组ID: {current_group_id}")
                    
                    if current_group_id == group_id:
                        print("✅ 数据库验证成功：设备已正确分配到分组")
                        return True
                    else:
                        print("❌ 数据库验证失败：设备分组未更新")
                        return False
                else:
                    print("❌ 找不到更新后的设备")
                    return False
            else:
                print("❌ 无法验证设备更新")
                return False
        else:
            print(f"❌ 设备添加到分组失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 设备分组测试异常: {e}")
        return False

def test_task_creation():
    """测试任务创建"""
    print("\n📋 测试任务创建...")
    
    try:
        # 测试点赞任务（无count参数）
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "test_blogger_123",
                "like_id": "test_like_456"
            },
            "target_scope": "single",
            "target_id": 1,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        response = requests.post("http://localhost:8000/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 点赞任务创建成功")
            print(f"   任务ID: {result.get('id')}")
            print(f"   参数: {result.get('parameters')}")
            
            # 验证参数中没有count
            params = result.get('parameters', {})
            if 'count' not in params:
                print("✅ 确认：点赞任务参数中没有count字段")
            else:
                print("❌ 警告：点赞任务参数中仍有count字段")
                
            return True
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务创建测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 简单功能测试...")
    print("="*50)
    
    tests = [
        ("API连接", test_api_connection),
        ("设备分组分配", test_device_group_assignment),
        ("任务创建", test_task_creation)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("🎉 所有测试通过！")
        print("\n✅ 验证的修复:")
        print("1. 设备分组分配 - 数据库正确更新")
        print("2. 点赞任务创建 - 移除count参数")
        print("3. API连接正常 - 所有接口可用")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
