#!/usr/bin/env python3
"""
测试任务管理和任务查看分离功能
"""

import requests
import json
import os

def test_backend_connection():
    """测试后端连接"""
    print("🔗 测试后端连接...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 测试任务API
        response = requests.get(f"{base_url}/tasks/", timeout=10)
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 任务API正常，返回 {len(tasks)} 个任务")
            
            # 分析任务数据
            if tasks:
                # 统计任务类型
                type_counts = {}
                status_counts = {}
                blogger_ids = set()
                like_ids = set()
                
                for task in tasks:
                    task_type = task.get('task_type', 'unknown')
                    status = task.get('status', 'unknown')
                    parameters = task.get('parameters', {})
                    
                    type_counts[task_type] = type_counts.get(task_type, 0) + 1
                    status_counts[status] = status_counts.get(status, 0) + 1
                    
                    if 'blogger_id' in parameters:
                        blogger_ids.add(parameters['blogger_id'])
                    if 'user_id' in parameters:
                        blogger_ids.add(parameters['user_id'])
                    if 'like_id' in parameters:
                        like_ids.add(parameters['like_id'])
                
                print(f"   📊 任务类型分布: {type_counts}")
                print(f"   📊 任务状态分布: {status_counts}")
                print(f"   👤 不同博主ID数量: {len(blogger_ids)}")
                print(f"   ❤️ 不同点赞ID数量: {len(like_ids)}")
                
                # 显示一些示例数据用于搜索测试
                if blogger_ids:
                    sample_blogger = list(blogger_ids)[0]
                    print(f"   🔍 可搜索的博主ID示例: {sample_blogger}")
                if like_ids:
                    sample_like = list(like_ids)[0]
                    print(f"   🔍 可搜索的点赞ID示例: {sample_like}")
            
            return True
        else:
            print(f"❌ 任务API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 后端连接测试异常: {e}")
        return False

def test_task_manager_features():
    """测试任务管理器功能"""
    print("\n📋 测试任务管理器功能...")
    
    try:
        # 检查任务管理器文件
        task_manager_file = "frontend/widgets/task_manager.py"
        if os.path.exists(task_manager_file):
            with open(task_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查快速创建任务功能
            quick_features = [
                "create_quick_task_creation",
                "🚀 快速创建任务",
                "quick_blogger_id",
                "quick_like_id",
                "quick_delay_click",
                "quick_create_task",
                "load_quick_data",
                "update_quick_groups_display"
            ]
            
            found_features = []
            for feature in quick_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 快速创建任务功能: {len(found_features)}/{len(quick_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(quick_features) * 0.8
        else:
            print("❌ 任务管理器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 任务管理器功能测试异常: {e}")
        return False

def test_task_viewer_features():
    """测试任务查看器功能"""
    print("\n🔍 测试任务查看器功能...")
    
    try:
        # 检查任务查看器文件
        task_viewer_file = "frontend/widgets/task_viewer.py"
        if os.path.exists(task_viewer_file):
            with open(task_viewer_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查搜索功能
            search_features = [
                "create_search_area",
                "🔍 任务搜索",
                "search_blogger_id",
                "search_like_id",
                "search_task_type",
                "search_status",
                "apply_search_filter",
                "on_search_changed",
                "clear_search",
                "blogger_id_filter",
                "like_id_filter"
            ]
            
            found_features = []
            for feature in search_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 搜索功能: {len(found_features)}/{len(search_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(search_features) * 0.8
        else:
            print("❌ 任务查看器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 任务查看器功能测试异常: {e}")
        return False

def test_main_interface_integration():
    """测试主界面集成"""
    print("\n🖥️ 测试主界面集成...")
    
    try:
        # 检查主界面文件
        main_file = "frontend/main.py"
        if os.path.exists(main_file):
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查集成功能
            integration_features = [
                "TaskViewerFrame",
                "task_viewer_frame",
                "🔍 任务查看",
                "self.task_viewer_frame.refresh()"
            ]
            
            found_features = []
            for feature in integration_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 主界面集成: {len(found_features)}/{len(integration_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(integration_features) * 0.8
        else:
            print("❌ 主界面文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 主界面集成测试异常: {e}")
        return False

def test_task_creation_with_new_params():
    """测试带新参数的任务创建"""
    print("\n🚀 测试带新参数的任务创建...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取分组
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code != 200:
            print("❌ 无法获取分组数据")
            return False
            
        groups = response.json()
        if not groups:
            print("⚠️ 没有可用的分组，跳过任务创建测试")
            return True
            
        test_group = groups[0]
        group_id = test_group.get('id')
        group_name = test_group.get('group_name', '测试分组')
        
        # 创建带完整参数的任务
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "test_separation_blogger",
                "like_id": "test_separation_like",
                "delay_click": 500  # 点赞延迟
            },
            "target_scope": "group",
            "target_id": group_id,
            "delay_group": 2000,
            "delay_like": 1000
        }
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            print(f"✅ 任务创建成功")
            print(f"   任务ID: {task_id}")
            print(f"   目标分组: {group_name}")
            print(f"   博主ID: test_separation_blogger")
            print(f"   点赞ID: test_separation_like")
            print(f"   点赞延迟: 500ms")
            
            return True
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务创建测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 任务管理和任务查看分离功能测试")
    print("="*60)
    
    tests = [
        ("后端连接", test_backend_connection),
        ("任务管理器功能", test_task_manager_features),
        ("任务查看器功能", test_task_viewer_features),
        ("主界面集成", test_main_interface_integration),
        ("带新参数任务创建", test_task_creation_with_new_params)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:
        print("🎉 功能分离完成！")
        
        print("\n✅ 功能分离成果:")
        print("1. 📋 任务管理页面")
        print("   • 🚀 快速创建任务功能")
        print("   • 📝 任务类型选择")
        print("   • 👥 多分组选择")
        print("   • ⏱️ 延迟参数设置（分组、操作、点赞）")
        print("   • 🔄 数据刷新功能")
        
        print("\n2. 🔍 任务查看页面")
        print("   • 🔍 博主ID搜索")
        print("   • ❤️ 点赞ID搜索")
        print("   • 📝 任务类型过滤")
        print("   • 📊 任务状态过滤")
        print("   • 📋 详细任务列表")
        print("   • 📊 实时统计信息")
        
        print("\n3. 🖥️ 主界面优化")
        print("   • 📱 设备管理")
        print("   • 📋 任务管理")
        print("   • 🔍 任务查看")
        print("   • 👥 分组管理")
        print("   • 🌐 连接监控")
        
        print("\n💡 使用指南:")
        print("• 📋 任务管理：用于创建和管理任务")
        print("  - 快速创建任务")
        print("  - 设置延迟参数")
        print("  - 选择目标分组")
        
        print("\n• 🔍 任务查看：用于查看和搜索任务")
        print("  - 搜索特定博主的任务")
        print("  - 搜索特定点赞ID的任务")
        print("  - 按类型和状态过滤")
        print("  - 查看任务详细信息")
        
        print("\n🎯 搜索功能:")
        print("• 👤 博主ID搜索 - 支持模糊匹配")
        print("• ❤️ 点赞ID搜索 - 支持模糊匹配")
        print("• 📝 任务类型过滤 - 精确匹配")
        print("• 📊 状态过滤 - 精确匹配")
        print("• 🔄 实时搜索 - 输入即搜索")
        
    else:
        print("⚠️ 部分功能可能未完全实现，请检查相关功能")

if __name__ == "__main__":
    main()
