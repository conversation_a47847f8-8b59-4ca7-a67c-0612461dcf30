#!/usr/bin/env python3
"""
设备管理系统启动脚本
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖项"""
    missing_deps = []

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    try:
        import tkinter.ttk
    except ImportError:
        missing_deps.append("tkinter")

    if missing_deps:
        error_msg = f"缺少必要的依赖项: {', '.join(missing_deps)}\n\n"
        if "requests" in missing_deps:
            error_msg += "请安装: pip install requests\n"
        if "tkinter" in missing_deps:
            error_msg += "请安装tkinter (通常随Python安装)\n"
            error_msg += "Linux: sudo apt install python3-tk\n"
            error_msg += "macOS: brew install python-tk"

        print(error_msg)
        try:
            messagebox.showerror("依赖错误", error_msg)
        except:
            pass  # 如果tkinter不可用，messagebox也会失败
        return False

    return True

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        error_msg = f"需要Python 3.7或更高版本，当前版本: {sys.version}"
        print(error_msg)
        try:
            messagebox.showerror("版本错误", error_msg)
        except:
            pass
        return False
    return True

def main():
    """主函数"""
    print("正在启动设备管理系统...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")

    # 检查Python版本
    if not check_python_version():
        return

    # 检查依赖
    if not check_dependencies():
        return

    try:
        # 导入主应用
        print("导入主应用模块...")
        from main import MainApplication

        # 创建并运行应用
        print("创建应用实例...")
        app = MainApplication()
        print("设备管理系统已启动")
        app.run()

    except ImportError as e:
        error_msg = f"导入模块失败: {e}\n\n请确保所有文件都在正确的位置"
        print(error_msg)
        print(f"详细错误:\n{traceback.format_exc()}")
        try:
            messagebox.showerror("导入失败", error_msg)
        except:
            pass

    except Exception as e:
        error_msg = f"启动应用程序时发生错误: {e}"
        print(error_msg)
        print(f"详细错误:\n{traceback.format_exc()}")
        try:
            messagebox.showerror("启动失败", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
