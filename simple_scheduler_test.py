#!/usr/bin/env python3
"""
简化的调度器测试
"""

import sys
import asyncio
sys.path.append('.')

async def test_scheduler_components():
    """测试调度器各个组件"""
    print("🧪 测试调度器组件...")
    
    try:
        # 1. 测试数据库连接
        print("1. 测试数据库连接...")
        from app.db import get_db
        db = next(get_db())
        db.close()
        print("✅ 数据库连接正常")
        
        # 2. 测试模型导入
        print("2. 测试模型导入...")
        from app.models.device import Device
        from app.models.task import Task, TaskQueue
        print("✅ 模型导入正常")
        
        # 3. 测试调度器导入
        print("3. 测试调度器导入...")
        from app.services.optimized_scheduler import OptimizedTaskScheduler
        print("✅ 调度器类导入正常")
        
        # 4. 创建调度器实例
        print("4. 创建调度器实例...")
        scheduler = OptimizedTaskScheduler()
        print(f"✅ 调度器实例创建成功，运行状态: {scheduler.is_running}")
        
        # 5. 测试设备查询
        print("5. 测试设备查询...")
        db = next(get_db())
        try:
            devices = db.query(Device).all()
            print(f"✅ 查询到 {len(devices)} 个设备")
            for device in devices[:3]:  # 只显示前3个
                print(f"   设备: {device.device_number} (ID: {device.id})")
        finally:
            db.close()
        
        # 6. 手动初始化设备队列
        print("6. 手动初始化设备队列...")
        db = next(get_db())
        try:
            devices = db.query(Device).all()
            for device in devices:
                scheduler.device_queues[device.id] = asyncio.Queue()
                scheduler.device_locks[device.id] = asyncio.Lock()
            print(f"✅ 初始化了 {len(scheduler.device_queues)} 个设备队列")
        finally:
            db.close()
        
        # 7. 测试任务查询
        print("7. 测试pending任务查询...")
        db = next(get_db())
        try:
            pending_queues = db.query(TaskQueue).filter(TaskQueue.status == 'pending').all()
            print(f"✅ 查询到 {len(pending_queues)} 个pending任务队列")
            
            # 显示设备9的任务
            device9_tasks = [tq for tq in pending_queues if tq.device_id == 9]
            print(f"   设备9有 {len(device9_tasks)} 个pending任务")
            for tq in device9_tasks:
                print(f"     任务ID: {tq.task_id}")
        finally:
            db.close()
        
        # 8. 手动将任务190加入设备9队列
        print("8. 手动将任务190加入设备9队列...")
        if 9 in scheduler.device_queues:
            db = next(get_db())
            try:
                task190 = db.query(Task).filter(Task.id == 190).first()
                if task190:
                    await scheduler.device_queues[9].put(task190)
                    queue_size = scheduler.device_queues[9].qsize()
                    print(f"✅ 任务190已加入设备9队列，队列大小: {queue_size}")
                else:
                    print("❌ 任务190不存在")
            finally:
                db.close()
        else:
            print("❌ 设备9队列不存在")
        
        # 9. 启动一个设备处理器测试
        print("9. 启动设备9处理器测试...")
        scheduler.is_running = True
        
        # 创建一个简化的设备处理器
        async def simple_device_processor():
            print("🔄 简化设备处理器启动")
            try:
                if 9 in scheduler.device_queues:
                    queue = scheduler.device_queues[9]
                    if not queue.empty():
                        task = await asyncio.wait_for(queue.get(), timeout=2.0)
                        print(f"📤 获取到任务: {task.id}")
                        
                        # 简单处理
                        print(f"🚀 开始处理任务 {task.id}")
                        await asyncio.sleep(1)  # 模拟处理
                        print(f"✅ 任务 {task.id} 处理完成")
                        
                        return True
                    else:
                        print("📭 队列为空")
                        return False
                else:
                    print("❌ 设备9队列不存在")
                    return False
            except asyncio.TimeoutError:
                print("⏰ 获取任务超时")
                return False
            except Exception as e:
                print(f"❌ 处理器错误: {e}")
                return False
        
        result = await simple_device_processor()
        print(f"📊 处理器测试结果: {'成功' if result else '失败'}")
        
        print("\n✅ 所有组件测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scheduler_components())
