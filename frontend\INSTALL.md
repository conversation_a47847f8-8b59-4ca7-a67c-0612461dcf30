# 设备管理系统前端 - 安装指南

## 系统要求

### Python版本
- Python 3.7 或更高版本
- 推荐使用 Python 3.8+

### 操作系统支持
- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+, CentOS 7+)

## 安装步骤

### 1. 检查Python环境

```bash
# 检查Python版本
python --version
# 或
python3 --version

# 检查pip版本
pip --version
# 或
pip3 --version
```

### 2. 克隆或下载项目

```bash
# 如果使用Git
git clone <repository-url>
cd frontend

# 或直接下载并解压到frontend目录
```

### 3. 安装依赖

```bash
# 方法1: 使用requirements.txt
pip install -r requirements.txt

# 方法2: 手动安装核心依赖
pip install requests>=2.28.0 urllib3>=1.26.0
```

### 4. 验证tkinter安装

```bash
# 测试tkinter是否可用
python -c "import tkinter; print('tkinter可用')"
```

如果提示tkinter不可用，请参考下面的解决方案。

## 平台特定安装说明

### Windows

1. **Python安装**：
   - 从 [python.org](https://www.python.org/downloads/) 下载Python
   - 安装时勾选 "Add Python to PATH"
   - tkinter通常已包含

2. **如果tkinter缺失**：
   - 重新安装Python，确保勾选所有组件
   - 或使用Anaconda: `conda install tk`

### macOS

1. **Python安装**：
   ```bash
   # 使用Homebrew
   brew install python-tk
   
   # 或使用官方安装包
   # 从python.org下载安装
   ```

2. **如果tkinter缺失**：
   ```bash
   # 使用Homebrew重新安装
   brew reinstall python-tk
   ```

### Linux (Ubuntu/Debian)

1. **安装Python和tkinter**：
   ```bash
   # 更新包列表
   sudo apt update
   
   # 安装Python3和tkinter
   sudo apt install python3 python3-pip python3-tk
   
   # 安装依赖
   pip3 install -r requirements.txt
   ```

### Linux (CentOS/RHEL)

1. **安装Python和tkinter**：
   ```bash
   # CentOS 7
   sudo yum install python3 python3-pip tkinter
   
   # CentOS 8/RHEL 8
   sudo dnf install python3 python3-pip python3-tkinter
   
   # 安装依赖
   pip3 install -r requirements.txt
   ```

## 配置设置

### 1. 后端服务器配置

编辑 `config.py` 文件：

```python
# 修改为您的后端服务器地址
API_BASE_URL = "http://your-backend-server:8000"
WEBSOCKET_URL = "ws://your-backend-server:8000"
```

### 2. 界面配置（可选）

```python
# 窗口大小
WINDOW_WIDTH = 1400
WINDOW_HEIGHT = 900

# 刷新间隔
AUTO_REFRESH_INTERVAL = 5  # 秒
CONNECTION_CHECK_INTERVAL = 3  # 秒
```

## 启动应用

### 方法1: 使用启动脚本（推荐）

```bash
python run.py
```

### 方法2: 直接运行主程序

```bash
python main.py
```

### 方法3: 作为模块运行

```bash
python -m frontend.main
```

## 故障排除

### 常见问题及解决方案

#### 1. ModuleNotFoundError: No module named 'tkinter'

**Windows解决方案**：
```bash
# 重新安装Python，确保包含tkinter
# 或使用Anaconda
conda install tk
```

**Linux解决方案**：
```bash
# Ubuntu/Debian
sudo apt install python3-tk

# CentOS/RHEL
sudo yum install tkinter  # CentOS 7
sudo dnf install python3-tkinter  # CentOS 8+
```

**macOS解决方案**：
```bash
# 使用Homebrew
brew install python-tk
```

#### 2. requests模块缺失

```bash
pip install requests
```

#### 3. 权限错误

```bash
# 使用用户安装
pip install --user -r requirements.txt

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

#### 4. 无法连接后端

1. 检查后端服务是否启动
2. 确认防火墙设置
3. 验证API地址配置
4. 检查网络连接

#### 5. 界面显示异常

1. 检查屏幕分辨率
2. 更新显卡驱动
3. 尝试不同的主题设置

## 虚拟环境安装（推荐）

### 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv device_manager_env

# 激活虚拟环境
# Linux/macOS:
source device_manager_env/bin/activate

# Windows:
device_manager_env\Scripts\activate
```

### 在虚拟环境中安装

```bash
# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 运行应用
python run.py
```

### 退出虚拟环境

```bash
deactivate
```

## 开发环境设置

如果您要进行开发，建议安装额外的开发工具：

```bash
# 安装开发依赖
pip install pytest pytest-mock black flake8

# 代码格式化
black *.py widgets/*.py utils/*.py

# 代码检查
flake8 *.py widgets/*.py utils/*.py

# 运行测试
pytest
```

## 性能优化

### 1. 减少内存使用

- 调整 `MAX_LOG_LINES` 配置
- 减少自动刷新频率

### 2. 提高响应速度

- 使用更快的网络连接
- 调整 `REQUEST_TIMEOUT` 设置

## 卸载

```bash
# 卸载依赖包
pip uninstall -r requirements.txt

# 删除项目文件夹
rm -rf frontend/  # Linux/macOS
# 或手动删除文件夹 (Windows)
```

## 技术支持

如果遇到安装问题：

1. 检查Python版本是否符合要求
2. 确认所有依赖都已正确安装
3. 查看错误日志获取详细信息
4. 参考本文档的故障排除部分

## 更新说明

### 更新应用

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重启应用
python run.py
```

### 版本兼容性

- v1.0: 支持Python 3.7+
- 后续版本将在README中说明兼容性要求
