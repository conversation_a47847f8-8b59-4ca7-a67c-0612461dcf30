/**
 * 客户端心跳优化脚本
 * 基于服务端日志分析，优化心跳发送机制
 */

// 当前问题分析：
// 1. 客户端发送多种格式的心跳（date_string, iso_timestamp）
// 2. 还在发送不必要的二进制消息
// 3. 心跳频率可能过高

// 优化方案1: 统一心跳格式（推荐）
function optimizedHeartbeat() {
    console.log("启动优化的心跳机制...");
    
    let heartbeatInterval;
    let heartbeatCount = 0;
    
    function sendStandardHeartbeat() {
        if (ws && ws.readyState === WebSocket.OPEN) {
            const heartbeat = {
                type: "heartbeat",
                timestamp: new Date().toISOString(),
                device_number: "devi201", // 替换为实际设备号
                sequence: ++heartbeatCount,
                // 可选：添加设备状态
                status: {
                    battery: getBatteryLevel ? getBatteryLevel() : 100,
                    signal: getSignalStrength ? getSignalStrength() : "strong",
                    memory: getMemoryUsage ? getMemoryUsage() : 50
                }
            };
            
            try {
                ws.send(JSON.stringify(heartbeat));
                console.log(`心跳发送成功 #${heartbeatCount}:`, heartbeat.timestamp);
            } catch (error) {
                console.error("心跳发送失败:", error);
            }
        }
    }
    
    // 启动心跳
    heartbeatInterval = setInterval(sendStandardHeartbeat, 3000);
    
    // 返回停止函数
    return function stopHeartbeat() {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            console.log("心跳已停止");
        }
    };
}

// 优化方案2: 改进现有的startHeartBeat函数
function improveExistingHeartbeat() {
    console.log("改进现有心跳机制...");
    
    // 如果您的框架有startHeartBeat函数，可以这样改进：
    if (typeof ws.startHeartBeat === 'function') {
        ws.startHeartBeat(
            function() {
                // 不发送额外的二进制数据
                return null;
            },
            function() {
                // 发送标准JSON格式心跳
                return JSON.stringify({
                    type: "heartbeat",
                    timestamp: new Date().toISOString(),
                    device_number: "devi201"
                });
            },
            3000, // 3秒间隔
            false  // 不发送二进制数据
        );
    }
}

// 优化方案3: 智能心跳（根据网络状况调整）
function smartHeartbeat() {
    console.log("启动智能心跳机制...");
    
    let heartbeatInterval = 3000; // 默认3秒
    let missedAcks = 0;
    let lastAckTime = Date.now();
    let heartbeatTimer;
    
    function sendSmartHeartbeat() {
        if (ws && ws.readyState === WebSocket.OPEN) {
            const heartbeat = {
                type: "heartbeat",
                timestamp: new Date().toISOString(),
                device_number: "devi201",
                interval: heartbeatInterval,
                missed_acks: missedAcks
            };
            
            ws.send(JSON.stringify(heartbeat));
            
            // 检查心跳确认超时
            setTimeout(() => {
                const now = Date.now();
                if (now - lastAckTime > heartbeatInterval + 2000) {
                    missedAcks++;
                    console.warn(`心跳确认超时，连续未确认: ${missedAcks}`);
                    
                    // 调整心跳频率
                    if (missedAcks > 2) {
                        heartbeatInterval = Math.min(heartbeatInterval * 1.5, 10000);
                        console.log(`调整心跳间隔为: ${heartbeatInterval}ms`);
                    }
                }
            }, heartbeatInterval + 2000);
        }
        
        // 重新设置定时器
        heartbeatTimer = setTimeout(sendSmartHeartbeat, heartbeatInterval);
    }
    
    // 处理心跳确认
    const originalOnMessage = ws.onmessage;
    ws.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type === "heartbeat_ack") {
                lastAckTime = Date.now();
                missedAcks = 0;
                
                // 网络良好，可以恢复正常频率
                if (heartbeatInterval > 3000) {
                    heartbeatInterval = Math.max(heartbeatInterval * 0.9, 3000);
                    console.log(`恢复心跳间隔为: ${heartbeatInterval}ms`);
                }
                
                console.log("收到心跳确认:", data.received_format);
            }
        } catch (e) {
            // 忽略JSON解析错误
        }
        
        // 调用原始消息处理函数
        if (originalOnMessage) {
            originalOnMessage.call(this, event);
        }
    };
    
    // 启动智能心跳
    sendSmartHeartbeat();
    
    return function stopSmartHeartbeat() {
        if (heartbeatTimer) {
            clearTimeout(heartbeatTimer);
            console.log("智能心跳已停止");
        }
    };
}

// 消息处理优化
function optimizeMessageHandling() {
    console.log("优化消息处理...");
    
    // 处理服务器响应
    ws.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            
            switch(data.type) {
                case "heartbeat_ack":
                    console.log("心跳确认:", {
                        timestamp: data.timestamp,
                        format: data.received_format,
                        latency: data.latency_ms
                    });
                    break;
                    
                case "error":
                    console.warn("服务器错误:", data.message);
                    
                    // 根据错误类型调整行为
                    switch(data.message) {
                        case "binary_not_supported":
                            console.log("停止发送二进制数据");
                            // 这里可以调整客户端行为
                            break;
                        case "invalid_json":
                            console.log("JSON格式错误，检查消息格式");
                            break;
                    }
                    break;
                    
                case "task":
                    console.log("收到任务:", data);
                    // 处理任务逻辑
                    break;
                    
                default:
                    console.log("收到消息:", data);
            }
        } catch (e) {
            console.error("消息解析失败:", e);
            console.log("原始消息:", event.data);
        }
    };
}

// 连接状态监控
function monitorConnection() {
    console.log("启动连接监控...");
    
    ws.onopen = function() {
        console.log("WebSocket连接已建立");
        // 连接成功后启动心跳
        optimizedHeartbeat();
    };
    
    ws.onclose = function(event) {
        console.log("WebSocket连接已关闭:", event.code, event.reason);
        // 这里可以添加重连逻辑
    };
    
    ws.onerror = function(error) {
        console.error("WebSocket错误:", error);
    };
}

// 使用示例
function initOptimizedWebSocket() {
    console.log("初始化优化的WebSocket连接...");
    
    // 方法1: 使用优化的心跳（推荐）
    const stopHeartbeat = optimizedHeartbeat();
    
    // 方法2: 或者改进现有心跳
    // improveExistingHeartbeat();
    
    // 方法3: 或者使用智能心跳
    // const stopSmartHeartbeat = smartHeartbeat();
    
    // 优化消息处理
    optimizeMessageHandling();
    
    // 监控连接状态
    monitorConnection();
    
    // 返回清理函数
    return function cleanup() {
        if (stopHeartbeat) stopHeartbeat();
        console.log("WebSocket优化已清理");
    };
}

// 立即修复方案（最简单）
function quickFix() {
    console.log("应用快速修复...");
    
    // 如果您使用的是现有的心跳机制，只需要修改返回值：
    if (typeof ws.startHeartBeat === 'function') {
        // 停止现有心跳
        if (typeof ws.stopHeartBeat === 'function') {
            ws.stopHeartBeat();
        }
        
        // 启动优化的心跳
        ws.startHeartBeat(
            function() { return null; }, // 不发送额外数据
            function() {
                // 返回标准JSON格式
                return JSON.stringify({
                    type: "heartbeat",
                    timestamp: new Date().toISOString(),
                    device_number: "devi201"
                });
            },
            3000, // 3秒间隔
            false // 不发送二进制数据
        );
        
        console.log("快速修复已应用");
    }
}

// 导出函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        optimizedHeartbeat,
        improveExistingHeartbeat,
        smartHeartbeat,
        optimizeMessageHandling,
        monitorConnection,
        initOptimizedWebSocket,
        quickFix
    };
}

// 自动执行（如果直接引入脚本）
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.WebSocketOptimizer = {
        optimizedHeartbeat,
        improveExistingHeartbeat,
        smartHeartbeat,
        quickFix,
        initOptimizedWebSocket
    };
    
    console.log("WebSocket优化器已加载，使用 WebSocketOptimizer.quickFix() 快速修复");
}
