"""
任务状态同步API路由
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db import get_db
from app.services.task_sync import (
    sync_task_status,
    sync_device_current_task,
    sync_all_task_status,
    sync_all_device_current_task,
    get_group_task_summary,
    get_device_task_queue
)
from typing import List, Dict

router = APIRouter(prefix="/task-sync", tags=["Task Sync"])

@router.post("/sync-task/{task_id}")
def sync_single_task_status(task_id: int, db: Session = Depends(get_db)):
    """同步单个任务状态"""
    success = sync_task_status(db, task_id)
    if success:
        return {"message": f"任务 {task_id} 状态同步成功"}
    else:
        raise HTTPException(status_code=400, detail="任务状态同步失败")

@router.post("/sync-device/{device_id}")
def sync_single_device_current_task(device_id: int, db: Session = Depends(get_db)):
    """同步单个设备当前任务"""
    success = sync_device_current_task(db, device_id)
    if success:
        return {"message": f"设备 {device_id} 当前任务同步成功"}
    else:
        raise HTTPException(status_code=400, detail="设备当前任务同步失败")

@router.post("/sync-all-tasks")
def sync_all_tasks_status(db: Session = Depends(get_db)):
    """同步所有任务状态"""
    result = sync_all_task_status(db)
    return {
        "message": "批量同步任务状态完成",
        "result": result
    }

@router.post("/sync-all-devices")
def sync_all_devices_current_task(db: Session = Depends(get_db)):
    """同步所有设备当前任务"""
    result = sync_all_device_current_task(db)
    return {
        "message": "批量同步设备当前任务完成",
        "result": result
    }

@router.get("/group-summary/{group_id}")
def get_group_summary(group_id: int, db: Session = Depends(get_db)):
    """获取分组任务摘要"""
    summary = get_group_task_summary(db, group_id)
    if summary:
        return summary
    else:
        raise HTTPException(status_code=404, detail="分组不存在或获取摘要失败")

@router.get("/device-queue/{device_id}")
def get_device_queue(device_id: int, limit: int = 10, db: Session = Depends(get_db)):
    """获取设备任务队列"""
    queue = get_device_task_queue(db, device_id, limit)
    return {
        "device_id": device_id,
        "queue": queue,
        "count": len(queue)
    }

@router.get("/dashboard-stats")
def get_dashboard_stats(db: Session = Depends(get_db)):
    """获取仪表板统计信息"""
    try:
        from app.models.task import Task, TaskQueue
        from app.models.device import Device
        from app.models.group import Group
        from sqlalchemy import func
        
        # 任务统计
        task_stats = db.query(
            Task.status,
            func.count(Task.id).label('count')
        ).group_by(Task.status).all()
        
        task_summary = {
            'pending': 0,
            'running': 0,
            'done': 0,
            'failed': 0,
            'paused': 0,
            'cancelled': 0
        }
        
        for status, count in task_stats:
            task_summary[status] = count
            
        # 设备统计
        device_stats = db.query(
            Device.online_status,
            func.count(Device.id).label('count')
        ).group_by(Device.online_status).all()
        
        device_summary = {
            'online': 0,
            'offline': 0
        }
        
        for status, count in device_stats:
            device_summary[status] = count
            
        # 当前执行任务统计
        running_tasks = db.query(func.count(Device.id)).filter(
            Device.current_task_id.isnot(None)
        ).scalar()
        
        # 分组统计
        group_count = db.query(func.count(Group.id)).scalar()
        
        return {
            "tasks": task_summary,
            "devices": device_summary,
            "groups": {"total": group_count},
            "running_tasks": running_tasks,
            "last_update": "now"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {e}")

@router.get("/timeout-stats")
def get_timeout_statistics():
    """获取任务超时统计信息"""
    try:
        from app.services.task_timeout_monitor import get_timeout_stats
        stats = get_timeout_stats()
        return {
            "message": "获取超时统计成功",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取超时统计失败: {e}")

@router.get("/task-device-logs/{task_id}")
def get_task_device_logs(task_id: int, db: Session = Depends(get_db)):
    """获取任务的设备执行日志"""
    try:
        from app.models.device_task import DeviceTask
        from app.models.device import Device
        from app.utils.time_utils import format_beijing_time

        # 查询任务的所有设备执行记录
        device_tasks = db.query(DeviceTask, Device).join(
            Device, DeviceTask.device_id == Device.id
        ).filter(DeviceTask.task_id == task_id).all()

        logs = []
        for device_task, device in device_tasks:
            log_entry = {
                "device_id": device.id,
                "device_number": device.device_number,
                "status": device_task.status,
                "start_time": format_beijing_time(device_task.start_time) if device_task.start_time else None,
                "finish_time": format_beijing_time(device_task.finish_time) if device_task.finish_time else None,
                "result_summary": device_task.result_summary or "",
                "error_message": device_task.error_message or "",
                "is_timeout": device_task.error_message == "Task timeout" if device_task.error_message else False
            }
            logs.append(log_entry)

        return {
            "task_id": task_id,
            "logs": logs,
            "total_devices": len(logs),
            "completed_devices": len([log for log in logs if log["status"] in ["done", "failed"]]),
            "timeout_devices": len([log for log in logs if log["is_timeout"]])
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务设备日志失败: {e}")
