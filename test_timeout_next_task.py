#!/usr/bin/env python3
"""
测试超时后自动启动下一个任务功能
"""

import requests
import json
import time

def test_timeout_next_task_features():
    """测试超时后启动下一个任务的功能"""
    print("🔧 测试超时后启动下一个任务功能...")
    
    try:
        # 检查超时监控器文件
        import os
        timeout_monitor_file = "app/services/task_timeout_monitor.py"
        if os.path.exists(timeout_monitor_file):
            with open(timeout_monitor_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查下一个任务启动功能
            next_task_features = [
                "start_next_task_if_needed",
                "查找同一设备的下一个等待中的任务",
                "启动下一个任务",
                "发现设备.*的下一个待执行任务",
                "成功启动下一个任务",
                "设备.*没有更多待执行的任务"
            ]
            
            found_features = []
            for feature in next_task_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 下一个任务启动功能: {len(found_features)}/{len(next_task_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(next_task_features) * 0.8
        else:
            print("❌ 超时监控器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 下一个任务启动功能测试异常: {e}")
        return False

def test_manual_start_next_task_api():
    """测试手动启动下一个任务API"""
    print("\n🚀 测试手动启动下一个任务API...")
    
    try:
        base_url = "http://localhost:8001"
        
        # 获取设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取设备数据: {response.status_code}")
            return False
            
        devices = response.json()
        if not devices:
            print("⚠️ 没有可用的设备")
            return True
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number', '测试设备')
        
        print(f"📱 使用设备: {device_number} (ID: {device_id})")
        
        # 测试手动启动下一个任务API
        response = requests.post(f"{base_url}/timeout-monitor/start-next-task/{device_id}", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 手动启动下一个任务API正常")
            print(f"   消息: {result.get('message')}")
            print(f"   状态: {result.get('status')}")
            
            if result.get('next_task_id'):
                print(f"   下一个任务ID: {result.get('next_task_id')}")
            
            return True
        else:
            print(f"❌ 手动启动下一个任务API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 手动启动下一个任务API测试异常: {e}")
        return False

def test_create_multiple_tasks_and_monitor():
    """创建多个任务并监控超时处理"""
    print("\n📝 创建多个任务并监控超时处理...")
    
    try:
        base_url = "http://localhost:8001"
        
        # 获取设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取设备数据: {response.status_code}")
            return False
            
        devices = response.json()
        if not devices:
            print("⚠️ 没有可用的设备")
            return True
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number', '测试设备')
        
        print(f"📱 使用设备: {device_number} (ID: {device_id})")
        
        # 创建多个测试任务
        task_ids = []
        for i in range(3):
            task_data = {
                "task_type": "like",
                "parameters": {
                    "blogger_id": f"next_task_test_blogger_{i}",
                    "like_id": f"next_task_test_like_{i}",
                    "delay_click": 500
                },
                "target_scope": "single",
                "target_id": device_id,
                "delay_like": 1000
            }
            
            response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('id')
                task_ids.append(task_id)
                print(f"✅ 创建任务 {i+1}: 任务ID {task_id}")
            else:
                print(f"❌ 创建任务 {i+1} 失败: {response.status_code}")
                return False
        
        print(f"📋 成功创建 {len(task_ids)} 个任务: {task_ids}")
        
        # 监控任务状态
        print("🔍 监控任务状态...")
        for check_round in range(5):
            time.sleep(3)
            
            # 获取运行中任务信息
            response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
            if response.status_code == 200:
                running_info = response.json()
                tasks = running_info.get('tasks', [])
                
                print(f"   第{check_round+1}次检查: 运行中任务 {len(tasks)} 个")
                
                for task in tasks:
                    if task.get('task_id') in task_ids:
                        runtime = task.get('runtime_minutes', 0)
                        remaining = task.get('remaining_minutes', 0)
                        near_timeout = task.get('is_near_timeout', False)
                        
                        status_icon = "⚠️" if near_timeout else "🔄"
                        print(f"     {status_icon} 任务{task.get('task_id')} - 运行{runtime}分钟 - 剩余{remaining:.1f}分钟")
            else:
                print(f"   第{check_round+1}次检查失败: {response.status_code}")
        
        # 检查任务队列状态
        print("\n📊 检查任务队列状态...")
        response = requests.get(f"{base_url}/tasks/", timeout=10)
        if response.status_code == 200:
            all_tasks = response.json()
            
            # 统计我们创建的任务状态
            our_tasks = [task for task in all_tasks if task.get('id') in task_ids]
            status_counts = {}
            
            for task in our_tasks:
                status = task.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print(f"   任务状态分布: {status_counts}")
            
            # 显示任务详情
            for task in our_tasks:
                task_id = task.get('id')
                status = task.get('status')
                parameters = task.get('parameters', {})
                blogger_id = parameters.get('blogger_id', '')
                
                print(f"   任务{task_id}: {status} - {blogger_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多任务监控测试异常: {e}")
        return False

def test_timeout_monitor_integration():
    """测试超时监控集成"""
    print("\n🔗 测试超时监控集成...")
    
    try:
        base_url = "http://localhost:8001"
        
        # 测试调试信息API
        response = requests.get(f"{base_url}/timeout-monitor/debug-info", timeout=10)
        if response.status_code == 200:
            debug_info = response.json()
            print(f"✅ 调试信息API正常")
            
            running_count = debug_info.get('running_count', 0)
            print(f"   当前运行中任务: {running_count}个")
            
            if running_count > 0:
                print("   运行中任务详情:")
                for task in debug_info.get('running_tasks', [])[:3]:
                    runtime = task.get('runtime_minutes', 0)
                    remaining = task.get('remaining_minutes', 0)
                    print(f"     任务{task.get('task_id')} - 运行{runtime}分钟 - 剩余{remaining:.1f}分钟")
            
            # 检查超时统计
            stats = debug_info.get('recent_stats', {})
            timeout_count = stats.get('timeout_count_24h', 0)
            total_tasks = stats.get('total_tasks_24h', 0)
            timeout_rate = stats.get('timeout_rate_24h', 0)
            
            print(f"   24小时统计: 总任务{total_tasks}个, 超时{timeout_count}个, 超时率{timeout_rate}%")
            
            return True
        else:
            print(f"❌ 调试信息API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 超时监控集成测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 超时后自动启动下一个任务功能测试")
    print("="*70)
    
    tests = [
        ("下一个任务启动功能", test_timeout_next_task_features),
        ("手动启动下一个任务API", test_manual_start_next_task_api),
        ("超时监控集成", test_timeout_monitor_integration),
        ("多任务监控", test_create_multiple_tasks_and_monitor)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*70)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:
        print("🎉 超时后自动启动下一个任务功能完成！")
        
        print("\n✅ 新增功能:")
        print("• 🔄 超时任务处理后自动启动下一个任务")
        print("• 🧹 异常任务清理后自动启动下一个任务")
        print("• 🚀 手动启动下一个任务API")
        print("• 📊 设备在线状态检查")
        print("• 📝 详细的任务启动日志")
        
        print("\n💡 工作流程:")
        print("• ⏰ 检测到任务超时")
        print("• ❌ 标记超时任务为失败")
        print("• 🔍 查找同设备的下一个待执行任务")
        print("• 📡 检查设备在线状态")
        print("• 🚀 启动下一个任务（如果设备在线）")
        print("• 📝 记录启动结果")
        
        print("\n🎯 优化特点:")
        print("• 🔄 无缝任务切换 - 超时后立即启动下一个")
        print("• 📡 设备状态检查 - 只对在线设备启动任务")
        print("• 🧹 异常任务清理 - 清理后也会启动下一个")
        print("• 🚀 手动触发支持 - 提供API手动启动")
        print("• 📝 完整日志记录 - 详细记录启动过程")
        print("• ⚡ 高效处理 - 避免任务队列阻塞")
        
        print("\n🔧 API接口:")
        print("• 🚀 POST /timeout-monitor/start-next-task/{device_id}")
        print("  - 手动为指定设备启动下一个任务")
        print("• 🔍 GET /timeout-monitor/debug-info")
        print("  - 获取详细的调试信息")
        print("• 🧹 POST /timeout-monitor/cleanup-stale-tasks")
        print("  - 手动清理异常任务并启动下一个")
        
    else:
        print("⚠️ 部分功能可能未完全实现，请检查相关功能")

if __name__ == "__main__":
    main()
