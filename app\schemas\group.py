from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class GroupBase(BaseModel):
    group_name: str  # 匹配数据库模型的字段名
    description: Optional[str] = None

class GroupCreate(GroupBase):
    pass

class GroupUpdate(BaseModel):
    group_name: Optional[str] = None
    description: Optional[str] = None

class GroupOut(GroupBase):  # 添加GroupOut类
    id: int
    create_time: Optional[datetime] = None

    class Config:
        from_attributes = True

# 保持向后兼容
class Group(GroupOut):
    devices: List[int] = []

class GroupDevice(BaseModel):
    device_id: int
    group_id: Optional[int] = None
