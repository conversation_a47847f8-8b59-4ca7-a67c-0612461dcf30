# 设备管理系统 - Tkinter前端

基于Tkinter开发的设备任务管理系统前端界面，与FastAPI后端配合使用。

## 功能特点

### 📱 设备管理
- 实时设备状态监控（在线/离线）
- 设备信息管理（设备号、IP地址、账号状态）
- 设备心跳监控
- 设备分组管理
- 设备搜索和过滤

### 📋 任务管理
- 创建和管理各类任务（签到、点赞、页面签到）
- 任务状态实时监控
- 任务暂停/恢复控制
- 任务进度跟踪
- 批量任务操作

### 👥 分组管理
- 创建和管理设备分组
- 设备分组分配
- 分组批量任务分发
- 分组统计信息

### 🔗 WebSocket监控
- 实时WebSocket连接状态监控
- 心跳状态监控
- 连接日志记录
- 连接异常告警

## 系统要求

- Python 3.7+
- tkinter（通常随Python安装）
- requests库

## 安装和运行

### 1. 安装依赖
```bash
pip install requests
```

### 2. 配置后端地址
编辑 `config.py` 文件，修改API服务器地址：
```python
API_BASE_URL = "http://localhost:8000"  # 修改为实际的后端地址
```

### 3. 启动应用
```bash
# 方法1: 使用启动脚本
python run.py

# 方法2: 直接运行主程序
python main.py
```

## 项目结构

```
frontend/
├── main.py                 # 主应用程序
├── run.py                  # 启动脚本
├── config.py               # 配置文件
├── README.md               # 说明文档
├── utils/
│   └── api_client.py       # API客户端
└── widgets/
    ├── device_manager.py   # 设备管理界面
    ├── task_manager.py     # 任务管理界面
    ├── group_manager.py    # 分组管理界面
    ├── websocket_monitor.py # WebSocket监控界面
    └── status_bar.py       # 状态栏组件
```

## 界面说明

### 主界面
- **菜单栏**: 文件操作、设置、帮助
- **工具栏**: 快速操作按钮
- **标签页**: 四个主要功能模块
- **状态栏**: 实时状态信息

### 设备管理页面
- **设备列表**: 显示所有设备及其状态
- **搜索过滤**: 按设备号、IP或状态过滤
- **设备详情**: 显示选中设备的详细信息
- **操作按钮**: 编辑、删除、发送测试任务

### 任务管理页面
- **任务列表**: 显示所有任务及其状态
- **任务过滤**: 按状态、类型过滤任务
- **任务详情**: 显示任务参数和执行状态
- **任务控制**: 暂停、恢复、查看状态

### 分组管理页面
- **分组列表**: 显示所有设备分组
- **设备分配**: 管理分组中的设备
- **批量操作**: 为整个分组创建任务

### WebSocket监控页面
- **连接概览**: 连接状态统计
- **连接列表**: 活跃连接详情
- **实时监控**: 自动刷新连接状态
- **连接日志**: 记录连接事件

## 配置说明

### config.py 主要配置项

```python
# API服务器配置
API_BASE_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000"

# 刷新间隔配置（秒）
AUTO_REFRESH_INTERVAL = 5
CONNECTION_CHECK_INTERVAL = 3

# 界面配置
WINDOW_WIDTH = 1400
WINDOW_HEIGHT = 900

# 任务类型配置
TASK_TYPES = {
    "sign": "签到任务",
    "like": "点赞任务", 
    "page_sign": "页面签到任务"
}
```

## API接口说明

前端通过以下API与后端通信：

### 设备管理
- `GET /devices/` - 获取设备列表
- `POST /devices/` - 创建设备
- `PUT /devices/{id}` - 更新设备
- `DELETE /devices/{id}` - 删除设备

### 任务管理
- `GET /tasks/` - 获取任务列表
- `POST /tasks/` - 创建任务
- `POST /tasks/{id}/pause` - 暂停任务
- `POST /tasks/{id}/resume` - 恢复任务

### 分组管理
- `GET /groups/` - 获取分组列表
- `POST /groups/` - 创建分组
- `PUT /groups/{id}` - 更新分组
- `DELETE /groups/{id}` - 删除分组

## 使用说明

### 1. 设备管理
1. 在设备管理页面查看所有设备状态
2. 使用搜索框快速查找设备
3. 双击设备查看详细信息
4. 右键菜单进行设备操作

### 2. 任务创建
1. 切换到任务管理页面
2. 点击"创建任务"按钮
3. 选择任务类型和目标范围
4. 设置任务参数
5. 提交创建任务

### 3. 分组管理
1. 在分组管理页面创建分组
2. 将设备添加到分组
3. 为分组创建批量任务

### 4. 监控连接
1. 在WebSocket监控页面查看连接状态
2. 监控心跳和连接异常
3. 查看连接日志

## 故障排除

### 常见问题

1. **无法连接到后端**
   - 检查后端服务是否启动
   - 确认API地址配置正确
   - 检查网络连接

2. **界面显示异常**
   - 确认Python版本支持tkinter
   - 检查屏幕分辨率设置
   - 重启应用程序

3. **数据不刷新**
   - 检查自动刷新设置
   - 手动点击刷新按钮
   - 检查后端API响应

### 日志查看
- 应用程序会在控制台输出运行日志
- WebSocket监控页面有连接日志
- 状态栏显示实时状态信息

## 开发说明

### 添加新功能
1. 在相应的widget文件中添加界面组件
2. 在api_client.py中添加API调用方法
3. 在config.py中添加相关配置
4. 更新主界面的菜单和工具栏

### 自定义样式
- 修改config.py中的COLORS配置
- 在main.py的setup_styles方法中添加样式
- 使用ttk.Style配置组件外观

## 版本历史

### v1.0 (2025-06-06)
- 初始版本发布
- 实现基本的设备、任务、分组管理功能
- 添加WebSocket连接监控
- 支持实时状态更新

## 技术支持

如有问题或建议，请联系开发团队。
