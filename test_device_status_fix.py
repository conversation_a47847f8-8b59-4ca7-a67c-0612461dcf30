#!/usr/bin/env python3
"""
测试设备状态修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import asyncio
import websockets
import json
from datetime import datetime

def test_device_api():
    """测试设备API"""
    print("📱 测试设备API...")
    
    try:
        response = requests.get("http://localhost:8002/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ API响应正常，返回 {len(devices)} 个设备")
            
            # 统计在线状态
            online_count = sum(1 for d in devices if d.get('online_status') == 'online')
            offline_count = sum(1 for d in devices if d.get('online_status') == 'offline')
            
            print(f"   在线设备: {online_count}")
            print(f"   离线设备: {offline_count}")
            
            # 显示前3个设备的详细信息
            print("\n   设备详情:")
            for device in devices[:3]:
                print(f"     {device.get('device_number')}: {device.get('online_status')} (心跳: {device.get('last_heartbeat')})")
                print(f"       当前任务: {device.get('current_task_type')} (ID: {device.get('current_task_id')})")
                
            return True
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def test_task_sync_api():
    """测试任务同步API"""
    print("\n🔄 测试任务同步API...")
    
    try:
        # 测试仪表板统计
        response = requests.get("http://localhost:8002/task-sync/dashboard-stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 仪表板统计API正常")
            print(f"   任务统计: {stats.get('tasks')}")
            print(f"   设备统计: {stats.get('devices')}")
            print(f"   执行中任务: {stats.get('running_tasks')}")
            
            return True
        else:
            print(f"❌ 仪表板统计API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务同步API测试异常: {e}")
        return False

async def test_websocket_connection():
    """测试WebSocket连接和心跳"""
    print("\n🔗 测试WebSocket连接...")
    
    try:
        uri = "ws://localhost:8002/ws/test_fix_device"
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送心跳
            heartbeat_msg = {
                "type": "heartbeat",
                "timestamp": datetime.utcnow().isoformat(),
                "device_number": "test_fix_device"
            }
            
            await websocket.send(json.dumps(heartbeat_msg))
            print(f"→ 发送心跳: {heartbeat_msg}")
            
            # 等待一下让服务器处理
            await asyncio.sleep(2)
            
            print("✅ WebSocket心跳发送成功")
            return True
            
    except Exception as e:
        print(f"❌ WebSocket测试异常: {e}")
        return False

def test_device_status_after_heartbeat():
    """测试心跳后的设备状态"""
    print("\n💓 测试心跳后设备状态...")
    
    try:
        # 等待一下让心跳处理完成
        import time
        time.sleep(3)
        
        response = requests.get("http://localhost:8002/devices/", timeout=10)
        if response.status_code == 200:
            devices = response.json()
            
            # 查找测试设备
            test_device = None
            for device in devices:
                if device.get('device_number') == 'test_fix_device':
                    test_device = device
                    break
                    
            if test_device:
                print(f"✅ 找到测试设备: {test_device.get('device_number')}")
                print(f"   在线状态: {test_device.get('online_status')}")
                print(f"   最后心跳: {test_device.get('last_heartbeat')}")
                
                if test_device.get('online_status') == 'online':
                    print("✅ 设备状态更新正常")
                    return True
                else:
                    print("⚠️ 设备状态未更新为在线")
                    return False
            else:
                print("⚠️ 未找到测试设备，可能心跳处理有延迟")
                return False
                
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 心跳后状态测试异常: {e}")
        return False

def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n🖥️ 测试前端兼容性...")
    
    try:
        # 检查前端配置
        frontend_config_path = "frontend/config.py"
        if os.path.exists(frontend_config_path):
            with open(frontend_config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
                
            if 'API_BASE_URL' in config_content:
                print("✅ 前端配置文件存在")
                
                # 检查API基础URL
                if '8000' in config_content:
                    print("⚠️ 前端配置指向端口8000，当前后端在8002")
                    print("   建议更新前端配置或重启后端到8000端口")
                else:
                    print("✅ 前端配置正常")
                    
                return True
            else:
                print("⚠️ 前端配置缺少API_BASE_URL")
                return False
        else:
            print("⚠️ 前端配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 前端兼容性测试异常: {e}")
        return False

async def run_websocket_test():
    """运行WebSocket测试"""
    return await test_websocket_connection()

def main():
    """主函数"""
    print("🧪 设备状态修复验证测试")
    print("="*50)
    
    tests = [
        ("设备API", test_device_api),
        ("任务同步API", test_task_sync_api),
        ("前端兼容性", test_frontend_compatibility)
    ]
    
    passed_tests = 0
    
    # 运行同步测试
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    # 运行WebSocket测试
    try:
        websocket_result = asyncio.run(run_websocket_test())
        if websocket_result:
            passed_tests += 1
            print("✅ WebSocket连接 测试通过\n")
        else:
            print("❌ WebSocket连接 测试失败\n")
    except Exception as e:
        print(f"❌ WebSocket连接 测试异常: {e}\n")
    
    # 测试心跳后状态
    try:
        if test_device_status_after_heartbeat():
            passed_tests += 1
            print("✅ 心跳后状态 测试通过\n")
        else:
            print("❌ 心跳后状态 测试失败\n")
    except Exception as e:
        print(f"❌ 心跳后状态 测试异常: {e}\n")
    
    total_tests = len(tests) + 2  # 加上WebSocket和心跳测试
    
    print("="*50)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！设备状态修复成功！")
        
        print("\n✅ 修复总结:")
        print("1. DeviceStatus表添加了last_update字段")
        print("2. WebSocket管理器同时更新Device和DeviceStatus表")
        print("3. 设备API正确返回在线状态")
        print("4. 任务同步API正常工作")
        print("5. WebSocket心跳机制正常")
        
        print("\n💡 使用说明:")
        print("1. 设备连接后会自动显示为在线")
        print("2. 前端设备管理页面会显示正确的在线状态")
        print("3. 当前任务信息会正确显示")
        print("4. 心跳机制确保状态实时更新")
        
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        
        print("\n🔍 故障排除:")
        print("1. 确保后端服务器正在运行")
        print("2. 检查数据库连接")
        print("3. 验证WebSocket端口是否可访问")
        print("4. 查看后端日志中的错误信息")

if __name__ == "__main__":
    main()
