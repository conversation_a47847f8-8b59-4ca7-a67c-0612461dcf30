#!/usr/bin/env python3
"""
测试任务完成流程
模拟客户端正确处理任务并返回完成消息
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TaskCompletionTester:
    def __init__(self, device_number: str, device_id: int, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.device_id = device_id
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        self.received_tasks = []
        self.completed_tasks = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} (ID: {self.device_id}) 连接成功")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.websocket and not self.websocket.closed:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            print(f"📨 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            print(f"📨 收到服务器消息: {data}")
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                await self._handle_task(data)
            elif data.get('type') == 'error':
                print(f"⚠️ 服务器错误: {data.get('message')}")
            else:
                print(f"ℹ️ 其他消息: {data}")
                
        except json.JSONDecodeError:
            print(f"❌ 消息解析失败: {message}")
        except Exception as e:
            print(f"❌ 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """检查是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            'parameters' in data
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data['task_id']
        task_type = task_data['type']
        parameters = task_data['parameters']
        
        print(f"🎯 开始处理任务: ID={task_id}, 类型={task_type}, 参数={parameters}")
        
        self.received_tasks.append(task_data)
        
        try:
            # 模拟任务执行
            result = await self._execute_task(task_type, parameters)
            
            # 发送成功完成消息
            await self._send_task_completion(task_id, 'success', result)
            
        except Exception as e:
            print(f"❌ 任务执行失败: {e}")
            
            # 发送失败完成消息
            await self._send_task_completion(task_id, 'failed', {
                'error': str(e),
                'error_type': type(e).__name__
            })
            
    async def _execute_task(self, task_type, parameters):
        """执行具体任务"""
        print(f"⚙️ 执行 {task_type} 任务...")
        
        if task_type == 'sign':
            return await self._execute_sign_task(parameters)
        elif task_type == 'like':
            return await self._execute_like_task(parameters)
        elif task_type == 'page_sign':
            return await self._execute_page_sign_task(parameters)
        else:
            raise ValueError(f"未知任务类型: {task_type}")
            
    async def _execute_sign_task(self, parameters):
        """执行签到任务"""
        count = parameters.get('count', 1)
        print(f"📝 执行签到任务，次数: {count}")
        
        success_count = 0
        failed_count = 0
        
        for i in range(count):
            # 模拟签到操作
            await asyncio.sleep(0.5)  # 模拟操作时间
            
            # 模拟90%成功率
            if i < count * 0.9:
                success_count += 1
                print(f"✅ 签到 {i+1}/{count} 成功")
            else:
                failed_count += 1
                print(f"❌ 签到 {i+1}/{count} 失败")
                
        return {
            'processed': count,
            'success_count': success_count,
            'failed_count': failed_count,
            'completion_time': datetime.utcnow().isoformat()
        }
        
    async def _execute_like_task(self, parameters):
        """执行点赞任务"""
        count = parameters.get('count', 1)
        blogger_id = parameters.get('blogger_id', 'unknown')
        
        print(f"👍 执行点赞任务，次数: {count}, 博主: {blogger_id}")
        
        success_count = 0
        
        for i in range(count):
            await asyncio.sleep(0.3)  # 模拟点赞操作
            success_count += 1
            print(f"👍 点赞 {i+1}/{count} 完成")
            
        return {
            'processed': count,
            'success_count': success_count,
            'blogger_id': blogger_id,
            'completion_time': datetime.utcnow().isoformat()
        }
        
    async def _execute_page_sign_task(self, parameters):
        """执行页面签到任务"""
        page_url = parameters.get('page_url', 'unknown')
        
        print(f"📄 执行页面签到任务，页面: {page_url}")
        
        # 模拟页面签到
        await asyncio.sleep(2)
        
        return {
            'page_url': page_url,
            'sign_time': datetime.utcnow().isoformat(),
            'status': 'completed'
        }
        
    async def _send_task_completion(self, task_id, status, result):
        """发送任务完成消息"""
        completion_message = {
            'type': 'task_completion',
            'task_id': task_id,
            'device_id': self.device_id,
            'status': status,
            'timestamp': datetime.utcnow().isoformat(),
            'result': result
        }
        
        try:
            await self.websocket.send(json.dumps(completion_message))
            print(f"📤 任务完成消息已发送: 任务{task_id} 状态{status}")
            self.completed_tasks.append(completion_message)
            
        except Exception as e:
            print(f"❌ 发送任务完成消息失败: {e}")
            
    async def send_heartbeat(self):
        """发送心跳消息"""
        heartbeat = {
            'type': 'heartbeat',
            'timestamp': datetime.utcnow().isoformat(),
            'device_number': self.device_number
        }
        
        try:
            await self.websocket.send(json.dumps(heartbeat))
            print(f"💓 心跳已发送")
        except Exception as e:
            print(f"❌ 心跳发送失败: {e}")
            
    def get_statistics(self):
        """获取统计信息"""
        return {
            'received_tasks': len(self.received_tasks),
            'completed_tasks': len(self.completed_tasks),
            'success_rate': len(self.completed_tasks) / len(self.received_tasks) * 100 if self.received_tasks else 0
        }

async def test_task_completion():
    """测试任务完成流程"""
    print("🧪 测试任务完成流程...")
    
    # 使用设备devi201，设备ID为4
    tester = TaskCompletionTester("devi201", 4)
    
    try:
        # 连接到服务器
        if not await tester.connect():
            return False
            
        print("✅ 连接成功，等待任务...")
        
        # 发送一个心跳确保连接正常
        await tester.send_heartbeat()
        
        # 等待任务（或者手动触发任务）
        print("⏳ 等待服务器分发任务...")
        print("💡 提示：您可以通过API创建任务来测试")
        print("   例如：POST /tasks/ 创建一个签到任务")
        
        # 等待60秒接收和处理任务
        await asyncio.sleep(60)
        
        # 显示统计信息
        stats = tester.get_statistics()
        print(f"\n📊 测试统计:")
        print(f"   接收任务数: {stats['received_tasks']}")
        print(f"   完成任务数: {stats['completed_tasks']}")
        print(f"   完成率: {stats['success_rate']:.1f}%")
        
        return stats['received_tasks'] > 0 and stats['completed_tasks'] > 0
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def test_manual_task():
    """手动测试任务处理"""
    print("🧪 手动测试任务处理...")
    
    tester = TaskCompletionTester("test_device", 999)
    
    try:
        if not await tester.connect():
            return False
            
        # 模拟接收到的任务
        mock_tasks = [
            {
                'task_id': 100,
                'type': 'sign',
                'parameters': {'count': 3}
            },
            {
                'task_id': 101,
                'type': 'like',
                'parameters': {'count': 5, 'blogger_id': 'test_blogger'}
            },
            {
                'task_id': 102,
                'type': 'page_sign',
                'parameters': {'page_url': 'https://example.com'}
            }
        ]
        
        for task in mock_tasks:
            print(f"\n🎯 模拟处理任务: {task}")
            await tester._handle_task(task)
            await asyncio.sleep(2)
            
        stats = tester.get_statistics()
        print(f"\n📊 手动测试统计:")
        print(f"   处理任务数: {stats['received_tasks']}")
        print(f"   完成任务数: {stats['completed_tasks']}")
        
        return stats['completed_tasks'] == len(mock_tasks)
        
    except Exception as e:
        print(f"❌ 手动测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def main():
    """主测试函数"""
    print("🚀 任务完成测试开始...\n")
    
    tests = [
        ("手动任务处理测试", test_manual_task),
        ("实际任务完成测试", test_task_completion)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待3秒后继续...")
        await asyncio.sleep(3)
    
    print(f"\n📊 总体测试结果: {passed}/{total} 通过")
    
    if passed > 0:
        print("🎉 任务处理机制测试完成！")
        print("\n📋 客户端实现要点:")
        print("1. ✅ 正确解析任务消息")
        print("2. ✅ 执行相应的任务逻辑")
        print("3. ✅ 发送标准格式的完成消息")
        print("4. ✅ 包含正确的设备ID")
        
        return True
    else:
        print("❌ 测试失败，请检查实现")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
