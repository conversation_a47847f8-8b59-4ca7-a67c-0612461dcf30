# 任务状态更新延迟问题修复总结

## 🎯 问题核心

**客户端返回任务完成结果后，task_queue表中的status没有及时从'running'更新为'done'，导致调度器认为设备还在执行任务，无法分发下一个任务。**

## 🔍 根本原因分析

### 1. **调度器等待逻辑错误**

**修复前的问题代码：**
```python
# scheduler.py 第436-443行
task_queue = db.query(TaskQueue).filter(
    TaskQueue.task_id == task_id,
    TaskQueue.device_id == device_id,
    TaskQueue.status.in_(['done', 'failed'])  # 查询done/failed状态
).first()

if task_queue:
    if task_queue.status == 'completed':  # 但检查completed状态！
        return
```

**问题：** 查询条件和检查条件不匹配，导致永远等待。

### 2. **重复的状态更新冲突**

**调度器中的重复更新：**
```python
# scheduler.py 第407-415行
if success:
    await self._wait_for_task_completion(task.id, device_id)
    
    # 重复更新状态 - 与WebSocket处理器冲突
    db.rollback()
    task_queue.status = 'done'
    task_queue.finish_time = datetime.utcnow()
    db.commit()
```

**问题：** WebSocket已经更新了状态，调度器又重复更新，可能导致竞争条件。

### 3. **状态检查频率过低**

**原等待逻辑：**
```python
await asyncio.sleep(1)  # 每秒检查一次
```

**问题：** 检查间隔太长，导致下一任务分发延迟。

## 🛠️ 修复方案

### 1. **修复等待逻辑**

**修复后的代码：**
```python
async def _wait_for_task_completion(self, task_id: int, device_id: int):
    max_wait_time = 300  # 最大等待5分钟
    check_interval = 0.5  # 每0.5秒检查一次
    
    while elapsed_time < max_wait_time:
        task_queue = db.query(TaskQueue).filter(
            TaskQueue.task_id == task_id,
            TaskQueue.device_id == device_id
        ).first()
        
        if task_queue and task_queue.status in ['done', 'failed']:
            print(f"[任务完成] 任务 {task_id} 状态: {task_queue.status}")
            return
        
        await asyncio.sleep(check_interval)
        elapsed_time += check_interval
```

**改进：**
- ✅ 统一状态检查逻辑
- ✅ 提高检查频率（0.5秒）
- ✅ 添加详细日志
- ✅ 改进超时处理

### 2. **移除重复状态更新**

**修复后的代码：**
```python
if success:
    # 等待任务完成 - WebSocket会处理状态更新
    await self._wait_for_task_completion(task.id, device_id)
    print(f"[调度器] 任务 {task.id} 已完成，继续处理下一个任务")
```

**改进：**
- ✅ 移除调度器中的重复状态更新
- ✅ 让WebSocket处理器负责状态更新
- ✅ 调度器只负责等待和调度

### 3. **优化下一任务触发**

**修复后的代码：**
```python
async def _process_next_task(self, device_number: str, completion_data: dict):
    print(f"[下一任务] 设备 {device_number} 任务完成，检查待处理任务")
    
    from app.services.scheduler import scheduler
    
    if device_id in scheduler.device_queues:
        print(f"[下一任务] 设备 {device_id} 队列存在，调度器将自动处理下一任务")
```

**改进：**
- ✅ 简化下一任务处理逻辑
- ✅ 依赖调度器的自动检查机制
- ✅ 减少复杂的状态管理

## 📊 修复效果

### 修复前的问题：
- ❌ 任务完成后等待5-30秒才能分发下一任务
- ❌ 调度器永远等待'completed'状态（不存在的状态）
- ❌ 重复的状态更新导致竞争条件
- ❌ 状态检查频率低（1秒一次）

### 修复后的改进：
- ✅ 任务完成后0.5-1秒内分发下一任务
- ✅ 正确等待'done'/'failed'状态
- ✅ 单一状态更新路径（WebSocket负责）
- ✅ 高频状态检查（0.5秒一次）

## 🔧 关键修改文件

### 1. `app/services/scheduler.py`
- **修复 `_wait_for_task_completion` 方法**
  - 统一状态检查逻辑
  - 提高检查频率
  - 改进日志和错误处理

- **移除重复状态更新**
  - 删除调度器中的状态更新代码
  - 让WebSocket处理器负责状态更新

### 2. `app/websocket/ws_manager.py`
- **优化 `_process_next_task` 方法**
  - 简化下一任务处理逻辑
  - 依赖调度器自动检查机制

## 🚀 部署建议

### 1. **立即生效**
- 重启应用服务
- 监控任务分发延迟
- 检查日志中的状态更新信息

### 2. **监控指标**
- 任务完成到下一任务分发的时间间隔
- 数据库连接池使用情况
- WebSocket连接稳定性

### 3. **验证方法**
```bash
# 查看任务状态更新日志
grep "任务完成\|下一任务" logs/app.log

# 监控数据库中的任务状态
SELECT task_id, device_id, status, finish_time 
FROM task_queue 
WHERE status IN ('running', 'done') 
ORDER BY finish_time DESC LIMIT 10;
```

## 🎉 预期效果

修复后，系统应该表现为：

1. **客户端发送任务完成消息**
2. **WebSocket立即更新task_queue.status = 'done'**
3. **调度器在0.5秒内检测到状态变化**
4. **调度器立即处理下一个pending任务**
5. **整个流程在1秒内完成**

这将显著提高任务处理效率，消除任务分发延迟问题。
