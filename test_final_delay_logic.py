#!/usr/bin/env python3
"""
最终测试：验证基于任务完成时间的分组延迟
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def create_task(task_name, device_id, delay_group_sec, delay_like_sec=0.5):
    """创建任务"""
    task_data = {
        "task_type": "sign",
        "parameters": {"count": 1, "test_name": task_name},
        "target_scope": "single",
        "target_id": device_id,
        "delay_group": int(delay_group_sec * 1000),  # 转换为毫秒
        "delay_like": int(delay_like_sec * 1000)     # 转换为毫秒
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

def wait_for_task_completion(task_id, task_name, max_wait=30):
    """等待任务完成并返回完成时间"""
    log_with_time(f"⏳ 等待 {task_name} (ID: {task_id}) 完成...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                current_status = status_data.get('status', 'unknown')
                
                if current_status in ['done', 'failed', 'cancelled']:
                    completion_time = time.time()
                    total_time = completion_time - start_time
                    log_with_time(f"🏁 {task_name} 完成，状态: {current_status}, 耗时: {total_time:.1f}s")
                    return completion_time, current_status
                    
        except Exception as e:
            log_with_time(f"⚠️ 检查 {task_name} 状态出错: {e}")
        
        time.sleep(0.5)  # 更频繁地检查
    
    log_with_time(f"⏰ {task_name} 等待超时")
    return None, 'timeout'

def test_completion_based_delay():
    """测试基于完成时间的分组延迟"""
    log_with_time("=== 测试基于完成时间的分组延迟 ===")
    
    # 使用设备9（已知在线）
    device_id = 9
    log_with_time(f"📱 使用设备ID: {device_id}")
    log_with_time(f"⏰ 设置: 分组延迟=3秒, 操作延迟=1秒")
    
    # 记录测试开始时间
    test_start_time = time.time()
    
    # 创建三个连续任务
    tasks = []
    task_names = ["延迟测试任务1", "延迟测试任务2", "延迟测试任务3"]
    
    for i, task_name in enumerate(task_names):
        log_with_time(f"🚀 创建 {task_name}...")
        task = create_task(task_name, device_id, 3.0, 1.0)  # 3秒分组延迟，1秒操作延迟
        if task:
            tasks.append((task, task_name))
        else:
            log_with_time(f"❌ 创建 {task_name} 失败，停止测试")
            return
        
        # 快速连续创建，不等待
        time.sleep(0.1)
    
    log_with_time(f"📋 所有任务已创建，开始监控执行...")
    
    # 监控任务完成时间
    completion_times = []
    for task, task_name in tasks:
        completion_time, status = wait_for_task_completion(task.get('id'), task_name, 60)
        if completion_time:
            completion_times.append((task_name, completion_time, status))
        else:
            log_with_time(f"❌ {task_name} 未能完成")
    
    # 分析结果
    log_with_time("📊 执行结果分析:")
    
    if len(completion_times) >= 2:
        for i in range(len(completion_times) - 1):
            task1_name, time1, status1 = completion_times[i]
            task2_name, time2, status2 = completion_times[i + 1]
            
            delay = time2 - time1
            log_with_time(f"   {task1_name}完成 → {task2_name}完成: {delay:.1f}秒")
            
            # 预期：任务1完成 + 3秒分组延迟 + 1秒操作延迟 = 4秒总间隔
            expected_min = 3.5  # 允许一些误差
            expected_max = 5.0
            
            if expected_min <= delay <= expected_max:
                log_with_time(f"   ✅ 分组延迟正常工作 ({delay:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
            else:
                log_with_time(f"   ⚠️ 分组延迟可能异常 ({delay:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
    
    total_test_time = time.time() - test_start_time
    log_with_time(f"📊 总测试时间: {total_test_time:.1f}秒")
    
    # 预期时间：任务1(1s) + 延迟(3s) + 任务2(1s) + 延迟(3s) + 任务3(1s) = 9秒
    expected_total = 9.0
    if abs(total_test_time - expected_total) <= 3.0:  # 允许3秒误差
        log_with_time(f"✅ 总时间符合预期 (预期约{expected_total}秒)")
    else:
        log_with_time(f"⚠️ 总时间与预期差异较大 (预期约{expected_total}秒)")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 最终分组延迟测试")
    print("💡 验证：任务完成后等待分组延迟时间再执行下一个任务")
    print("=" * 60)
    
    # 测试API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    test_completion_based_delay()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 正确的分组延迟逻辑已实现:")
    print("   1. 任务创建后立即分发")
    print("   2. 设备执行任务（包含操作延迟）")
    print("   3. 任务完成后记录完成时间")
    print("   4. 下一个任务等待分组延迟时间后开始")
    print("   5. 不同设备的任务完全并行")
    print("=" * 60)

if __name__ == "__main__":
    main()
