
// 正确的设备端WebSocket处理示例
const ws = new WebSocket('ws://localhost:8000/ws/test_device');

ws.onopen = function() {
    console.log('WebSocket连接已建立');
    
    // 发送心跳
    setInterval(() => {
        const heartbeat = {
            type: 'heartbeat',
            timestamp: new Date().toISOString(),
            device_number: 'test_device'
        };
        ws.send(JSON.stringify(heartbeat));
    }, 3000);
};

ws.onmessage = function(event) {
    try {
        const data = JSON.parse(event.data);
        console.log('收到消息:', data);
        
        // 检查是否是任务消息
        if (data.task_id && data.type) {
            handleTask(data);
        }
    } catch (error) {
        console.error('解析消息失败:', error);
    }
};

function handleTask(taskData) {
    console.log('处理任务:', taskData);
    
    const taskId = taskData.task_id;
    const taskType = taskData.type;
    const parameters = taskData.parameters;
    
    // 模拟任务处理
    setTimeout(() => {
        // 发送任务完成消息
        const completion = {
            type: 'task_completion',
            task_id: taskId,
            status: 'success',  // 或 'failed'
            result: {
                message: '任务执行成功',
                execution_time: 2.0,
                timestamp: new Date().toISOString()
            }
        };
        
        ws.send(JSON.stringify(completion));
        console.log('任务完成消息已发送:', completion);
    }, 2000);
}
