#!/usr/bin/env python3
"""
测试超时监控优化
"""

import requests
import json
import time

def test_timeout_monitor_optimization():
    """测试超时监控优化"""
    print("🔧 测试超时监控优化...")
    
    try:
        base_url = "http://localhost:8001"
        
        # 测试调试信息API
        response = requests.get(f"{base_url}/timeout-monitor/debug-info", timeout=10)
        if response.status_code == 200:
            debug_info = response.json()
            print(f"✅ 调试信息API正常")
            print(f"   监控状态: {'活跃' if debug_info.get('monitor_active') else '非活跃'}")
            print(f"   超时阈值: {debug_info.get('timeout_threshold_minutes')}分钟")
            print(f"   运行中任务: {debug_info.get('running_count')}个")
            
            # 显示运行中任务详情
            running_tasks = debug_info.get('running_tasks', [])
            if running_tasks:
                print("   运行中任务详情:")
                for task in running_tasks[:5]:  # 只显示前5个
                    runtime = task.get('runtime_minutes', 0)
                    remaining = task.get('remaining_minutes', 0)
                    near_timeout = task.get('is_near_timeout', False)
                    
                    status_icon = "⚠️" if near_timeout else "🔄"
                    print(f"     {status_icon} 任务{task.get('task_id')} - 设备{task.get('device_id')} - "
                          f"运行{runtime}分钟 - 剩余{remaining:.1f}分钟")
            
            # 显示24小时统计
            stats = debug_info.get('recent_stats', {})
            print(f"   24小时统计:")
            print(f"     总任务: {stats.get('total_tasks_24h')}个")
            print(f"     超时任务: {stats.get('timeout_count_24h')}个")
            print(f"     超时率: {stats.get('timeout_rate_24h')}%")
            
        else:
            print(f"❌ 调试信息API失败: {response.status_code}")
            return False
        
        # 测试手动清理API
        print("\n🧹 测试手动清理功能...")
        response = requests.post(f"{base_url}/timeout-monitor/cleanup-stale-tasks", timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 手动清理API正常")
            print(f"   清理结果: {result.get('message')}")
            print(f"   状态: {result.get('status')}")
        else:
            print(f"❌ 手动清理API失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 超时监控优化测试异常: {e}")
        return False

def test_create_and_monitor_task():
    """创建任务并监控其状态"""
    print("\n📝 创建任务并监控状态...")
    
    try:
        base_url = "http://localhost:8001"
        
        # 获取设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取设备数据: {response.status_code}")
            return False
            
        devices = response.json()
        if not devices:
            print("⚠️ 没有可用的设备")
            return True
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number', '测试设备')
        
        print(f"📱 使用设备: {device_number} (ID: {device_id})")
        
        # 创建测试任务
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "timeout_optimization_test",
                "like_id": "timeout_optimization_like",
                "delay_click": 500
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 1000
        }
        
        print("📤 创建测试任务...")
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            print(f"✅ 测试任务创建成功 - 任务ID: {task_id}")
            
            # 监控任务状态变化
            print("🔍 监控任务状态变化...")
            for i in range(3):
                time.sleep(2)
                
                # 获取运行中任务信息
                response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
                if response.status_code == 200:
                    running_info = response.json()
                    tasks = running_info.get('tasks', [])
                    
                    # 查找我们的测试任务
                    test_task = None
                    for task in tasks:
                        if task.get('task_id') == task_id:
                            test_task = task
                            break
                    
                    if test_task:
                        runtime = test_task.get('runtime_minutes', 0)
                        remaining = test_task.get('remaining_minutes', 0)
                        near_timeout = test_task.get('is_near_timeout', False)
                        
                        status_icon = "⚠️" if near_timeout else "🔄"
                        print(f"   {status_icon} 第{i+1}次检查: 运行{runtime}分钟, 剩余{remaining:.1f}分钟")
                    else:
                        print(f"   ℹ️ 第{i+1}次检查: 任务不在运行中列表（可能已完成或未开始）")
                else:
                    print(f"   ❌ 第{i+1}次检查失败: {response.status_code}")
            
            return True
        else:
            print(f"❌ 测试任务创建失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务监控测试异常: {e}")
        return False

def test_optimization_features():
    """测试优化功能"""
    print("\n🚀 测试优化功能...")
    
    try:
        # 检查超时监控器文件
        import os
        timeout_monitor_file = "app/services/task_timeout_monitor.py"
        if os.path.exists(timeout_monitor_file):
            with open(timeout_monitor_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查优化功能
            optimization_features = [
                "cleanup_stale_tasks",
                "异常长时间运行任务",
                "get_beijing_now_naive",
                "runtime_minutes > 60",
                "✅ 任务超时处理完成",
                "状态变更:",
                "详细检查每个运行中的任务"
            ]
            
            found_features = []
            for feature in optimization_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 优化功能: {len(found_features)}/{len(optimization_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(optimization_features) * 0.8
        else:
            print("❌ 超时监控器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 优化功能测试异常: {e}")
        return False

def test_time_zone_consistency():
    """测试时区一致性"""
    print("\n🌍 测试时区一致性...")
    
    try:
        # 检查scheduler文件
        import os
        scheduler_file = "app/services/scheduler.py"
        if os.path.exists(scheduler_file):
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查时区修复
            timezone_features = [
                "get_beijing_now_naive",
                "使用北京时间，与超时监控保持一致"
            ]
            
            found_features = []
            for feature in timezone_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 时区一致性: {len(found_features)}/{len(timezone_features)}")
            for feature in found_features:
                print(f"   ✓ {feature}")
            
            return len(found_features) >= len(timezone_features) * 0.8
        else:
            print("❌ 调度器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 时区一致性测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 超时监控优化测试")
    print("="*60)
    
    tests = [
        ("优化功能", test_optimization_features),
        ("时区一致性", test_time_zone_consistency),
        ("超时监控优化", test_timeout_monitor_optimization),
        ("任务监控", test_create_and_monitor_task)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:
        print("🎉 超时监控优化完成！")
        
        print("\n✅ 优化成果:")
        print("• 🌍 修复时区一致性问题")
        print("• 📊 详细的运行时长监控")
        print("• 🧹 异常任务自动清理")
        print("• 📝 完善的日志记录")
        print("• 🔍 调试信息API")
        print("• 🛠️ 手动清理功能")
        
        print("\n💡 优化特点:")
        print("• ⏰ 精确的5分钟超时控制")
        print("• 🌍 统一使用北京时间")
        print("• 📊 详细的任务运行状态监控")
        print("• 🧹 自动清理超过1小时的异常任务")
        print("• 📝 完整的状态变更日志")
        print("• 🔍 实时的调试信息接口")
        
        print("\n🎯 问题解决:")
        print("• 🌍 时区不一致导致的超时计算错误")
        print("• 📊 缺乏详细的任务运行状态信息")
        print("• 🧹 异常长时间运行任务无法自动清理")
        print("• 📝 超时处理缺乏详细日志")
        print("• 🔍 缺乏调试和监控接口")
        
    else:
        print("⚠️ 部分功能可能未完全实现，请检查相关功能")

if __name__ == "__main__":
    main()
