"""
任务超时监控服务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db import get_db
from app.models.task import Task, TaskQueue
from app.utils.time_utils import get_beijing_now_naive

logger = logging.getLogger(__name__)

class TaskTimeoutMonitor:
    """任务超时监控器"""
    
    def __init__(self, timeout_minutes: int = 5):
        """
        初始化监控器
        
        Args:
            timeout_minutes: 任务超时时间（分钟）
        """
        self.timeout_minutes = timeout_minutes
        self.timeout_delta = timedelta(minutes=timeout_minutes)
        self.is_running = False
        self.check_interval = 30  # 检查间隔（秒）
        
    async def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            logger.warning("任务超时监控已在运行")
            return
            
        self.is_running = True
        logger.info(f"启动任务超时监控，超时时间: {self.timeout_minutes}分钟")

        # 启动时先清理一次异常任务
        await self.cleanup_stale_tasks()

        check_count = 0
        while self.is_running:
            try:
                await self.check_timeout_tasks()

                # 每10次检查（5分钟）执行一次异常任务清理
                check_count += 1
                if check_count >= 10:
                    await self.cleanup_stale_tasks()
                    check_count = 0

                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"任务超时检查异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        logger.info("停止任务超时监控")
    
    async def check_timeout_tasks(self):
        """检查超时任务"""
        try:
            db = next(get_db())

            # 获取当前时间
            current_time = get_beijing_now_naive()
            timeout_threshold = current_time - self.timeout_delta

            # 先查看所有运行中的任务
            running_tasks = db.query(TaskQueue).filter(TaskQueue.status == 'running').all()

            if running_tasks:
                logger.debug(f"当前有 {len(running_tasks)} 个运行中的任务")

                # 详细检查每个运行中的任务
                timeout_tasks = []
                for task in running_tasks:
                    if task.dispatch_time is None:
                        logger.warning(f"任务 {task.task_id} (设备 {task.device_id}) 状态为running但dispatch_time为空")
                        continue

                    runtime_seconds = (current_time - task.dispatch_time).total_seconds()
                    runtime_minutes = runtime_seconds / 60

                    logger.debug(f"任务 {task.task_id} (设备 {task.device_id}) 运行时长: {runtime_minutes:.1f}分钟")

                    if task.dispatch_time <= timeout_threshold:
                        timeout_tasks.append(task)
                        logger.warning(f"发现超时任务: 任务ID={task.task_id}, 设备ID={task.device_id}, 运行时长={runtime_minutes:.1f}分钟")

                # 处理超时任务
                if timeout_tasks:
                    logger.error(f"发现 {len(timeout_tasks)} 个超时任务，开始处理...")

                    for device_task in timeout_tasks:
                        await self.handle_timeout_task(db, device_task)

                    db.commit()
                    logger.info(f"已处理 {len(timeout_tasks)} 个超时任务")
                else:
                    logger.debug(f"所有 {len(running_tasks)} 个运行中任务都在正常时间范围内")
            else:
                logger.debug("当前没有运行中的任务")

        except Exception as e:
            logger.error(f"检查超时任务失败: {e}")
            if 'db' in locals():
                db.rollback()
        finally:
            if 'db' in locals():
                db.close()
    
    async def handle_timeout_task(self, db: Session, task_queue: TaskQueue):
        """处理超时任务"""
        try:
            # 计算任务运行时长
            current_time = get_beijing_now_naive()
            if task_queue.dispatch_time:
                runtime_seconds = (current_time - task_queue.dispatch_time).total_seconds()
                runtime_minutes = runtime_seconds / 60
                runtime_str = f"{runtime_minutes:.1f}分钟"

                # 如果运行时长异常长（超过1小时），可能是时区或其他问题
                if runtime_minutes > 60:
                    logger.error(f"异常长时间运行任务: 任务ID={task_queue.task_id}, 设备ID={task_queue.device_id}, "
                               f"运行时长={runtime_str}, dispatch_time={task_queue.dispatch_time}, current_time={current_time}")
            else:
                runtime_str = "未知"
                logger.error(f"任务dispatch_time为空: 任务ID={task_queue.task_id}, 设备ID={task_queue.device_id}")

            # 记录超时前的状态
            logger.info(f"处理超时任务: 任务ID={task_queue.task_id}, 设备ID={task_queue.device_id}, "
                       f"当前状态={task_queue.status}, 运行时长={runtime_str}")

            # 更新TaskQueue状态为失败
            old_status = task_queue.status
            task_queue.status = 'failed'
            task_queue.finish_time = current_time
            task_queue.result_summary = f"任务超时（运行{runtime_str}，超过{self.timeout_minutes}分钟未完成）"

            # TaskQueue模型没有error_message字段，使用result_summary记录错误信息
            # 在result_summary中包含错误类型信息

            # 同时更新主任务状态（如果所有设备任务都失败了）
            await self.update_main_task_status_if_needed(db, task_queue.task_id)

            # 启动下一个任务（如果有的话）
            await self.start_next_task_if_needed(db, task_queue)

            logger.warning(f"✅ 任务超时处理完成: 任务ID={task_queue.task_id}, 设备ID={task_queue.device_id}, "
                         f"状态变更: {old_status} → failed, 运行时长={runtime_str}")

        except Exception as e:
            logger.error(f"❌ 处理超时任务失败: 任务ID={getattr(task_queue, 'task_id', 'unknown')}, "
                        f"设备ID={getattr(task_queue, 'device_id', 'unknown')}, 错误: {e}")

    async def update_main_task_status_if_needed(self, db: Session, task_id: int):
        """如果需要，更新主任务状态"""
        try:
            # 检查该任务的所有设备任务状态
            all_queues = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).all()

            if not all_queues:
                return

            # 统计各状态的任务数量
            status_counts = {}
            for queue in all_queues:
                status = queue.status
                status_counts[status] = status_counts.get(status, 0) + 1

            # 如果所有任务都完成了（done或failed），更新主任务状态
            pending_count = status_counts.get('pending', 0)
            running_count = status_counts.get('running', 0)

            if pending_count == 0 and running_count == 0:
                # 所有任务都完成了
                main_task = db.query(Task).filter(Task.id == task_id).first()
                if main_task and main_task.status in ['pending', 'running']:
                    done_count = status_counts.get('done', 0)
                    failed_count = status_counts.get('failed', 0)

                    if failed_count > 0 and done_count == 0:
                        # 全部失败
                        main_task.status = 'failed'
                        logger.info(f"主任务全部失败: 任务ID={task_id}")
                    elif done_count > 0 and failed_count == 0:
                        # 全部成功
                        main_task.status = 'done'
                        logger.info(f"主任务全部完成: 任务ID={task_id}")
                    else:
                        # 部分成功部分失败
                        main_task.status = 'done'  # 有成功的就算完成
                        logger.info(f"主任务部分完成: 任务ID={task_id}, 成功={done_count}, 失败={failed_count}")

        except Exception as e:
            logger.error(f"更新主任务状态失败: {e}")

    async def start_next_task_if_needed(self, db: Session, completed_task_queue: TaskQueue):
        """如果需要，启动下一个任务"""
        try:
            device_id = completed_task_queue.device_id

            # 查找同一设备的下一个等待中的任务
            next_task_queue = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.device_id == device_id,
                    TaskQueue.status == 'pending'
                )
            ).order_by(TaskQueue.id).first()

            if next_task_queue:
                logger.info(f"发现设备 {device_id} 的下一个待执行任务: 任务ID={next_task_queue.task_id}")

                # 检查设备是否在线
                from app.websocket.ws_manager import manager as ws_manager
                from app.models.device import Device

                device = db.query(Device).filter(Device.id == device_id).first()
                if device and device.device_number in ws_manager.active_connections:
                    # 设备在线，可以启动下一个任务
                    logger.info(f"设备 {device.device_number} 在线，启动下一个任务: 任务ID={next_task_queue.task_id}")

                    # 更新任务状态为运行中
                    next_task_queue.status = 'running'
                    next_task_queue.dispatch_time = get_beijing_now_naive()

                    # 获取任务详情
                    task = db.query(Task).filter(Task.id == next_task_queue.task_id).first()
                    if task:
                        # 发送任务到设备
                        task_msg = {
                            'task_id': task.id,
                            'type': task.task_type,
                            'parameters': task.parameters
                        }

                        # 异步发送任务
                        success = await ws_manager.send_task(device.device_number, task_msg)
                        if success:
                            logger.info(f"✅ 成功启动下一个任务: 任务ID={task.id}, 设备={device.device_number}")
                        else:
                            logger.error(f"❌ 发送任务失败: 任务ID={task.id}, 设备={device.device_number}")
                            # 发送失败，恢复状态
                            next_task_queue.status = 'pending'
                            next_task_queue.dispatch_time = None
                    else:
                        logger.error(f"❌ 未找到任务详情: 任务ID={next_task_queue.task_id}")
                        next_task_queue.status = 'failed'
                        next_task_queue.finish_time = get_beijing_now_naive()
                        next_task_queue.result_summary = "任务详情不存在"
                else:
                    logger.warning(f"设备 {device_id} 离线或不存在，无法启动下一个任务")
            else:
                logger.debug(f"设备 {device_id} 没有更多待执行的任务")

        except Exception as e:
            logger.error(f"启动下一个任务失败: 设备ID={completed_task_queue.device_id}, 错误: {e}")

    async def cleanup_stale_tasks(self):
        """清理异常状态的任务"""
        try:
            db = next(get_db())
            current_time = get_beijing_now_naive()

            # 查找运行时间超过1小时的任务（可能是异常任务）
            one_hour_ago = current_time - timedelta(hours=1)

            stale_tasks = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.status == 'running',
                    TaskQueue.dispatch_time.isnot(None),
                    TaskQueue.dispatch_time <= one_hour_ago
                )
            ).all()

            if stale_tasks:
                logger.warning(f"发现 {len(stale_tasks)} 个异常长时间运行的任务，开始清理...")

                for task in stale_tasks:
                    runtime_hours = (current_time - task.dispatch_time).total_seconds() / 3600
                    logger.warning(f"清理异常任务: 任务ID={task.task_id}, 设备ID={task.device_id}, "
                                 f"运行时长={runtime_hours:.1f}小时")

                    task.status = 'failed'
                    task.finish_time = current_time
                    task.result_summary = f"异常长时间运行任务清理（运行{runtime_hours:.1f}小时）"

                    # TaskQueue模型没有error_message字段，错误信息已包含在result_summary中

                db.commit()

                # 为每个清理的任务启动下一个任务
                for task in stale_tasks:
                    await self.start_next_task_if_needed(db, task)

                logger.info(f"已清理 {len(stale_tasks)} 个异常任务，并尝试启动下一个任务")

        except Exception as e:
            logger.error(f"清理异常任务失败: {e}")
            if 'db' in locals():
                db.rollback()
        finally:
            if 'db' in locals():
                db.close()

    def get_running_tasks_info(self) -> Dict[str, Any]:
        """获取当前运行中的任务信息"""
        try:
            db = next(get_db())

            # 获取运行中的任务
            running_tasks = db.query(TaskQueue).filter(TaskQueue.status == 'running').all()

            current_time = get_beijing_now_naive()
            task_info = []

            for task in running_tasks:
                if task.dispatch_time:
                    runtime_seconds = (current_time - task.dispatch_time).total_seconds()
                    runtime_minutes = runtime_seconds / 60

                    # 计算剩余时间
                    remaining_seconds = (self.timeout_minutes * 60) - runtime_seconds
                    remaining_minutes = remaining_seconds / 60

                    task_info.append({
                        "task_id": task.task_id,
                        "device_id": task.device_id,
                        "runtime_minutes": round(runtime_minutes, 1),
                        "remaining_minutes": round(remaining_minutes, 1),
                        "is_near_timeout": remaining_minutes < 1,  # 剩余不到1分钟
                        "dispatch_time": task.dispatch_time.strftime("%H:%M:%S") if task.dispatch_time else None
                    })

            return {
                "running_count": len(running_tasks),
                "tasks": task_info,
                "timeout_threshold_minutes": self.timeout_minutes
            }

        except Exception as e:
            logger.error(f"获取运行任务信息失败: {e}")
            return {
                "running_count": 0,
                "tasks": [],
                "timeout_threshold_minutes": self.timeout_minutes
            }
        finally:
            if 'db' in locals():
                db.close()
    
    def get_timeout_statistics(self) -> Dict[str, Any]:
        """获取超时统计信息"""
        try:
            db = next(get_db())
            
            # 获取最近24小时的超时任务统计
            current_time = get_beijing_now_naive()
            yesterday = current_time - timedelta(days=1)
            
            timeout_count = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.status == 'failed',
                    TaskQueue.result_summary.like('%任务超时%'),
                    TaskQueue.finish_time >= yesterday
                )
            ).count()

            total_tasks = db.query(TaskQueue).filter(
                TaskQueue.finish_time >= yesterday
            ).count()
            
            timeout_rate = (timeout_count / total_tasks * 100) if total_tasks > 0 else 0
            
            return {
                "timeout_count_24h": timeout_count,
                "total_tasks_24h": total_tasks,
                "timeout_rate_24h": round(timeout_rate, 2),
                "timeout_threshold_minutes": self.timeout_minutes,
                "is_monitoring": self.is_running
            }
            
        except Exception as e:
            logger.error(f"获取超时统计失败: {e}")
            return {
                "timeout_count_24h": 0,
                "total_tasks_24h": 0,
                "timeout_rate_24h": 0,
                "timeout_threshold_minutes": self.timeout_minutes,
                "is_monitoring": self.is_running
            }
        finally:
            if 'db' in locals():
                db.close()

# 全局监控器实例
timeout_monitor = TaskTimeoutMonitor(timeout_minutes=5)

async def start_timeout_monitoring():
    """启动超时监控"""
    await timeout_monitor.start_monitoring()

def stop_timeout_monitoring():
    """停止超时监控"""
    timeout_monitor.stop_monitoring()

def get_timeout_stats():
    """获取超时统计"""
    return timeout_monitor.get_timeout_statistics()

def get_running_tasks_info():
    """获取运行中任务信息"""
    return timeout_monitor.get_running_tasks_info()
