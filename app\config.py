import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DB_NAME = os.getenv("DB_NAME", "wb")

# 检查是否使用SQLite
if DB_NAME.startswith("sqlite:"):
    DATABASE_URL = DB_NAME
else:
    # MySQL配置
    MYSQL_USER = os.getenv("DB_USER", "root")
    MYSQL_PASSWORD = os.getenv("DB_PASSWORD", "123456")
    MYSQL_HOST = os.getenv("DB_HOST", "127.0.0.1")
    MYSQL_PORT = os.getenv("DB_PORT", "3306")
    MYSQL_DB = DB_NAME

    DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"
