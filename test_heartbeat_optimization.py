#!/usr/bin/env python3
"""
WebSocket心跳优化测试脚本
验证优化后的心跳机制是否正常工作
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class HeartbeatTestClient:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        self.heartbeat_interval = 3  # 3秒心跳间隔
        self.heartbeat_task = None
        self.running = False
        self.heartbeat_count = 0
        self.ack_count = 0
        self.latencies = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.running = True
            print(f"✅ 设备 {self.device_number} 连接成功")
            
            # 启动心跳和消息处理
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            message_task = asyncio.create_task(self._message_loop())
            
            # 等待任务完成
            await asyncio.gather(self.heartbeat_task, message_task)
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.websocket:
            await self.websocket.close()
        print(f"🔌 设备 {self.device_number} 已断开连接")
        
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except Exception as e:
                print(f"💔 心跳发送失败: {e}")
                break
                
    async def _send_heartbeat(self):
        """发送心跳消息"""
        if not self.websocket:
            return
            
        heartbeat_msg = {
            "type": "heartbeat",
            "timestamp": datetime.utcnow().isoformat(),
            "device_number": self.device_number,
            "sequence": self.heartbeat_count
        }
        
        send_time = time.time()
        await self.websocket.send(json.dumps(heartbeat_msg))
        self.heartbeat_count += 1
        
        print(f"💓 发送心跳 #{self.heartbeat_count}")
        
    async def _message_loop(self):
        """消息接收循环"""
        while self.running:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)
                await self._handle_message(data)
            except Exception as e:
                print(f"📨 消息接收失败: {e}")
                break
                
    async def _handle_message(self, data):
        """处理接收到的消息"""
        msg_type = data.get("type")
        
        if msg_type == "heartbeat_ack":
            self.ack_count += 1
            
            # 计算延迟
            if "timestamp" in data:
                try:
                    server_time = datetime.fromisoformat(data["timestamp"].replace('Z', ''))
                    client_time = datetime.utcnow()
                    latency = (client_time - server_time).total_seconds() * 1000
                    self.latencies.append(latency)
                    
                    print(f"💚 收到心跳确认 #{self.ack_count} (延迟: {latency:.1f}ms)")
                except:
                    print(f"💚 收到心跳确认 #{self.ack_count}")
            
            # 显示延迟统计
            if data.get("latency_ms"):
                print(f"   服务器计算延迟: {data['latency_ms']}ms")
                
        elif msg_type == "error":
            print(f"❌ 服务器错误: {data.get('message', 'Unknown error')}")
            
        else:
            print(f"📨 收到消息: {data}")

    def get_statistics(self):
        """获取统计信息"""
        avg_latency = sum(self.latencies) / len(self.latencies) if self.latencies else 0
        max_latency = max(self.latencies) if self.latencies else 0
        min_latency = min(self.latencies) if self.latencies else 0
        
        return {
            "heartbeat_sent": self.heartbeat_count,
            "heartbeat_ack": self.ack_count,
            "success_rate": (self.ack_count / self.heartbeat_count * 100) if self.heartbeat_count > 0 else 0,
            "avg_latency_ms": avg_latency,
            "max_latency_ms": max_latency,
            "min_latency_ms": min_latency
        }

async def test_single_client():
    """测试单个客户端心跳"""
    print("🧪 测试单个客户端心跳机制...")
    
    client = HeartbeatTestClient("test_device_001")
    
    try:
        # 运行30秒测试
        await asyncio.wait_for(client.connect(), timeout=30)
    except asyncio.TimeoutError:
        print("⏰ 测试超时，正常结束")
    except KeyboardInterrupt:
        print("⌨️ 用户中断测试")
    finally:
        await client.disconnect()
        
        # 显示统计信息
        stats = client.get_statistics()
        print(f"\n📊 测试统计:")
        print(f"   发送心跳: {stats['heartbeat_sent']}")
        print(f"   收到确认: {stats['heartbeat_ack']}")
        print(f"   成功率: {stats['success_rate']:.1f}%")
        print(f"   平均延迟: {stats['avg_latency_ms']:.1f}ms")
        print(f"   最大延迟: {stats['max_latency_ms']:.1f}ms")
        print(f"   最小延迟: {stats['min_latency_ms']:.1f}ms")

async def test_multiple_clients():
    """测试多个客户端心跳"""
    print("🧪 测试多个客户端心跳机制...")
    
    clients = []
    for i in range(3):
        client = HeartbeatTestClient(f"test_device_{i:03d}")
        clients.append(client)
    
    try:
        # 并发运行所有客户端
        tasks = [asyncio.create_task(client.connect()) for client in clients]
        await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=20)
    except asyncio.TimeoutError:
        print("⏰ 多客户端测试超时，正常结束")
    except KeyboardInterrupt:
        print("⌨️ 用户中断测试")
    finally:
        # 断开所有客户端
        for client in clients:
            await client.disconnect()
            
        # 显示汇总统计
        print(f"\n📊 多客户端测试汇总:")
        total_sent = sum(client.heartbeat_count for client in clients)
        total_ack = sum(client.ack_count for client in clients)
        
        print(f"   客户端数量: {len(clients)}")
        print(f"   总发送心跳: {total_sent}")
        print(f"   总收到确认: {total_ack}")
        print(f"   整体成功率: {(total_ack / total_sent * 100) if total_sent > 0 else 0:.1f}%")

async def test_heartbeat_timeout():
    """测试心跳超时机制"""
    print("🧪 测试心跳超时机制...")
    
    client = HeartbeatTestClient("test_timeout_device")
    
    try:
        # 连接并发送几个心跳
        await asyncio.wait_for(client.connect(), timeout=10)
    except asyncio.TimeoutError:
        pass
    except KeyboardInterrupt:
        pass
    finally:
        # 停止心跳但保持连接
        client.running = False
        if client.heartbeat_task:
            client.heartbeat_task.cancel()
            
        print("🛑 停止发送心跳，等待服务器超时断开...")
        
        # 等待服务器检测超时并断开连接
        try:
            while client.websocket and not client.websocket.closed:
                await asyncio.sleep(1)
                print("⏳ 等待服务器断开连接...")
        except:
            pass
            
        print("✅ 服务器已检测到心跳超时并断开连接")
        await client.disconnect()

async def main():
    """主测试函数"""
    print("🚀 WebSocket心跳优化测试开始...\n")
    
    tests = [
        ("单客户端心跳测试", test_single_client),
        ("多客户端心跳测试", test_multiple_clients),
        ("心跳超时测试", test_heartbeat_timeout)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            await test_func()
            print(f"✅ {test_name} 完成")
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
        
        print("\n⏸️ 等待5秒后继续下一个测试...")
        await asyncio.sleep(5)
    
    print(f"\n🎉 所有心跳测试完成！")
    print("\n📋 测试总结:")
    print("1. 验证了客户端心跳发送机制")
    print("2. 验证了服务器心跳确认响应")
    print("3. 验证了多客户端并发心跳")
    print("4. 验证了心跳超时检测机制")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
