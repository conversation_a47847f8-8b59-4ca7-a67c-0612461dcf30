from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db import get_db
from app.schemas.group import GroupCreate, GroupUpdate, GroupDevice, GroupOut
from app.services.group import (
    create_group,
    get_group,
    get_all_groups,
    update_group,
    delete_group,
    add_device_to_group,
    remove_device_from_group,
    get_group_devices
)

router = APIRouter()

@router.post("/", response_model=GroupOut)
def create_new_group(group: GroupCreate, db: Session = Depends(get_db)):
    db_group = create_group(db, group)
    return GroupOut.model_validate(db_group)

@router.get("/", response_model=list[GroupOut])
def read_groups(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    groups = get_all_groups(db)
    return [GroupOut.model_validate(group) for group in groups]

@router.get("/{group_id}", response_model=GroupOut)
def read_group(group_id: int, db: Session = Depends(get_db)):
    db_group = get_group(db, group_id=group_id)
    if db_group is None:
        raise HTTPException(status_code=404, detail="Group not found")
    return GroupOut.model_validate(db_group)

@router.put("/{group_id}", response_model=GroupCreate)
def update_existing_group(group_id: int, group: GroupUpdate, db: Session = Depends(get_db)):
    db_group = update_group(db, group_id=group_id, group_data=group)
    if db_group is None:
        raise HTTPException(status_code=404, detail="Group not found")
    return db_group

@router.delete("/{group_id}")
def delete_existing_group(group_id: int, db: Session = Depends(get_db)):
    db_group = delete_group(db, group_id=group_id)
    if db_group is None:
        raise HTTPException(status_code=404, detail="Group not found")
    return {"message": "Group deleted successfully"}

@router.post("/{group_id}/devices")
def add_device_to_existing_group(group_id: int, device: GroupDevice, db: Session = Depends(get_db)):
    if device.group_id is None:
        device.group_id = group_id
    elif device.group_id != group_id:
        raise HTTPException(status_code=400, detail="Group ID in path and body do not match")

    result = add_device_to_group(db, device)
    if result is None:
        raise HTTPException(
            status_code=400,
            detail="Failed to add device to group"
        )
    return result

@router.delete("/{group_id}/devices/{device_id}")
def remove_device_from_existing_group(group_id: int, device_id: int, db: Session = Depends(get_db)):
    group_device = GroupDevice(device_id=device_id, group_id=group_id)
    result = remove_device_from_group(db, group_device)
    if result is None:
        raise HTTPException(status_code=404, detail="Device not found in group")
    return {"message": "Device removed from group successfully"}
