# 设备管理系统前端依赖包
# Device Management System Frontend Requirements

# HTTP请求库 - 用于与后端API通信
requests>=2.28.0

# JSON处理库 (Python内置，但列出以供参考)
# json - Built-in Python module

# 日期时间处理库 (Python内置)
# datetime - Built-in Python module

# 线程处理库 (Python内置)
# threading - Built-in Python module

# GUI框架 (通常随Python安装)
# tkinter - Usually comes with Python installation

# 可选：更好的HTTP会话管理
urllib3>=1.26.0

# 可选：JSON模式验证
# jsonschema>=4.0.0

# 开发和测试依赖 (可选)
# pytest>=7.0.0
# pytest-mock>=3.0.0

# 注意事项:
# 1. tkinter通常随Python安装，如果缺失请安装python3-tk (Linux) 或重新安装Python
# 2. 本项目主要使用Python标准库，依赖较少
# 3. 确保Python版本 >= 3.7
