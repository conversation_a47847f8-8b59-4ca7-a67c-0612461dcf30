"""
简化稳定版任务调度器
专注于稳定性，避免复杂的数据库依赖
"""

import asyncio
import logging
from typing import Dict
from datetime import datetime

logger = logging.getLogger(__name__)

class SimpleStableScheduler:
    """简化稳定版任务调度器"""
    
    def __init__(self):
        # 基础状态
        self.is_running = False
        self.is_paused = False
        
        # 设备队列和锁
        self.device_queues: Dict[str, asyncio.Queue] = {}
        self.device_locks: Dict[str, asyncio.Lock] = {}
        
        # 任务处理器
        self.task_processors: Dict[str, asyncio.Task] = {}
        
        # 稳定性监控
        self.last_heartbeat = datetime.now()
        self.error_count = 0
        self.max_errors = 10
        
        # 延迟初始化事件
        self.pause_event = None
        
        # 模拟设备列表
        self.mock_devices = ["ceshi212", "test_device_1", "test_device_2"]
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        try:
            # 初始化事件
            if self.pause_event is None:
                self.pause_event = asyncio.Event()
                self.pause_event.set()
            
            self.is_running = True
            self.error_count = 0
            logger.info("🚀 启动简化稳定版调度器")
            
            # 初始化设备
            await self._initialize_devices()
            
            # 启动核心组件
            asyncio.create_task(self._stable_task_dispatcher())
            asyncio.create_task(self._stability_monitor())
            
            logger.info("✅ 简化稳定版调度器启动完成")
            
        except Exception as e:
            logger.error(f"❌ 调度器启动失败: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """停止调度器"""
        self.is_running = False
        
        # 停止所有任务处理器
        for processor in self.task_processors.values():
            if not processor.done():
                processor.cancel()
        
        logger.info("🛑 简化稳定版调度器已停止")
    
    async def pause(self):
        """暂停任务分发"""
        if self.is_paused:
            logger.warning("调度器已处于暂停状态")
            return
        
        self.is_paused = True
        if self.pause_event:
            self.pause_event.clear()
        
        logger.info("⏸️ 简化稳定版调度器已暂停")
    
    async def resume(self):
        """恢复任务分发"""
        if not self.is_paused:
            logger.warning("调度器未处于暂停状态")
            return
        
        self.is_paused = False
        if self.pause_event:
            self.pause_event.set()
        
        logger.info("▶️ 简化稳定版调度器已恢复")
    
    async def _initialize_devices(self):
        """初始化设备"""
        logger.info(f"📱 初始化 {len(self.mock_devices)} 个模拟设备")
        
        for device_number in self.mock_devices:
            # 创建设备队列和锁
            self.device_queues[device_number] = asyncio.Queue(maxsize=100)
            self.device_locks[device_number] = asyncio.Lock()
            
            # 启动设备任务处理器
            processor = asyncio.create_task(
                self._stable_device_processor(device_number)
            )
            self.task_processors[device_number] = processor
            
            logger.info(f"📱 设备 {device_number} 处理器已启动")
    
    async def _stable_task_dispatcher(self):
        """稳定的任务分发器"""
        logger.info("🔄 稳定任务分发器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.is_running:
            try:
                # 更新心跳
                self.last_heartbeat = datetime.now()
                
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 模拟分发新任务
                await self._simulate_task_dispatch()
                
                # 重置错误计数
                consecutive_errors = 0
                
                # 正常等待
                await asyncio.sleep(5)  # 5秒检查一次
                
            except Exception as e:
                consecutive_errors += 1
                self.error_count += 1
                
                logger.error(f"❌ 任务分发器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error("❌ 连续错误过多，延长等待时间")
                    await asyncio.sleep(30)
                    consecutive_errors = 0
                else:
                    wait_time = min(2 ** consecutive_errors, 30)
                    await asyncio.sleep(wait_time)
    
    async def _simulate_task_dispatch(self):
        """真实任务分发 - 从数据库获取待处理任务"""
        try:
            # 🔥 修复：连接到真实的数据库任务
            from app.db import get_db
            from app.models.task import Task, TaskQueue
            from app.models.device import Device
            from app.models.device_group import DeviceGroup
            from sqlalchemy import and_

            db = next(get_db())
            try:
                # 查找新任务（pending状态且未在队列中的任务）
                new_tasks = db.query(Task).filter(
                    and_(
                        Task.status == 'pending',
                        ~Task.id.in_(
                            db.query(TaskQueue.task_id).distinct()
                        )
                    )
                ).order_by(Task.create_time.asc()).limit(5).all()

                if new_tasks:
                    logger.info(f"📋 发现 {len(new_tasks)} 个新任务待分发")

                    for task in new_tasks:
                        await self._dispatch_real_task(task, db)

                # 查找已有队列中的pending任务
                pending_queues = db.query(TaskQueue).filter(
                    TaskQueue.status == 'pending'
                ).limit(10).all()

                if pending_queues:
                    logger.info(f"📋 发现 {len(pending_queues)} 个队列中的待处理任务")

                    for tq in pending_queues:
                        task = db.query(Task).filter(Task.id == tq.task_id).first()
                        device = db.query(Device).filter(Device.id == tq.device_id).first()

                        if task and device:
                            await self._add_task_to_device_queue(device.device_number, task)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ 真实任务分发失败: {e}")

    async def _dispatch_real_task(self, task, db):
        """分发真实任务到设备"""
        try:
            from app.models.device import Device
            from app.models.device_group import DeviceGroup

            if task.target_scope == "group":
                # 获取组内设备
                devices = db.query(Device).join(DeviceGroup).filter(
                    DeviceGroup.group_id == task.target_id
                ).all()

                for device in devices:
                    await self._create_task_queue_record(task, device.id, db)
                    await self._add_task_to_device_queue(device.device_number, task)

            elif task.target_scope == "single":
                device = db.query(Device).filter(Device.id == task.target_id).first()
                if device:
                    await self._create_task_queue_record(task, device.id, db)
                    await self._add_task_to_device_queue(device.device_number, task)

            elif task.target_scope == "all":
                devices = db.query(Device).all()
                for device in devices:
                    await self._create_task_queue_record(task, device.id, db)
                    await self._add_task_to_device_queue(device.device_number, task)

        except Exception as e:
            logger.error(f"❌ 分发任务 {task.id} 失败: {e}")

    async def _create_task_queue_record(self, task, device_id, db):
        """创建任务队列记录"""
        try:
            from app.models.task import TaskQueue
            from app.utils.time_utils import get_beijing_now_naive
            from sqlalchemy import and_

            # 检查是否已存在
            existing = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.task_id == task.id,
                    TaskQueue.device_id == device_id
                )
            ).first()

            if existing:
                return

            # 创建任务队列记录
            task_queue = TaskQueue(
                task_id=task.id,
                device_id=device_id,
                status='pending',
                create_time=get_beijing_now_naive()
            )

            db.add(task_queue)
            db.commit()

            logger.debug(f"📤 创建任务队列记录: 任务{task.id}, 设备{device_id}")

        except Exception as e:
            logger.error(f"❌ 创建任务队列记录失败: 任务{task.id}, 设备{device_id}, 错误:{e}")
            db.rollback()

    async def _add_task_to_device_queue(self, device_number, task):
        """添加任务到设备队列"""
        if device_number in self.device_queues:
            try:
                await self.device_queues[device_number].put(task)
                logger.info(f"📤 任务 {task.id} 已加入设备 {device_number} 队列")
            except Exception as e:
                logger.error(f"❌ 添加任务到队列失败: {e}")
        else:
            logger.warning(f"⚠️ 设备 {device_number} 队列不存在")
    
    async def _stable_device_processor(self, device_number: str):
        """稳定的设备任务处理器"""
        logger.info(f"🔄 设备 {device_number} 稳定处理器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 3
        
        while self.is_running:
            try:
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 检查设备是否在线（模拟）
                if not self._is_device_online(device_number):
                    await asyncio.sleep(5)
                    continue
                
                # 获取任务
                try:
                    task = await asyncio.wait_for(
                        self.device_queues[device_number].get(),
                        timeout=3.0
                    )
                    
                    # 处理任务
                    await self._process_task(device_number, task)
                    
                    # 重置错误计数
                    consecutive_errors = 0
                    
                except asyncio.TimeoutError:
                    # 队列为空，继续等待
                    continue
                
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"❌ 设备 {device_number} 处理器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"❌ 设备 {device_number} 连续错误过多，暂停处理")
                    await asyncio.sleep(60)
                    consecutive_errors = 0
                else:
                    await asyncio.sleep(5)
    
    async def _process_task(self, device_number: str, task):
        """处理真实任务并应用分组延迟"""
        try:
            async with self.device_locks[device_number]:
                logger.info(f"🚀 开始处理任务: 任务ID={task.id}, 设备={device_number}")

                # 🔥 关键：应用分组延迟
                if hasattr(task, 'delay_group') and task.delay_group > 0:
                    delay_seconds = task.delay_group / 1000.0  # 毫秒转秒
                    logger.info(f"⏰ 应用分组延迟: {delay_seconds}秒")
                    await asyncio.sleep(delay_seconds)

                # 更新任务状态为running
                await self._update_task_status(task.id, device_number, 'running')

                # 发送任务到设备
                success = await self._send_task_to_device(device_number, task)

                if success:
                    logger.info(f"✅ 任务 {task.id} 发送成功到设备 {device_number}")
                    # 应用操作延迟
                    if hasattr(task, 'delay_like') and task.delay_like > 0:
                        delay_seconds = task.delay_like / 1000.0
                        logger.info(f"⏰ 应用操作延迟: {delay_seconds}秒")
                        await asyncio.sleep(delay_seconds)
                else:
                    logger.error(f"❌ 任务 {task.id} 发送失败")
                    await self._update_task_status(task.id, device_number, 'failed')

        except Exception as e:
            logger.error(f"❌ 处理任务失败: 任务{getattr(task, 'id', 'unknown')}, 设备={device_number}, 错误:{e}")

    async def _send_task_to_device(self, device_number: str, task) -> bool:
        """发送任务到设备"""
        try:
            # 尝试通过WebSocket发送
            from app.websocket.ws_manager import manager as ws_manager

            # 构建任务消息
            task_msg = {
                'task_id': task.id,
                'type': task.task_type,
                'parameters': task.parameters,
                'device_number': device_number
            }

            # 检查WebSocket连接
            if device_number in ws_manager.active_connections:
                success = await ws_manager.send_task(device_number, task_msg)
                return success
            else:
                logger.warning(f"⚠️ 设备 {device_number} 离线，无法发送任务")
                return False

        except Exception as e:
            logger.error(f"❌ 发送任务失败: {e}")
            return False

    async def _update_task_status(self, task_id: int, device_number: str, status: str):
        """更新任务状态"""
        try:
            from app.db import get_db
            from app.models.task import TaskQueue
            from app.models.device import Device
            from app.utils.time_utils import get_beijing_now_naive
            from sqlalchemy import and_

            db = next(get_db())
            try:
                # 获取设备ID
                device = db.query(Device).filter(Device.device_number == device_number).first()
                if not device:
                    logger.error(f"❌ 设备 {device_number} 不存在")
                    return

                # 更新任务队列状态
                task_queue = db.query(TaskQueue).filter(
                    and_(
                        TaskQueue.task_id == task_id,
                        TaskQueue.device_id == device.id
                    )
                ).first()

                if task_queue:
                    task_queue.status = status

                    if status == 'running':
                        task_queue.dispatch_time = get_beijing_now_naive()
                    elif status in ['done', 'failed']:
                        task_queue.finish_time = get_beijing_now_naive()

                    db.commit()
                    logger.debug(f"📊 任务状态更新: 任务{task_id}, 设备{device_number}, 状态={status}")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: 任务{task_id}, 设备{device_number}, 错误:{e}")
    
    def _is_device_online(self, device_number: str) -> bool:
        """检查设备是否在线（模拟）"""
        try:
            # 尝试导入WebSocket管理器
            from app.websocket.ws_manager import manager as ws_manager
            return device_number in ws_manager.active_connections
        except:
            # 如果导入失败，假设设备在线
            return True
    
    async def _stability_monitor(self):
        """稳定性监控器"""
        logger.info("🔄 稳定性监控器已启动")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 检查心跳
                heartbeat_age = (current_time - self.last_heartbeat).total_seconds()
                if heartbeat_age > 60:
                    logger.warning(f"⚠️ 调度器心跳异常: {heartbeat_age:.1f}秒无响应")
                
                # 检查错误率
                if self.error_count > self.max_errors:
                    logger.warning(f"⚠️ 错误次数过多: {self.error_count}")
                    self.error_count = 0
                
                # 检查任务处理器状态
                dead_processors = []
                for device_number, processor in self.task_processors.items():
                    if processor.done():
                        dead_processors.append(device_number)
                
                # 重启死掉的处理器
                for device_number in dead_processors:
                    logger.warning(f"⚠️ 重启设备 {device_number} 的任务处理器")
                    processor = asyncio.create_task(
                        self._stable_device_processor(device_number)
                    )
                    self.task_processors[device_number] = processor
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 稳定性监控错误: {e}")
                await asyncio.sleep(60)
    
    async def add_task_to_device(self, device_number: str, task_data):
        """添加任务到指定设备"""
        if device_number in self.device_queues:
            try:
                await self.device_queues[device_number].put(task_data)
                logger.info(f"📤 任务已添加到设备 {device_number} 队列")
                return True
            except Exception as e:
                logger.error(f"❌ 添加任务失败: {e}")
                return False
        else:
            logger.error(f"❌ 设备 {device_number} 不存在")
            return False

# 创建全局实例
simple_stable_scheduler = SimpleStableScheduler()
