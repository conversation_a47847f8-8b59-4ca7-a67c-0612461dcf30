"""
简化稳定版任务调度器
专注于稳定性，避免复杂的数据库依赖
"""

import asyncio
import logging
from typing import Dict
from datetime import datetime

logger = logging.getLogger(__name__)

class SimpleStableScheduler:
    """简化稳定版任务调度器"""
    
    def __init__(self):
        # 基础状态
        self.is_running = False
        self.is_paused = False
        
        # 设备队列和锁
        self.device_queues: Dict[str, asyncio.Queue] = {}
        self.device_locks: Dict[str, asyncio.Lock] = {}
        
        # 任务处理器
        self.task_processors: Dict[str, asyncio.Task] = {}
        
        # 稳定性监控
        self.last_heartbeat = datetime.now()
        self.error_count = 0
        self.max_errors = 10
        
        # 延迟初始化事件
        self.pause_event = None
        
        # 模拟设备列表
        self.mock_devices = ["ceshi212", "test_device_1", "test_device_2"]
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        try:
            # 初始化事件
            if self.pause_event is None:
                self.pause_event = asyncio.Event()
                self.pause_event.set()
            
            self.is_running = True
            self.error_count = 0
            logger.info("🚀 启动简化稳定版调度器")
            
            # 初始化设备
            await self._initialize_devices()
            
            # 启动核心组件
            asyncio.create_task(self._stable_task_dispatcher())
            asyncio.create_task(self._stability_monitor())
            
            logger.info("✅ 简化稳定版调度器启动完成")
            
        except Exception as e:
            logger.error(f"❌ 调度器启动失败: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """停止调度器"""
        self.is_running = False
        
        # 停止所有任务处理器
        for processor in self.task_processors.values():
            if not processor.done():
                processor.cancel()
        
        logger.info("🛑 简化稳定版调度器已停止")
    
    async def pause(self):
        """暂停任务分发"""
        if self.is_paused:
            logger.warning("调度器已处于暂停状态")
            return
        
        self.is_paused = True
        if self.pause_event:
            self.pause_event.clear()
        
        logger.info("⏸️ 简化稳定版调度器已暂停")
    
    async def resume(self):
        """恢复任务分发"""
        if not self.is_paused:
            logger.warning("调度器未处于暂停状态")
            return
        
        self.is_paused = False
        if self.pause_event:
            self.pause_event.set()
        
        logger.info("▶️ 简化稳定版调度器已恢复")
    
    async def _initialize_devices(self):
        """初始化设备"""
        logger.info(f"📱 初始化 {len(self.mock_devices)} 个模拟设备")
        
        for device_number in self.mock_devices:
            # 创建设备队列和锁
            self.device_queues[device_number] = asyncio.Queue(maxsize=100)
            self.device_locks[device_number] = asyncio.Lock()
            
            # 启动设备任务处理器
            processor = asyncio.create_task(
                self._stable_device_processor(device_number)
            )
            self.task_processors[device_number] = processor
            
            logger.info(f"📱 设备 {device_number} 处理器已启动")
    
    async def _stable_task_dispatcher(self):
        """稳定的任务分发器"""
        logger.info("🔄 稳定任务分发器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.is_running:
            try:
                # 更新心跳
                self.last_heartbeat = datetime.now()
                
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 模拟分发新任务
                await self._simulate_task_dispatch()
                
                # 重置错误计数
                consecutive_errors = 0
                
                # 正常等待
                await asyncio.sleep(5)  # 5秒检查一次
                
            except Exception as e:
                consecutive_errors += 1
                self.error_count += 1
                
                logger.error(f"❌ 任务分发器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error("❌ 连续错误过多，延长等待时间")
                    await asyncio.sleep(30)
                    consecutive_errors = 0
                else:
                    wait_time = min(2 ** consecutive_errors, 30)
                    await asyncio.sleep(wait_time)
    
    async def _simulate_task_dispatch(self):
        """模拟任务分发"""
        # 这里可以集成真实的任务分发逻辑
        # 目前只是保持调度器运行
        pass
    
    async def _stable_device_processor(self, device_number: str):
        """稳定的设备任务处理器"""
        logger.info(f"🔄 设备 {device_number} 稳定处理器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 3
        
        while self.is_running:
            try:
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 检查设备是否在线（模拟）
                if not self._is_device_online(device_number):
                    await asyncio.sleep(5)
                    continue
                
                # 获取任务
                try:
                    task = await asyncio.wait_for(
                        self.device_queues[device_number].get(),
                        timeout=3.0
                    )
                    
                    # 处理任务
                    await self._process_task(device_number, task)
                    
                    # 重置错误计数
                    consecutive_errors = 0
                    
                except asyncio.TimeoutError:
                    # 队列为空，继续等待
                    continue
                
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"❌ 设备 {device_number} 处理器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"❌ 设备 {device_number} 连续错误过多，暂停处理")
                    await asyncio.sleep(60)
                    consecutive_errors = 0
                else:
                    await asyncio.sleep(5)
    
    async def _process_task(self, device_number: str, task):
        """处理任务"""
        try:
            async with self.device_locks[device_number]:
                logger.info(f"🚀 开始处理任务: 设备={device_number}")
                
                # 模拟任务处理
                await asyncio.sleep(1)
                
                logger.info(f"✅ 任务处理完成: 设备={device_number}")
                
        except Exception as e:
            logger.error(f"❌ 处理任务失败: 设备={device_number}, 错误:{e}")
    
    def _is_device_online(self, device_number: str) -> bool:
        """检查设备是否在线（模拟）"""
        try:
            # 尝试导入WebSocket管理器
            from app.websocket.ws_manager import manager as ws_manager
            return device_number in ws_manager.active_connections
        except:
            # 如果导入失败，假设设备在线
            return True
    
    async def _stability_monitor(self):
        """稳定性监控器"""
        logger.info("🔄 稳定性监控器已启动")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 检查心跳
                heartbeat_age = (current_time - self.last_heartbeat).total_seconds()
                if heartbeat_age > 60:
                    logger.warning(f"⚠️ 调度器心跳异常: {heartbeat_age:.1f}秒无响应")
                
                # 检查错误率
                if self.error_count > self.max_errors:
                    logger.warning(f"⚠️ 错误次数过多: {self.error_count}")
                    self.error_count = 0
                
                # 检查任务处理器状态
                dead_processors = []
                for device_number, processor in self.task_processors.items():
                    if processor.done():
                        dead_processors.append(device_number)
                
                # 重启死掉的处理器
                for device_number in dead_processors:
                    logger.warning(f"⚠️ 重启设备 {device_number} 的任务处理器")
                    processor = asyncio.create_task(
                        self._stable_device_processor(device_number)
                    )
                    self.task_processors[device_number] = processor
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 稳定性监控错误: {e}")
                await asyncio.sleep(60)
    
    async def add_task_to_device(self, device_number: str, task_data):
        """添加任务到指定设备"""
        if device_number in self.device_queues:
            try:
                await self.device_queues[device_number].put(task_data)
                logger.info(f"📤 任务已添加到设备 {device_number} 队列")
                return True
            except Exception as e:
                logger.error(f"❌ 添加任务失败: {e}")
                return False
        else:
            logger.error(f"❌ 设备 {device_number} 不存在")
            return False

# 创建全局实例
simple_stable_scheduler = SimpleStableScheduler()
