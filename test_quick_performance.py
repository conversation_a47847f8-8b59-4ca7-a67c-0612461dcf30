#!/usr/bin/env python3
"""
快速性能测试
验证优化后的调度器性能
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def test_api_performance():
    """测试API响应性能"""
    log_with_time("=== API性能测试 ===")
    
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": "性能测试",
            "like_id": "test_like_123"
        },
        "target_scope": "group",
        "target_id": 1,
        "delay_group": 3000,
        "delay_like": 1000
    }
    
    # 测试5次API调用
    times = []
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000
            times.append(duration)
            
            if response.status_code == 200:
                task = response.json()
                log_with_time(f"✅ 测试{i+1}: 任务{task.get('id')} 创建成功，耗时: {duration:.1f}ms")
            else:
                log_with_time(f"❌ 测试{i+1}: 失败 {response.status_code}")
                
        except Exception as e:
            log_with_time(f"❌ 测试{i+1}: 异常 {e}")
        
        time.sleep(0.5)  # 间隔0.5秒
    
    # 分析结果
    if times:
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        log_with_time(f"📊 性能统计:")
        log_with_time(f"   平均耗时: {avg_time:.1f}ms")
        log_with_time(f"   最大耗时: {max_time:.1f}ms")
        log_with_time(f"   最小耗时: {min_time:.1f}ms")
        
        if avg_time < 500:
            log_with_time("✅ API响应性能优秀 (<500ms)")
        elif avg_time < 1000:
            log_with_time("✅ API响应性能良好 (<1s)")
        elif avg_time < 2000:
            log_with_time("⚠️ API响应性能一般 (<2s)")
        else:
            log_with_time("❌ API响应性能较差 (>2s)")

def check_scheduler_status():
    """检查调度器状态"""
    log_with_time("=== 调度器状态检查 ===")
    
    import sys
    sys.path.append('.')
    
    try:
        from app.services.optimized_scheduler import optimized_scheduler
        
        log_with_time(f"📊 调度器运行状态: {optimized_scheduler.is_running}")
        log_with_time(f"📊 设备队列数量: {len(optimized_scheduler.device_queues)}")
        log_with_time(f"📊 设备锁数量: {len(optimized_scheduler.device_locks)}")
        log_with_time(f"📊 正在处理任务数: {len(optimized_scheduler.processing_tasks)}")
        
        # 检查设备状态缓存
        online_devices = sum(1 for online in optimized_scheduler.device_online_cache.values() if online)
        log_with_time(f"📊 在线设备数量: {online_devices}/{len(optimized_scheduler.device_online_cache)}")
        
        # 检查运行任务
        total_running = sum(len(tasks) for tasks in optimized_scheduler.device_running_tasks.values())
        log_with_time(f"📊 运行中任务总数: {total_running}")
        
        if optimized_scheduler.is_running:
            log_with_time("✅ 调度器运行正常")
        else:
            log_with_time("❌ 调度器未运行")
            
    except Exception as e:
        log_with_time(f"❌ 检查调度器状态失败: {e}")

def check_recent_tasks():
    """检查最近任务状态"""
    log_with_time("=== 最近任务状态 ===")
    
    import sys
    sys.path.append('.')
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    from datetime import datetime, timedelta
    
    db = next(get_db())
    try:
        # 检查最近2分钟的任务
        recent_time = datetime.now() - timedelta(minutes=2)
        recent_tasks = db.query(Task).filter(
            Task.create_time >= recent_time
        ).order_by(Task.create_time.desc()).limit(10).all()
        
        log_with_time(f"最近2分钟任务: {len(recent_tasks)}个")
        
        # 统计状态
        status_count = {}
        for task in recent_tasks:
            status = task.status
            status_count[status] = status_count.get(status, 0) + 1
        
        for status, count in status_count.items():
            log_with_time(f"   {status}: {count}个")
        
        # 检查任务队列状态
        if recent_tasks:
            task_ids = [t.id for t in recent_tasks]
            queues = db.query(TaskQueue).filter(TaskQueue.task_id.in_(task_ids)).all()
            
            queue_status_count = {}
            for queue in queues:
                status = queue.status
                queue_status_count[status] = queue_status_count.get(status, 0) + 1
            
            log_with_time("队列状态:")
            for status, count in queue_status_count.items():
                log_with_time(f"   {status}: {count}个")
        
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 快速性能测试")
    print("💡 验证优化后的调度器性能")
    print("=" * 50)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 30)
    print("🧪 开始测试")
    print("=" * 30)
    
    # 1. API性能测试
    test_api_performance()
    
    print("\n" + "-" * 30)
    
    # 2. 调度器状态检查
    check_scheduler_status()
    
    print("\n" + "-" * 30)
    
    # 3. 最近任务状态
    check_recent_tasks()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 优化要点:")
    print("   1. 移除了task_dispatcher中的动态导入")
    print("   2. 添加了缓存机制减少数据库查询")
    print("   3. 防重复处理机制")
    print("   4. 删除了冗余的任务处理方法")
    print("=" * 50)

if __name__ == "__main__":
    main()
