from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from app.db import Base

class Group(Base):
    __tablename__ = "groups"

    id = Column(Integer, primary_key=True, index=True)
    group_name = Column(String(64), nullable=False)
    description = Column(Text)
    create_time = Column(DateTime, default=datetime.utcnow)

    devices = relationship("Device", back_populates="group")
    task_statuses = relationship("GroupTaskStatus", back_populates="group")
    group_devices = relationship("DeviceGroup", back_populates="group")

class DeviceGroup(Base):
    __tablename__ = "device_groups"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("devices.id"))
    group_id = Column(Integer, ForeignKey("groups.id"))
    create_time = Column(DateTime, default=datetime.utcnow)

    device = relationship("Device", back_populates="device_groups")
    group = relationship("Group", back_populates="group_devices")
