"""
后端时间工具模块 - 统一处理北京时间
"""

from datetime import datetime, timezone, timedelta
import pytz

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def get_beijing_now():
    """获取当前北京时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_now_naive():
    """获取当前北京时间（无时区信息，用于数据库存储）"""
    return datetime.now(BEIJING_TZ).replace(tzinfo=None)

def utc_to_beijing(utc_datetime):
    """将UTC时间转换为北京时间"""
    if utc_datetime is None:
        return None
        
    if utc_datetime.tzinfo is None:
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
        
    return utc_datetime.astimezone(BEIJING_TZ)

def beijing_to_utc(beijing_datetime):
    """将北京时间转换为UTC时间"""
    if beijing_datetime is None:
        return None
        
    if beijing_datetime.tzinfo is None:
        beijing_datetime = BEIJING_TZ.localize(beijing_datetime)
        
    return beijing_datetime.astimezone(timezone.utc)

def format_beijing_time(dt=None, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化北京时间"""
    if dt is None:
        dt = get_beijing_now()
    elif dt.tzinfo is None:
        # 假设是UTC时间
        dt = dt.replace(tzinfo=timezone.utc)
        
    beijing_dt = dt.astimezone(BEIJING_TZ)
    return beijing_dt.strftime(format_str)

def beijing_timestamp():
    """获取北京时间戳字符串"""
    return get_beijing_now().strftime('%Y-%m-%d %H:%M:%S')

def beijing_isoformat():
    """获取北京时间ISO格式字符串"""
    return get_beijing_now().isoformat()

# 兼容旧代码的函数
def utcnow_beijing():
    """替代datetime.utcnow()，返回北京时间"""
    return get_beijing_now_naive()

# 导出常用函数
__all__ = [
    'get_beijing_now',
    'get_beijing_now_naive',
    'utc_to_beijing',
    'beijing_to_utc', 
    'format_beijing_time',
    'beijing_timestamp',
    'beijing_isoformat',
    'utcnow_beijing',
    'BEIJING_TZ'
]
