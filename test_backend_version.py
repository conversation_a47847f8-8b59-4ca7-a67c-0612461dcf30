#!/usr/bin/env python3
"""
测试后端版本和代码更新
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.db import engine
from app.models.device import Device
from app.models.group import Group, DeviceGroup
from app.services.group import add_device_to_group
from app.schemas.group import GroupDevice

def test_backend_service_directly():
    """直接测试后端服务代码"""
    print("🔧 直接测试后端服务代码...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 找到一个未分组的设备
        unassigned_device = db.query(Device).filter(Device.group_id.is_(None)).first()
        if not unassigned_device:
            print("❌ 没有未分组的设备")
            return False
            
        # 找到第一个分组
        group = db.query(Group).first()
        if not group:
            print("❌ 没有分组")
            return False
            
        device_id = unassigned_device.id
        group_id = group.id
        
        print(f"   测试设备: {unassigned_device.device_number} (ID: {device_id})")
        print(f"   当前分组ID: {unassigned_device.group_id}")
        print(f"   目标分组: {group.group_name} (ID: {group_id})")
        
        # 直接调用服务方法
        group_device = GroupDevice(device_id=device_id, group_id=group_id)
        result = add_device_to_group(db, group_device)
        
        if result:
            print("✅ 服务方法调用成功")
            
            # 重新查询设备
            updated_device = db.query(Device).filter(Device.id == device_id).first()
            print(f"   设备更新后的分组ID: {updated_device.group_id}")
            
            if updated_device.group_id == group_id:
                print("✅ 直接服务调用成功：设备已正确分配到分组")
                return True
            else:
                print("❌ 直接服务调用失败：设备分组未更新")
                return False
        else:
            print("❌ 服务方法调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 直接测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

def check_service_code():
    """检查服务代码内容"""
    print("\n📝 检查服务代码内容...")
    
    try:
        # 读取服务文件内容
        with open('app/services/group.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否包含我们的修复
        if 'device.group_id = group_device.group_id' in content:
            print("✅ 服务代码包含Device.group_id更新逻辑")
        else:
            print("❌ 服务代码缺少Device.group_id更新逻辑")
            
        if 'from app.models.device import Device' in content:
            print("✅ 服务代码导入了Device模型")
        else:
            print("❌ 服务代码没有导入Device模型")
            
        # 显示add_device_to_group方法的内容
        lines = content.split('\n')
        in_method = False
        method_lines = []
        
        for line in lines:
            if 'def add_device_to_group(' in line:
                in_method = True
                method_lines.append(line)
            elif in_method:
                if line.startswith('def ') and 'add_device_to_group' not in line:
                    break
                method_lines.append(line)
                
        if method_lines:
            print("\n📋 add_device_to_group方法内容:")
            for i, line in enumerate(method_lines[:20]):  # 只显示前20行
                print(f"   {i+1:2d}: {line}")
                
        return True
        
    except Exception as e:
        print(f"❌ 检查代码异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查后端版本和代码更新...")
    print("="*50)
    
    # 1. 检查服务代码
    check_service_code()
    
    # 2. 直接测试服务
    test_backend_service_directly()
    
    print("\n💡 如果直接服务调用成功但API调用失败，说明需要重启后端服务器")

if __name__ == "__main__":
    main()
