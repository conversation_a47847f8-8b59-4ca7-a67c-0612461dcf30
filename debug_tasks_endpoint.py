#!/usr/bin/env python3
"""
调试任务端点问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db
from app import crud, models
from sqlalchemy.orm import Session

def test_get_tasks():
    """测试get_tasks函数"""
    print("测试get_tasks函数...")
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 调用get_tasks
        tasks = crud.get_tasks(db)
        print(f"✅ get_tasks成功，返回 {len(tasks)} 个任务")
        
        # 打印任务详情
        for i, task in enumerate(tasks[:3]):  # 只显示前3个
            print(f"任务 {i+1}:")
            print(f"  ID: {task.id}")
            print(f"  类型: {task.task_type}")
            print(f"  状态: {task.status}")
            print(f"  创建时间: {task.create_time}")
            
            # 检查是否有问题字段
            if hasattr(task, 'update_time'):
                print(f"  更新时间: {task.update_time}")
            else:
                print("  ⚠️ 没有update_time字段")
                
            if hasattr(task, 'progress'):
                print(f"  进度: {task.progress}")
            else:
                print("  ⚠️ 没有progress字段")
            print()
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ get_tasks失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_schema():
    """测试任务schema"""
    print("测试任务schema...")
    
    try:
        from app.schemas.task import TaskOut
        from app.models.task import Task
        
        # 获取数据库会话
        db = next(get_db())
        
        # 获取一个任务
        task = db.query(Task).first()
        if not task:
            print("❌ 数据库中没有任务")
            return False
            
        print(f"任务对象字段: {dir(task)}")
        print(f"任务ID: {task.id}")
        print(f"任务类型: {task.task_type}")
        print(f"任务状态: {task.status}")
        
        # 尝试创建TaskOut对象
        try:
            task_out = TaskOut.from_orm(task)
            print("✅ TaskOut.from_orm成功")
            print(f"TaskOut对象: {task_out}")
        except Exception as e:
            print(f"❌ TaskOut.from_orm失败: {e}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试schema失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 调试任务端点问题...")
    print("="*50)
    
    # 测试get_tasks函数
    print("1. 测试get_tasks函数")
    test_get_tasks()
    
    print("\n" + "="*50)
    
    # 测试任务schema
    print("2. 测试任务schema")
    test_task_schema()

if __name__ == "__main__":
    main()
