#!/usr/bin/env python3
"""
WebSocket消息处理测试脚本
测试修复后的消息接收和JSON解析逻辑
"""

import asyncio
import websockets
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class WebSocketMessageTester:
    def __init__(self, device_number: str, server_url: str = "ws://localhost:8000"):
        self.device_number = device_number
        self.server_url = f"{server_url}/ws/{device_number}"
        self.websocket = None
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 设备 {self.device_number} 连接成功")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def send_valid_json(self):
        """发送有效的JSON消息"""
        print("\n📤 测试发送有效JSON消息...")
        
        messages = [
            {
                "type": "heartbeat",
                "timestamp": "2024-01-01T12:00:00Z",
                "device_number": self.device_number
            },
            {
                "type": "task_completion",
                "task_id": 1,
                "device_id": 1,
                "status": "success",
                "timestamp": "2024-01-01T12:00:00Z"
            }
        ]
        
        for msg in messages:
            try:
                await self.websocket.send(json.dumps(msg))
                print(f"✅ 发送JSON消息: {msg['type']}")
                
                # 接收响应
                response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"📨 收到响应: {response_data}")
                
            except Exception as e:
                print(f"❌ 发送JSON消息失败: {e}")
                
    async def send_invalid_json(self):
        """发送无效的JSON消息"""
        print("\n📤 测试发送无效JSON消息...")
        
        invalid_messages = [
            "这不是JSON",
            '{"invalid": json}',  # 缺少引号
            '{"type": "test", "incomplete":',  # 不完整的JSON
            "",  # 空字符串
            "null",  # 虽然是有效JSON，但不是对象
            '["array", "not", "object"]'  # 数组而不是对象
        ]
        
        for msg in invalid_messages:
            try:
                await self.websocket.send(msg)
                print(f"📤 发送无效消息: {msg[:50]}...")
                
                # 接收错误响应
                response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"📨 收到错误响应: {response_data}")
                
            except Exception as e:
                print(f"❌ 处理无效消息时异常: {e}")
                
    async def send_missing_fields(self):
        """发送缺少必要字段的消息"""
        print("\n📤 测试发送缺少字段的消息...")
        
        incomplete_messages = [
            {},  # 空对象
            {"type": "task_completion"},  # 缺少必要字段
            {"task_id": 1, "device_id": 1},  # 缺少type字段
            {"type": "unknown_type", "data": "test"}  # 未知消息类型
        ]
        
        for msg in incomplete_messages:
            try:
                await self.websocket.send(json.dumps(msg))
                print(f"📤 发送不完整消息: {msg}")
                
                # 接收错误响应
                response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"📨 收到错误响应: {response_data}")
                
            except Exception as e:
                print(f"❌ 处理不完整消息时异常: {e}")
                
    async def test_binary_message(self):
        """测试发送二进制消息"""
        print("\n📤 测试发送二进制消息...")
        
        try:
            # 发送二进制数据
            binary_data = b"This is binary data"
            await self.websocket.send(binary_data)
            print(f"📤 发送二进制消息: {binary_data}")
            
            # 接收错误响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5)
            response_data = json.loads(response)
            print(f"📨 收到错误响应: {response_data}")
            
        except Exception as e:
            print(f"❌ 处理二进制消息时异常: {e}")

async def test_message_handling():
    """测试消息处理功能"""
    print("🧪 测试WebSocket消息处理...")
    
    tester = WebSocketMessageTester("test_message_device")
    
    try:
        # 连接到服务器
        if not await tester.connect():
            return False
            
        # 运行各种测试
        await tester.send_valid_json()
        await asyncio.sleep(1)
        
        await tester.send_invalid_json()
        await asyncio.sleep(1)
        
        await tester.send_missing_fields()
        await asyncio.sleep(1)
        
        await tester.test_binary_message()
        await asyncio.sleep(1)
        
        print("\n✅ 所有消息处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def test_connection_stability():
    """测试连接稳定性"""
    print("\n🧪 测试连接稳定性...")
    
    tester = WebSocketMessageTester("test_stability_device")
    
    try:
        if not await tester.connect():
            return False
            
        # 发送大量消息测试稳定性
        print("📤 发送大量消息测试稳定性...")
        
        for i in range(10):
            # 交替发送有效和无效消息
            if i % 2 == 0:
                msg = {
                    "type": "heartbeat",
                    "timestamp": f"2024-01-01T12:00:{i:02d}Z",
                    "sequence": i
                }
                await tester.websocket.send(json.dumps(msg))
            else:
                # 发送无效消息
                await tester.websocket.send(f"invalid message {i}")
                
            # 尝试接收响应
            try:
                response = await asyncio.wait_for(tester.websocket.recv(), timeout=2)
                response_data = json.loads(response)
                print(f"📨 消息 {i}: {response_data.get('type', 'unknown')}")
            except asyncio.TimeoutError:
                print(f"⏰ 消息 {i}: 响应超时")
            except Exception as e:
                print(f"❌ 消息 {i}: 处理异常 {e}")
                
            await asyncio.sleep(0.1)
            
        print("✅ 连接稳定性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 稳定性测试异常: {e}")
        return False
    finally:
        await tester.disconnect()

async def main():
    """主测试函数"""
    print("🚀 WebSocket消息处理修复测试开始...\n")
    
    tests = [
        ("消息处理测试", test_message_handling),
        ("连接稳定性测试", test_connection_stability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        print("\n⏸️ 等待3秒后继续...")
        await asyncio.sleep(3)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！WebSocket消息处理修复成功")
        print("\n📋 修复要点:")
        print("1. ✅ 修复了JSON解析错误处理")
        print("2. ✅ 支持文本和二进制消息类型检测")
        print("3. ✅ 改进了错误响应机制")
        print("4. ✅ 增强了消息格式验证")
        
        print("\n🔧 关键改进:")
        print("- 使用 websocket.receive() 替代 receive_json()")
        print("- 先检查消息类型，再尝试JSON解析")
        print("- 提供详细的错误信息和调试信息")
        print("- 支持各种异常情况的优雅处理")
        
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⌨️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
