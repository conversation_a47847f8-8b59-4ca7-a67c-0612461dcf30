#!/usr/bin/env python3
"""
检查WebSocket通信问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime

def analyze_websocket_logs():
    """分析WebSocket日志中的问题"""
    print("🔍 分析WebSocket通信问题...")
    print("="*50)
    
    # 从您提供的日志分析问题
    log_analysis = {
        "发送的任务": {
            "task_id": 2,
            "type": "page_sign", 
            "parameters": {
                "like_id": "5173868756730165",
                "blogger_id": "1752510323"
            }
        },
        "收到的消息": [
            "new Date-Sat Jun 07 2025 13:24:49 GMT+0800 (GMT+08:00)",
            "2025-06-07T05:24:51.726Z",
            "new Date-Sat Jun 07 2025 13:24:51 GMT+0800 (GMT+08:00)",
            "test... (二进制)",
            "new Date-Sat Jun 07 2025 13:24:53 GMT+0800 (GMT+08:00)"
        ]
    }
    
    print("📋 日志分析结果:")
    print(f"✅ 任务发送成功: {log_analysis['发送的任务']}")
    print(f"❌ 只收到心跳消息，没有任务完成回复")
    print(f"📱 设备发送的消息: {log_analysis['收到的消息']}")
    
    print("\n🔍 问题诊断:")
    problems = [
        "设备端可能没有正确解析任务消息",
        "设备端没有发送task_completion消息",
        "消息格式可能不匹配",
        "设备端可能在处理任务时出错但没有报告"
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"   {i}. {problem}")
        
    return log_analysis

def check_message_formats():
    """检查消息格式要求"""
    print("\n📝 检查消息格式要求...")
    
    print("🔄 服务器期望的消息格式:")
    
    # 心跳消息格式
    heartbeat_format = {
        "type": "heartbeat",  # 或 msg_type
        "timestamp": "ISO格式时间戳",
        "device_number": "设备编号(可选)"
    }
    print(f"💓 心跳消息: {json.dumps(heartbeat_format, indent=2, ensure_ascii=False)}")
    
    # 任务完成消息格式
    task_completion_format = {
        "type": "task_completion",  # 或 msg_type
        "task_id": "任务ID(必需)",
        "status": "success 或 failed(必需)",
        "result": {
            "message": "执行结果描述",
            "details": "详细信息"
        }
    }
    print(f"✅ 任务完成消息: {json.dumps(task_completion_format, indent=2, ensure_ascii=False)}")
    
    print("\n⚠️ 常见问题:")
    common_issues = [
        "使用了错误的字段名 (type vs msg_type)",
        "缺少必需字段 task_id 或 status", 
        "status 值不是 'success' 或 'failed'",
        "消息不是有效的JSON格式",
        "设备端没有发送任务完成消息"
    ]
    
    for i, issue in enumerate(common_issues, 1):
        print(f"   {i}. {issue}")

def check_backend_status():
    """检查后端状态"""
    print("\n🖥️ 检查后端状态...")
    
    try:
        # 检查基本连接
        response = requests.get("http://localhost:8000/ip", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
        else:
            print(f"⚠️ 后端响应异常: {response.status_code}")
            
        # 检查设备状态
        response = requests.get("http://localhost:8000/devices/", timeout=5)
        if response.status_code == 200:
            devices = response.json()
            print(f"📱 设备数量: {len(devices)}")
            
            # 检查在线设备
            online_devices = [d for d in devices if d.get('online_status') == 'online']
            print(f"🟢 在线设备: {len(online_devices)}")
            
            if online_devices:
                print("   在线设备列表:")
                for device in online_devices[:3]:
                    print(f"     - {device.get('device_number')} ({device.get('device_ip')})")
            else:
                print("❌ 没有在线设备")
                
        # 检查任务状态
        response = requests.get("http://localhost:8000/tasks/", timeout=5)
        if response.status_code == 200:
            tasks = response.json()
            print(f"📋 任务数量: {len(tasks)}")
            
            # 统计任务状态
            status_counts = {}
            for task in tasks:
                status = task.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
                
            print("   任务状态统计:")
            for status, count in status_counts.items():
                print(f"     {status}: {count}")
                
    except Exception as e:
        print(f"❌ 后端检查失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议解决方案:")
    print("="*50)
    
    solutions = [
        {
            "问题": "设备端没有发送任务完成消息",
            "解决方案": [
                "检查设备端代码是否正确处理任务消息",
                "确保设备端在任务完成后发送task_completion消息",
                "检查设备端的WebSocket连接是否稳定"
            ]
        },
        {
            "问题": "消息格式不正确",
            "解决方案": [
                "确保使用正确的字段名: type 或 msg_type",
                "任务完成消息必须包含 task_id 和 status",
                "status 值必须是 'success' 或 'failed'",
                "消息必须是有效的JSON格式"
            ]
        },
        {
            "问题": "设备端处理任务时出错",
            "解决方案": [
                "添加设备端错误处理和日志",
                "即使任务失败也要发送task_completion消息",
                "在设备端添加调试输出"
            ]
        },
        {
            "问题": "WebSocket连接问题",
            "解决方案": [
                "检查网络连接稳定性",
                "确保WebSocket连接没有被防火墙阻止",
                "检查设备端WebSocket客户端实现"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['问题']}")
        for j, step in enumerate(solution['解决方案'], 1):
            print(f"   {j}) {step}")

def create_test_device_code():
    """创建测试设备端代码示例"""
    print("\n📝 创建测试设备端代码示例...")
    
    device_code = '''
// 正确的设备端WebSocket处理示例
const ws = new WebSocket('ws://localhost:8000/ws/test_device');

ws.onopen = function() {
    console.log('WebSocket连接已建立');
    
    // 发送心跳
    setInterval(() => {
        const heartbeat = {
            type: 'heartbeat',
            timestamp: new Date().toISOString(),
            device_number: 'test_device'
        };
        ws.send(JSON.stringify(heartbeat));
    }, 3000);
};

ws.onmessage = function(event) {
    try {
        const data = JSON.parse(event.data);
        console.log('收到消息:', data);
        
        // 检查是否是任务消息
        if (data.task_id && data.type) {
            handleTask(data);
        }
    } catch (error) {
        console.error('解析消息失败:', error);
    }
};

function handleTask(taskData) {
    console.log('处理任务:', taskData);
    
    const taskId = taskData.task_id;
    const taskType = taskData.type;
    const parameters = taskData.parameters;
    
    // 模拟任务处理
    setTimeout(() => {
        // 发送任务完成消息
        const completion = {
            type: 'task_completion',
            task_id: taskId,
            status: 'success',  // 或 'failed'
            result: {
                message: '任务执行成功',
                execution_time: 2.0,
                timestamp: new Date().toISOString()
            }
        };
        
        ws.send(JSON.stringify(completion));
        console.log('任务完成消息已发送:', completion);
    }, 2000);
}
'''
    
    with open('test_device_example.js', 'w', encoding='utf-8') as f:
        f.write(device_code)
        
    print("✅ 测试设备端代码已保存到 test_device_example.js")

def main():
    """主函数"""
    print("🔧 WebSocket通信问题诊断工具")
    print("="*50)
    
    # 1. 分析日志
    analyze_websocket_logs()
    
    # 2. 检查消息格式
    check_message_formats()
    
    # 3. 检查后端状态
    check_backend_status()
    
    # 4. 建议解决方案
    suggest_solutions()
    
    # 5. 创建测试代码
    create_test_device_code()
    
    print("\n" + "="*50)
    print("🎯 总结:")
    print("主要问题: 设备端没有发送正确格式的任务完成消息")
    print("解决方案: 确保设备端在收到任务后发送task_completion消息")
    print("测试工具: 使用 debug_websocket.py 进行调试")
    print("参考代码: 查看 test_device_example.js")

if __name__ == "__main__":
    main()
