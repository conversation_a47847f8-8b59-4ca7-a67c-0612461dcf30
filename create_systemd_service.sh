#!/bin/bash

# 创建系统服务实现永久启动

echo "=== 微博任务管理系统 - 系统服务配置 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    echo "使用方法: sudo $0"
    exit 1
fi

# 获取当前目录和用户
CURRENT_DIR=$(pwd)
CURRENT_USER=$(logname 2>/dev/null || echo $SUDO_USER)

if [ -z "$CURRENT_USER" ]; then
    log_error "无法确定当前用户"
    exit 1
fi

log_info "当前目录: $CURRENT_DIR"
log_info "当前用户: $CURRENT_USER"

# 检查必要文件
if [ ! -f "app/main.py" ]; then
    log_error "未找到 app/main.py，请在项目根目录运行此脚本"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    log_error "未找到虚拟环境，请先创建虚拟环境"
    exit 1
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    log_warn "未找到.env文件，将创建默认配置"
    cat > .env << 'EOF'
# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=wb

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
EOF
    chown $CURRENT_USER:$CURRENT_USER .env
fi

# 创建systemd服务文件
log_info "创建systemd服务文件..."

cat > /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博任务管理系统
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=$CURRENT_DIR/.env
ExecStart=$CURRENT_DIR/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=wb-system

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$CURRENT_DIR

[Install]
WantedBy=multi-user.target
EOF

# 创建日志目录
log_info "创建日志目录..."
mkdir -p logs
chown $CURRENT_USER:$CURRENT_USER logs

# 重载systemd配置
log_info "重载systemd配置..."
systemctl daemon-reload

# 启用服务
log_info "启用服务..."
systemctl enable wb-system.service

log_info "✅ 系统服务配置完成！"
echo ""
echo "🎉 微博任务管理系统已配置为系统服务"
echo "📍 服务名称: wb-system"
echo "📍 配置文件: /etc/systemd/system/wb-system.service"
echo "📍 工作目录: $CURRENT_DIR"
echo ""
echo "管理命令:"
echo "  启动服务: systemctl start wb-system"
echo "  停止服务: systemctl stop wb-system"
echo "  重启服务: systemctl restart wb-system"
echo "  查看状态: systemctl status wb-system"
echo "  查看日志: journalctl -u wb-system -f"
echo "  禁用服务: systemctl disable wb-system"
echo ""
echo "开机自启: 已启用"
echo ""

# 询问是否立即启动
read -p "是否立即启动服务？(Y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Nn]$ ]]; then
    log_info "服务已配置但未启动，可以手动启动: systemctl start wb-system"
else
    log_info "启动服务..."
    systemctl start wb-system
    
    # 等待启动
    sleep 3
    
    # 检查状态
    if systemctl is-active --quiet wb-system; then
        log_info "✅ 服务启动成功！"
        echo ""
        echo "🌐 访问地址: http://43.138.50.5:8000"
        echo "📖 API文档: http://43.138.50.5:8000/docs"
        echo ""
        echo "查看实时日志: journalctl -u wb-system -f"
    else
        log_error "❌ 服务启动失败"
        echo "查看错误日志: journalctl -u wb-system --no-pager"
    fi
fi
