#!/usr/bin/env python3
"""
测试前端功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_create_group():
    """测试创建分组"""
    print("测试创建分组...")
    
    group_data = {
        "group_name": "测试分组",
        "description": "这是一个测试分组"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/groups/", json=group_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 分组创建成功:")
            print(f"   分组ID: {result.get('id')}")
            print(f"   分组名称: {result.get('group_name')}")
            print(f"   描述: {result.get('description')}")
            return result.get('id')
        else:
            print(f"❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_groups():
    """测试获取分组列表"""
    print("\n测试获取分组列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/groups/", timeout=10)
        
        if response.status_code == 200:
            groups = response.json()
            print(f"✅ 获取分组成功，共 {len(groups)} 个分组:")
            
            for group in groups:
                print(f"   分组ID: {group.get('id')}")
                print(f"   分组名称: {group.get('group_name')}")
                print(f"   描述: {group.get('description')}")
                print(f"   创建时间: {group.get('create_time')}")
                print()
                
            return groups
        else:
            print(f"❌ 获取失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_create_inex_task():
    """测试创建inex任务"""
    print("测试创建inex任务...")
    
    task_data = {
        "task_type": "inex",
        "parameters": {
            "user_id": "test_user_456",
            "count": 2
        },
        "target_scope": "single",
        "target_id": 1,
        "delay_group": 1500,
        "delay_like": 800
    }
    
    try:
        response = requests.post(f"{BASE_URL}/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ inex任务创建成功:")
            print(f"   任务ID: {result.get('id')}")
            print(f"   任务类型: {result.get('task_type')}")
            print(f"   参数: {result.get('parameters')}")
            print(f"   目标范围: {result.get('target_scope')}")
            print(f"   目标ID: {result.get('target_id')}")
            print(f"   状态: {result.get('status')}")
            return result.get('id')
        else:
            print(f"❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_devices():
    """测试获取设备列表"""
    print("\n测试获取设备列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/devices/", timeout=10)
        
        if response.status_code == 200:
            devices = response.json()
            print(f"✅ 获取设备成功，共 {len(devices)} 个设备:")
            
            # 只显示前3个设备
            for device in devices[:3]:
                print(f"   设备ID: {device.get('id')}")
                print(f"   设备编号: {device.get('device_number')}")
                print(f"   设备IP: {device.get('device_ip')}")
                print(f"   在线状态: {device.get('online_status')}")
                print()
                
            return devices
        else:
            print(f"❌ 获取失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_get_tasks():
    """测试获取任务列表"""
    print("\n测试获取任务列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/tasks/", timeout=10)
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取任务成功，共 {len(tasks)} 个任务:")
            
            # 统计各类型任务数量
            task_types = {}
            for task in tasks:
                task_type = task.get('task_type')
                task_types[task_type] = task_types.get(task_type, 0) + 1
                
            print("   任务类型统计:")
            for task_type, count in task_types.items():
                print(f"     {task_type}: {count} 个")
                
            # 显示最近的3个任务
            print("\n   最近的任务:")
            for task in tasks[-3:]:
                print(f"     任务ID: {task.get('id')}")
                print(f"     类型: {task.get('task_type')}")
                print(f"     状态: {task.get('status')}")
                print(f"     参数: {task.get('parameters')}")
                print()
                
            return tasks
        else:
            print(f"❌ 获取失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def main():
    """主函数"""
    print("🧪 测试前端功能...")
    print("="*60)
    
    # 1. 测试获取设备列表
    devices = test_get_devices()
    
    # 2. 测试获取分组列表
    groups = test_get_groups()
    
    # 3. 测试创建分组
    new_group_id = test_create_group()
    
    # 4. 测试获取任务列表
    tasks = test_get_tasks()
    
    # 5. 测试创建inex任务
    new_task_id = test_create_inex_task()
    
    print("\n" + "="*60)
    print("📊 测试总结:")
    print(f"   设备数量: {len(devices)}")
    print(f"   分组数量: {len(groups)}")
    print(f"   任务数量: {len(tasks)}")
    print(f"   新建分组ID: {new_group_id}")
    print(f"   新建任务ID: {new_task_id}")
    
    if new_group_id and new_task_id:
        print("\n🎉 所有功能测试通过！")
        print("\n💡 前端功能说明:")
        print("1. 分组管理: 可以创建、编辑、删除分组")
        print("2. 任务管理: 支持所有任务类型包括inex")
        print("3. 设备管理: 显示设备状态和信息")
        print("4. 实时监控: WebSocket连接状态")
    else:
        print("\n⚠️ 部分功能可能存在问题")

if __name__ == "__main__":
    main()
