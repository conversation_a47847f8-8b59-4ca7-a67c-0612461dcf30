#!/usr/bin/env python3
"""
系统状态测试
检查调度器和任务状态
"""

import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def check_api_status():
    """检查API状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API正常运行")
            return True
        else:
            log_with_time(f"❌ API状态异常: {response.status_code}")
            return False
    except Exception as e:
        log_with_time(f"❌ API连接失败: {e}")
        return False

def check_task_status():
    """检查任务状态"""
    try:
        # 检查任务251和252
        for task_id in [251, 252]:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                log_with_time(f"任务{task_id}: {status_data}")
            else:
                log_with_time(f"❌ 获取任务{task_id}状态失败: {response.status_code}")
    except Exception as e:
        log_with_time(f"❌ 检查任务状态失败: {e}")

def create_simple_task():
    """创建一个简单任务测试"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": "系统状态测试",
            "like_id": "test123"
        },
        "target_scope": "group",
        "target_id": 1,
        "delay_group": 3000,  # 3秒分组延迟
        "delay_like": 1000    # 1秒操作延迟
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            task = response.json()
            creation_time = (end_time - start_time) * 1000
            log_with_time(f"✅ 测试任务创建成功，ID: {task.get('id')}, 耗时: {creation_time:.1f}ms")
            return task
        else:
            log_with_time(f"❌ 测试任务创建失败: {response.status_code}")
            log_with_time(f"响应: {response.text}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建测试任务失败: {e}")
        return None

def check_database_status():
    """检查数据库状态"""
    try:
        import sys
        sys.path.append('.')
        from app.db import get_db
        from app.models.task import Task, TaskQueue
        from app.models.device import Device
        
        db = next(get_db())
        try:
            # 检查设备数量
            devices = db.query(Device).all()
            log_with_time(f"数据库中设备数量: {len(devices)}")
            
            # 检查在线设备
            online_devices = [d for d in devices if d.online_status]
            log_with_time(f"在线设备数量: {len(online_devices)}")
            
            # 检查pending任务
            pending_tasks = db.query(TaskQueue).filter(TaskQueue.status == 'pending').count()
            log_with_time(f"pending任务数量: {pending_tasks}")
            
            # 检查running任务
            running_tasks = db.query(TaskQueue).filter(TaskQueue.status == 'running').count()
            log_with_time(f"running任务数量: {running_tasks}")
            
            # 检查最近任务
            recent_tasks = db.query(Task).order_by(Task.id.desc()).limit(3).all()
            log_with_time("最近3个任务:")
            for task in recent_tasks:
                test_name = task.parameters.get('test_name', '未知') if task.parameters else '未知'
                log_with_time(f"  任务{task.id}: {test_name}, 状态={task.status}")
            
        finally:
            db.close()
            
    except Exception as e:
        log_with_time(f"❌ 检查数据库状态失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 系统状态检查")
    print("=" * 50)
    
    # 1. 检查API状态
    log_with_time("=== 检查API状态 ===")
    if not check_api_status():
        return
    
    # 2. 检查数据库状态
    log_with_time("=== 检查数据库状态 ===")
    check_database_status()
    
    # 3. 检查现有任务状态
    log_with_time("=== 检查现有任务状态 ===")
    check_task_status()
    
    # 4. 创建测试任务
    log_with_time("=== 创建测试任务 ===")
    test_task = create_simple_task()
    
    if test_task:
        # 等待5秒观察任务状态变化
        log_with_time("⏳ 等待5秒观察任务状态变化...")
        time.sleep(5)
        
        task_id = test_task.get('id')
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                log_with_time(f"测试任务{task_id}状态: {status_data}")
            else:
                log_with_time(f"❌ 获取测试任务状态失败")
        except Exception as e:
            log_with_time(f"❌ 检查测试任务状态失败: {e}")
    
    print("\n" + "=" * 50)
    print("📋 系统状态检查完成")
    print("💡 如果任务一直pending，可能的原因:")
    print("   1. 调度器没有正确启动")
    print("   2. 设备没有在线")
    print("   3. 任务分发逻辑有问题")
    print("=" * 50)

if __name__ == "__main__":
    main()
