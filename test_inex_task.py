#!/usr/bin/env python3
"""
测试inex任务类型
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_inex_task_creation():
    """测试创建inex任务"""
    print("测试创建inex任务...")
    
    # 创建inex任务的数据
    task_data = {
        "task_type": "inex",
        "parameters": {
            "user_id": "test_user_123",
            "count": 1
        },
        "target_scope": "single",
        "target_id": 1,
        "delay_group": 1000,
        "delay_like": 500
    }
    
    try:
        response = requests.post(f"{BASE_URL}/tasks/", json=task_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ inex任务创建成功!")
            print(f"   任务ID: {result.get('id')}")
            print(f"   任务类型: {result.get('task_type')}")
            print(f"   参数: {result.get('parameters')}")
            print(f"   状态: {result.get('status')}")
            return result.get('id')
        else:
            print(f"❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_task(task_id):
    """测试获取单个任务"""
    print(f"\n测试获取任务 {task_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/tasks/{task_id}", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取任务成功!")
            print(f"   任务ID: {result.get('id')}")
            print(f"   任务类型: {result.get('task_type')}")
            print(f"   参数: {result.get('parameters')}")
            print(f"   状态: {result.get('status')}")
            return True
        else:
            print(f"❌ 获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_task_status(task_id):
    """测试获取任务状态"""
    print(f"\n测试获取任务状态 {task_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/tasks/{task_id}/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取任务状态成功!")
            print(f"   任务ID: {result.get('task_id')}")
            print(f"   状态: {result.get('status')}")
            print(f"   队列信息: {result.get('queues')}")
            return True
        else:
            print(f"❌ 获取状态失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_all_task_types():
    """测试所有任务类型"""
    print("\n测试所有任务类型...")
    
    task_types = [
        {
            "task_type": "sign",
            "parameters": {"count": 1}
        },
        {
            "task_type": "like", 
            "parameters": {"count": 1, "blogger_id": "123", "like_id": "456"}
        },
        {
            "task_type": "page_sign",
            "parameters": {"page_url": "https://example.com"}
        },
        {
            "task_type": "inex",
            "parameters": {"user_id": "test_user", "count": 1}
        }
    ]
    
    success_count = 0
    
    for task_config in task_types:
        task_data = {
            **task_config,
            "target_scope": "single",
            "target_id": 1,
            "delay_group": 1000,
            "delay_like": 500
        }
        
        try:
            response = requests.post(f"{BASE_URL}/tasks/", json=task_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {task_config['task_type']} 任务创建成功 (ID: {result.get('id')})")
                success_count += 1
            else:
                print(f"❌ {task_config['task_type']} 任务创建失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {task_config['task_type']} 任务创建异常: {e}")
    
    print(f"\n任务类型测试结果: {success_count}/{len(task_types)} 成功")
    return success_count == len(task_types)

def main():
    """主函数"""
    print("🧪 测试inex任务类型功能...")
    print("="*60)
    
    # 1. 测试创建inex任务
    task_id = test_inex_task_creation()
    
    if task_id:
        # 2. 测试获取任务
        test_get_task(task_id)
        
        # 3. 测试获取任务状态
        test_task_status(task_id)
    
    # 4. 测试所有任务类型
    test_all_task_types()
    
    print("\n" + "="*60)
    print("🎉 inex任务类型测试完成!")
    print("\n💡 前端使用说明:")
    print("1. 在任务管理页面选择 '主页关注任务'")
    print("2. 输入用户ID和关注次数")
    print("3. 选择目标设备或分组")
    print("4. 点击创建任务")

if __name__ == "__main__":
    main()
