设备任务控制系统 — REST API 接口文档

1. 通用说明
- 协议：HTTP/HTTPS
- 返回格式：JSON
- 服务器地址示例：http://your-server-ip:8000
- 身份认证：无 token，开放接口
- 状态码：使用标准 HTTP 状态码

2. 设备管理接口 /api/devices

2.1 获取设备列表
- GET /api/devices/
- 返回示例：
[
  {
    "id": 1,
    "device_number": "device001",
    "device_ip": "************",
    "online_status": "online",
    "account_status": "normal",
    "group_ids": [1, 2]
  }
]

2.2 
- POST /api/devices/
- 请求示例：
{
  "device_number": "device001",
  "device_ip": "************"
}
- 返回示例：
{
  "message": "Device registered",
  "device_id": 1
}

2.3 修改设备信息
- PUT /api/devices/{device_id}
- 请求示例：
{
  "device_number": "device001-new",
  "device_ip": "************"
}
- 返回示例：
{
  "message": "Device updated"
}

2.4 删除设备
- DELETE /api/devices/{device_id}
- 返回示例：
{
  "message": "Device deleted"
}

3. 分组管理接口 /api/groups

3.1 获取所有分组
- GET /api/groups/
- 返回示例：
[
  {
    "id": 1,
    "group_name": "Group A",
    "description": "高优先级分组",
    "device_ids": [1, 2]
  }
]

3.2 创建分组
- POST /api/groups/
- 请求示例：
{
  "group_name": "Group A",
  "description": "高优先级"
}
- 返回示例：
{
  "message": "Group created",
  "group_id": 1
}

3.3 修改分组
- PUT /api/groups/{group_id}
- 请求示例：
{
  "group_name": "Group A New",
  "description": "更新描述"
}
- 返回示例：
{
  "message": "Group updated"
}

3.4 删除分组
- DELETE /api/groups/{group_id}
- 返回示例：
{
  "message": "Group deleted"
}

3.5 添加设备到分组
- POST /api/groups/{group_id}/devices
- 请求示例：
{
  "device_ids": [1, 3, 5]
}
- 返回示例：
{
  "message": "Devices added to group"
}

3.6 从分组移除设备
- DELETE /api/groups/{group_id}/devices
- 请求示例：
{
  "device_ids": [1]
}
- 返回示例：
{
  "message": "Devices removed from group"
}

4. 任务管理接口 /api/tasks

4.1 创建任务
- POST /api/tasks/
- 请求示例：
{
  "task_type": "like",
  "parameters": {
    "blogger_id": "abc123"
  },
  "target_scope": "group",
  "target_id": 2,
  "delay_group": 3,
  "delay_like": 1
}
- 返回示例：
{
  "message": "Task created",
  "task_id": 5
}

4.2 获取任务列表
- GET /api/tasks/
- 支持按状态过滤，如 ?status=pending

4.3 获取任务详情
- GET /api/tasks/{task_id}

4.4 删除任务
- DELETE /api/tasks/{task_id}

4.5 暂停任务
- POST /api/tasks/{task_id}/pause

4.6 继续任务
- POST /api/tasks/{task_id}/resume

5. 任务队列查询 /api/task_queue

5.1 查询设备任务队列
- GET /api/task_queue?device_id={device_id}
- 返回设备待执行及执行中任务详情

6. WebSocket接口

6.1 设备连接 /ws/device
- 设备连接后自动注册
- 接收任务指令
- 发送心跳包和任务执行反馈

6.2 前端连接 /ws/frontend
- 实时接收设备状态、日志推送
- 发送控制命令（如刷新设备状态）

7. 返回格式说明

7.1 成功示例
{
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}

7.2 失败示例
{
  "code": 400,
  "message": "参数错误"
}

备注：
- 所有时间均为 UTC 时间
- target_scope 可选值：single（单设备），group（设备组），all（全部设备）
- 参数校验错误会返回 400 状态码及错误信息
- 版本：1.0
- 编写日期：2025-06-03
