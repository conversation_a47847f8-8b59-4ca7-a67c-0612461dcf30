"""
配置文件
"""

class Config:
    # API服务器配置 - 默认值，实际使用时会被动态配置覆盖
    API_BASE_URL = "http://localhost:8000"
    WEBSOCKET_URL = "ws://localhost:8000"

    @staticmethod
    def get_api_base_url():
        """获取动态配置的API基础URL"""
        try:
            from utils.config_manager import config_manager
            return config_manager.get_api_base_url()
        except ImportError:
            return Config.API_BASE_URL

    @staticmethod
    def get_websocket_url():
        """获取动态配置的WebSocket URL"""
        try:
            from utils.config_manager import config_manager
            return config_manager.get_websocket_url()
        except ImportError:
            return Config.WEBSOCKET_URL
    
    # 刷新间隔配置（秒）
    AUTO_REFRESH_INTERVAL = 5
    CONNECTION_CHECK_INTERVAL = 3
    HEARTBEAT_CHECK_INTERVAL = 10
    
    # 界面配置
    WINDOW_WIDTH = 1400
    WINDOW_HEIGHT = 900
    MIN_WIDTH = 1200
    MIN_HEIGHT = 800
    
    # 数据表格配置
    ITEMS_PER_PAGE = 50
    MAX_LOG_LINES = 1000
    
    # 任务类型配置
    TASK_TYPES = {
        "sign": "签到任务",
        "like": "点赞任务",
        "page_sign": "超话签到任务",
        "inex": "主页关注任务"
    }
    
    # 设备状态配置
    DEVICE_STATUS = {
        "online": "在线",
        "offline": "离线"
    }
    
    ACCOUNT_STATUS = {
        "normal": "正常",
        "not_logged_in": "未登录",
        "abnormal": "异常"
    }
    
    # 任务状态配置
    TASK_STATUS = {
        "pending": "等待中",
        "running": "运行中",
        "paused": "已暂停",
        "done": "已完成",
        "failed": "失败",
        "cancelled": "已取消"
    }
    
    TASK_QUEUE_STATUS = {
        "pending": "等待中",
        "running": "运行中", 
        "done": "已完成",
        "failed": "失败"
    }
    
    # 目标范围配置
    TARGET_SCOPE = {
        "single": "单个设备",
        "group": "设备分组",
        "all": "所有设备"
    }
    
    # 颜色配置
    COLORS = {
        "online": "#28a745",      # 绿色 - 在线
        "offline": "#dc3545",     # 红色 - 离线
        "running": "#007bff",     # 蓝色 - 运行中
        "pending": "#ffc107",     # 黄色 - 等待中
        "done": "#28a745",        # 绿色 - 完成
        "failed": "#dc3545",      # 红色 - 失败
        "paused": "#6c757d"       # 灰色 - 暂停
    }
    
    # API端点配置
    API_ENDPOINTS = {
        "devices": "/devices",
        "tasks": "/tasks",
        "groups": "/groups",
        "websocket": "/ws"
    }
    
    # 默认参数配置
    DEFAULT_TASK_PARAMS = {
        "sign": {"count": 1},
        "like": {"blogger_id": "", "like_id": ""},
        "page_sign": {"page_url": ""},
        "inex": {"user_id": "", "count": 1}
    }
    
    # 超时配置（秒）
    REQUEST_TIMEOUT = 10
    WEBSOCKET_TIMEOUT = 30
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 文件路径配置
    EXPORT_DIR = "exports"
    LOG_DIR = "logs"
    CONFIG_FILE = "config.json"
