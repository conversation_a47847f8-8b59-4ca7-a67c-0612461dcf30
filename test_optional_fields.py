#!/usr/bin/env python3
"""
测试可选字段功能
"""

import requests
import json

def test_task_creation_with_optional_fields():
    """测试带可选字段的任务创建"""
    print("🧪 测试可选字段任务创建...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取分组
        response = requests.get(f"{base_url}/groups/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取分组数据: {response.status_code}")
            return False
            
        groups = response.json()
        if not groups:
            print("⚠️ 没有可用的分组")
            return True
            
        test_group = groups[0]
        group_id = test_group.get('id')
        group_name = test_group.get('group_name', '测试分组')
        
        print(f"📋 使用分组: {group_name} (ID: {group_id})")
        
        # 测试场景
        test_cases = [
            {
                "name": "签到任务 - 无博主ID和点赞ID",
                "task_type": "sign",
                "parameters": {
                    "delay_click": 500
                }
            },
            {
                "name": "点赞任务 - 只有博主ID",
                "task_type": "like",
                "parameters": {
                    "blogger_id": "optional_test_blogger",
                    "delay_click": 500
                }
            },
            {
                "name": "点赞任务 - 只有点赞ID",
                "task_type": "like",
                "parameters": {
                    "like_id": "optional_test_like",
                    "delay_click": 500
                }
            },
            {
                "name": "点赞任务 - 无博主ID和点赞ID",
                "task_type": "like",
                "parameters": {
                    "delay_click": 500
                }
            },
            {
                "name": "超话签到 - 只有博主ID",
                "task_type": "page_sign",
                "parameters": {
                    "blogger_id": "optional_page_blogger",
                    "delay_click": 500
                }
            },
            {
                "name": "主页关注 - 只有用户ID",
                "task_type": "inex",
                "parameters": {
                    "user_id": "optional_user_id",
                    "count": 1,
                    "delay_click": 500
                }
            },
            {
                "name": "主页关注 - 无用户ID",
                "task_type": "inex",
                "parameters": {
                    "count": 1,
                    "delay_click": 500
                }
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}: {test_case['name']}")
            
            task_data = {
                "task_type": test_case["task_type"],
                "parameters": test_case["parameters"],
                "target_scope": "group",
                "target_id": group_id,
                "delay_group": 2000,
                "delay_like": 1000
            }
            
            print(f"   参数: {json.dumps(test_case['parameters'], indent=6)}")
            
            response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 创建成功 - 任务ID: {result.get('id')}")
                success_count += 1
            else:
                print(f"   ❌ 创建失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count >= len(test_cases) * 0.8
        
    except Exception as e:
        print(f"❌ 可选字段测试异常: {e}")
        return False

def test_backend_parameter_handling():
    """测试后端参数处理"""
    print("\n🔧 测试后端参数处理...")
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取设备
        response = requests.get(f"{base_url}/devices/", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取设备数据: {response.status_code}")
            return False
            
        devices = response.json()
        if not devices:
            print("⚠️ 没有可用的设备")
            return True
            
        test_device = devices[0]
        device_id = test_device.get('id')
        device_number = test_device.get('device_number', '测试设备')
        
        print(f"📱 使用设备: {device_number} (ID: {device_id})")
        
        # 测试最小参数集
        minimal_task = {
            "task_type": "sign",
            "parameters": {
                "delay_click": 300
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 1000
        }
        
        print("📤 发送最小参数任务...")
        response = requests.post(f"{base_url}/tasks/", json=minimal_task, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 最小参数任务创建成功 - 任务ID: {result.get('id')}")
            
            # 验证参数存储
            stored_params = result.get('parameters', {})
            print(f"   存储的参数: {stored_params}")
            
            return True
        else:
            print(f"❌ 最小参数任务创建失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 后端参数处理测试异常: {e}")
        return False

def test_frontend_validation_logic():
    """测试前端验证逻辑"""
    print("\n🖥️ 测试前端验证逻辑...")
    
    try:
        # 检查新任务管理器文件
        import os
        if os.getcwd().endswith('frontend'):
            task_manager_file = "widgets/new_task_manager.py"
        else:
            task_manager_file = "frontend/widgets/new_task_manager.py"
        with open(task_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查可选字段相关功能
        optional_features = [
            "博主ID(可选)",
            "点赞ID(可选)",
            "on_task_type_changed",
            "update_task_hint",
            "根据任务类型验证必要字段",
            "根据任务类型和输入情况"
        ]
        
        found_features = []
        for feature in optional_features:
            if feature in content:
                found_features.append(feature)
        
        print(f"✅ 可选字段功能: {len(found_features)}/{len(optional_features)}")
        for feature in found_features:
            print(f"   ✓ {feature}")
        
        return len(found_features) >= len(optional_features) * 0.8
        
    except Exception as e:
        print(f"❌ 前端验证逻辑测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 可选字段功能测试")
    print("="*60)
    
    tests = [
        ("前端验证逻辑", test_frontend_validation_logic),
        ("后端参数处理", test_backend_parameter_handling),
        ("可选字段任务创建", test_task_creation_with_optional_fields)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*60)
    print(f"📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests) * 0.8:
        print("🎉 可选字段功能完成！")
        
        print("\n✅ 可选字段特性:")
        print("• 📝 博主ID和点赞ID标记为可选")
        print("• 🔍 根据任务类型智能验证必要字段")
        print("• 💡 动态提示信息指导用户填写")
        print("• 🛠️ 灵活的参数构建逻辑")
        print("• 📊 支持最小参数集任务创建")
        
        print("\n💡 使用指南:")
        print("• 📋 签到任务：博主ID和点赞ID可不填")
        print("• ❤️ 点赞任务：建议填写博主ID和点赞ID")
        print("• 📄 超话签到：建议填写博主ID，点赞ID可选")
        print("• 👤 主页关注：建议填写博主ID，点赞ID可不填")
        
        print("\n🎯 智能验证:")
        print("• 🔍 只对必要字段进行验证")
        print("• 💡 实时提示当前任务类型的字段要求")
        print("• 🛠️ 根据输入情况动态构建参数")
        print("• 📊 支持空参数的任务类型")
        
    else:
        print("⚠️ 部分功能可能未完全实现，请检查相关功能")

if __name__ == "__main__":
    main()
