"""
分组管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading

class GroupManagerFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.groups_data = []
        self.devices_data = []
        
        self.create_widgets()
        self.refresh()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题和工具栏
        self.create_header()
        
        # 主要内容区域
        self.create_main_content()
        
    def create_header(self):
        """创建标题和工具栏"""
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 标题
        title_label = ttk.Label(header_frame, text="分组管理", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # 工具按钮
        btn_frame = ttk.Frame(header_frame)
        btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(btn_frame, text="🔄 刷新", command=self.refresh).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="➕ 创建分组", command=self.show_create_group_dialog).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="✏️ 编辑分组", command=self.show_edit_group_dialog).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="🗑️ 删除分组", command=self.delete_group).pack(side=tk.LEFT, padx=2)
        
    def create_main_content(self):
        """创建主要内容区域"""
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧分组列表
        left_frame = ttk.LabelFrame(main_frame, text="分组列表")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 分组树形控件
        self.group_tree = ttk.Treeview(left_frame, columns=('name', 'device_count', 'description'), show='headings', height=20)
        
        self.group_tree.heading('name', text='分组名称')
        self.group_tree.heading('device_count', text='设备数量')
        self.group_tree.heading('description', text='描述')
        
        self.group_tree.column('name', width=150)
        self.group_tree.column('device_count', width=80)
        self.group_tree.column('description', width=200)
        
        # 滚动条
        group_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.group_tree.yview)
        self.group_tree.configure(yscrollcommand=group_scrollbar.set)
        
        self.group_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        group_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.group_tree.bind('<<TreeviewSelect>>', self.on_group_selected)
        
        # 右侧设备列表
        right_frame = ttk.LabelFrame(main_frame, text="分组设备")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 设备操作按钮
        device_btn_frame = ttk.Frame(right_frame)
        device_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(device_btn_frame, text="➕ 添加设备", command=self.add_device_to_group).pack(side=tk.LEFT, padx=2)
        ttk.Button(device_btn_frame, text="➖ 移除设备", command=self.remove_device_from_group).pack(side=tk.LEFT, padx=2)
        ttk.Button(device_btn_frame, text="📋 批量任务", command=self.create_group_task).pack(side=tk.LEFT, padx=2)
        
        # 设备列表
        self.device_tree = ttk.Treeview(right_frame, columns=('device_number', 'device_ip', 'status'), show='headings', height=18)
        
        self.device_tree.heading('device_number', text='设备号')
        self.device_tree.heading('device_ip', text='IP地址')
        self.device_tree.heading('status', text='状态')
        
        self.device_tree.column('device_number', width=120)
        self.device_tree.column('device_ip', width=120)
        self.device_tree.column('status', width=80)
        
        # 设备列表滚动条
        device_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.device_tree.yview)
        self.device_tree.configure(yscrollcommand=device_scrollbar.set)
        
        self.device_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        device_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def refresh(self):
        """刷新数据"""
        def fetch_data():
            try:
                groups = self.api_client.get_groups()
                devices = self.api_client.get_devices()
                self.winfo_toplevel().after(0, lambda: self.update_display(groups, devices))
            except Exception as e:
                self.winfo_toplevel().after(0, lambda: messagebox.showerror("刷新失败", f"获取数据失败: {e}"))
                
        threading.Thread(target=fetch_data, daemon=True).start()
        
    def update_display(self, groups, devices):
        """更新显示"""
        self.groups_data = groups or []
        self.devices_data = devices or []
        
        # 更新分组列表
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)
            
        for group in self.groups_data:
            # 计算分组中的设备数量
            device_count = len([d for d in self.devices_data if d.get('group_id') == group.get('id')])

            values = (
                group.get('group_name', ''),  # 使用正确的字段名
                str(device_count),
                group.get('description', '')
            )

            self.group_tree.insert('', tk.END, values=values)
            
        # 清空设备列表
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
            
    def on_group_selected(self, event):
        """分组选择事件"""
        selection = self.group_tree.selection()
        if not selection:
            return
            
        item = selection[0]
        group_name = self.group_tree.item(item)['values'][0]
        
        # 查找分组数据
        selected_group = None
        for group in self.groups_data:
            if group.get('group_name') == group_name:  # 使用正确的字段名
                selected_group = group
                break
                
        if selected_group:
            self.update_device_list(selected_group)
            
    def update_device_list(self, group):
        """更新设备列表"""
        # 清空设备列表
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)
            
        # 添加分组中的设备
        group_devices = [d for d in self.devices_data if d.get('group_id') == group.get('id')]
        
        for device in group_devices:
            status_text = "🟢 在线" if device.get('online_status') == 'online' else "🔴 离线"
            
            values = (
                device.get('device_number', ''),
                device.get('device_ip', ''),
                status_text
            )
            
            self.device_tree.insert('', tk.END, values=values)
            
    def get_selected_group(self):
        """获取当前选中的分组"""
        selection = self.group_tree.selection()
        if not selection:
            return None
            
        item = selection[0]
        group_name = self.group_tree.item(item)['values'][0]
        
        for group in self.groups_data:
            if group.get('group_name') == group_name:  # 使用正确的字段名
                return group
        return None
        
    def get_selected_device(self):
        """获取当前选中的设备"""
        selection = self.device_tree.selection()
        if not selection:
            return None
            
        item = selection[0]
        device_number = self.device_tree.item(item)['values'][0]
        
        for device in self.devices_data:
            if device.get('device_number') == device_number:
                return device
        return None
        
    def show_create_group_dialog(self):
        """显示创建分组对话框"""
        CreateGroupDialog(self, self.api_client, self.refresh)
        
    def show_edit_group_dialog(self):
        """显示编辑分组对话框"""
        group = self.get_selected_group()
        if not group:
            messagebox.showwarning("未选择分组", "请先选择要编辑的分组")
            return
            
        EditGroupDialog(self, self.api_client, group, self.refresh)
        
    def delete_group(self):
        """删除分组"""
        group = self.get_selected_group()
        if not group:
            messagebox.showwarning("未选择分组", "请先选择要删除的分组")
            return

        if messagebox.askyesno("确认删除", f"确定要删除分组 '{group.get('group_name')}' 吗？\n分组内的设备将回归未分组状态。"):
            def delete():
                try:
                    result = self.api_client.delete_group(group['id'])
                    if result:
                        self.winfo_toplevel().after(0, lambda: messagebox.showinfo("操作成功", "分组已删除，设备已回归未分组状态"))
                        # 强制多次刷新确保界面更新
                        self.winfo_toplevel().after(0, self.refresh)
                        self.winfo_toplevel().after(500, self.refresh)
                        self.winfo_toplevel().after(1000, self.refresh)
                    else:
                        self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作失败", "删除分组失败"))
                except Exception as e:
                    self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作异常", f"删除分组时发生错误: {e}"))

            threading.Thread(target=delete, daemon=True).start()
            
    def add_device_to_group(self):
        """添加设备到分组"""
        group = self.get_selected_group()
        if not group:
            messagebox.showwarning("未选择分组", "请先选择分组")
            return
            
        AddDeviceDialog(self, self.api_client, group, self.devices_data, self.refresh)
        
    def remove_device_from_group(self):
        """从分组中移除设备"""
        group = self.get_selected_group()
        device = self.get_selected_device()

        if not group or not device:
            messagebox.showwarning("未选择", "请先选择分组和设备")
            return

        if messagebox.askyesno("确认移除", f"确定要从分组中移除设备 '{device.get('device_number')}' 吗？\n设备将回归未分组状态。"):
            def remove():
                try:
                    result = self.api_client.remove_device_from_group(group['id'], device['id'])
                    if result:
                        self.winfo_toplevel().after(0, lambda: messagebox.showinfo("操作成功", "设备已移除，回归未分组状态"))
                        # 强制刷新两次确保界面更新
                        self.winfo_toplevel().after(0, self.refresh)
                        self.winfo_toplevel().after(500, self.refresh)
                    else:
                        self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作失败", "移除设备失败"))
                except Exception as e:
                    self.winfo_toplevel().after(0, lambda: messagebox.showerror("操作异常", f"移除设备时发生错误: {e}"))

            threading.Thread(target=remove, daemon=True).start()
            
    def create_group_task(self):
        """创建分组任务"""
        group = self.get_selected_group()
        if not group:
            messagebox.showwarning("未选择分组", "请先选择分组")
            return
            
        # TODO: 实现分组任务创建
        messagebox.showinfo("功能开发中", f"为分组 '{group.get('group_name')}' 创建任务功能正在开发中")


class CreateGroupDialog:
    def __init__(self, parent, api_client, refresh_callback):
        self.parent = parent
        self.api_client = api_client
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("创建分组")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 分组名称
        ttk.Label(main_frame, text="分组名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(main_frame, width=30)
        self.name_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 描述
        ttk.Label(main_frame, text="描述:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        self.desc_text = tk.Text(main_frame, width=30, height=8)
        self.desc_text.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        ttk.Button(btn_frame, text="创建", command=self.create_group).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT, padx=5)
        
    def create_group(self):
        """创建分组"""
        name = self.name_entry.get().strip()
        description = self.desc_text.get(1.0, tk.END).strip()
        
        if not name:
            messagebox.showwarning("输入错误", "请输入分组名称")
            return
            
        def create():
            try:
                group_data = {
                    'group_name': name,  # 使用正确的字段名
                    'description': description
                }
                result = self.api_client.create_group(group_data)
                if result:
                    self.dialog.after(0, lambda: messagebox.showinfo("创建成功", "分组已创建"))
                    self.dialog.after(0, self.refresh_callback)  # 自动刷新
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("创建失败", "创建分组失败"))
            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("创建异常", f"创建分组时发生错误: {e}"))
                
        threading.Thread(target=create, daemon=True).start()


class EditGroupDialog:
    def __init__(self, parent, api_client, group, refresh_callback):
        self.parent = parent
        self.api_client = api_client
        self.group = group
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑分组")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        self.load_group_data()
        
    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 分组名称
        ttk.Label(main_frame, text="分组名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(main_frame, width=30)
        self.name_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 描述
        ttk.Label(main_frame, text="描述:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        self.desc_text = tk.Text(main_frame, width=30, height=8)
        self.desc_text.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        ttk.Button(btn_frame, text="保存", command=self.save_group).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT, padx=5)
        
    def load_group_data(self):
        """加载分组数据"""
        self.name_entry.insert(0, self.group.get('group_name', ''))  # 使用正确的字段名
        self.desc_text.insert(1.0, self.group.get('description', ''))
        
    def save_group(self):
        """保存分组"""
        name = self.name_entry.get().strip()
        description = self.desc_text.get(1.0, tk.END).strip()
        
        if not name:
            messagebox.showwarning("输入错误", "请输入分组名称")
            return
            
        def save():
            try:
                group_data = {
                    'group_name': name,  # 使用正确的字段名
                    'description': description
                }
                result = self.api_client.update_group(self.group['id'], group_data)
                if result:
                    self.dialog.after(0, lambda: messagebox.showinfo("保存成功", "分组已更新"))
                    self.dialog.after(0, self.refresh_callback)  # 自动刷新
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("保存失败", "更新分组失败"))
            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("保存异常", f"更新分组时发生错误: {e}"))
                
        threading.Thread(target=save, daemon=True).start()


class AddDeviceDialog:
    def __init__(self, parent, api_client, group, devices_data, refresh_callback):
        self.parent = parent
        self.api_client = api_client
        self.group = group
        self.devices_data = devices_data
        self.refresh_callback = refresh_callback
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加设备到分组")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        ttk.Label(main_frame, text=f"选择要添加到分组 '{self.group.get('group_name')}' 的设备:").pack(anchor=tk.W, pady=(0, 10))
        
        # 设备列表
        self.device_listbox = tk.Listbox(main_frame, selectmode=tk.MULTIPLE, height=15)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.device_listbox.yview)
        self.device_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.device_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 加载未分组的设备
        ungrouped_devices = [d for d in self.devices_data if not d.get('group_id') or d.get('group_id') != self.group.get('id')]
        
        for device in ungrouped_devices:
            display_text = f"{device.get('device_number', '')} - {device.get('device_ip', '')}"
            self.device_listbox.insert(tk.END, display_text)
            
        # 按钮
        btn_frame = ttk.Frame(self.dialog)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="添加", command=self.add_devices).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT, padx=5)
        
    def add_devices(self):
        """添加选中的设备"""
        selected_indices = self.device_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("未选择设备", "请选择要添加的设备")
            return

        def add_devices_to_group():
            try:
                success_count = 0
                total_count = len(selected_indices)

                # 获取未分组的设备列表
                ungrouped_devices = [d for d in self.devices_data if not d.get('group_id') or d.get('group_id') != self.group.get('id')]

                for index in selected_indices:
                    if index < len(ungrouped_devices):
                        device = ungrouped_devices[index]
                        device_id = device.get('id')
                        group_id = self.group.get('id')

                        # 调用API添加设备到分组
                        result = self.api_client.add_device_to_group(group_id, device_id)
                        if result:
                            success_count += 1

                # 在主线程中显示结果
                if success_count == total_count:
                    self.dialog.after(0, lambda: messagebox.showinfo("添加成功", f"成功添加 {success_count} 个设备到分组"))
                    self.dialog.after(0, self.refresh_callback)  # 自动刷新
                    self.dialog.after(0, self.dialog.destroy)
                elif success_count > 0:
                    self.dialog.after(0, lambda: messagebox.showwarning("部分成功", f"成功添加 {success_count}/{total_count} 个设备"))
                    self.dialog.after(0, self.refresh_callback)  # 自动刷新
                    self.dialog.after(0, self.refresh_callback)
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("添加失败", "没有设备添加成功"))

            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("添加异常", f"添加设备时发生错误: {e}"))

        threading.Thread(target=add_devices_to_group, daemon=True).start()
