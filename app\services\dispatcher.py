from typing import List, Dict
from sqlalchemy.orm import Session
from app.db import get_db
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.models.group import Group
from app.schemas.task import TaskCreate
# from app.services.scheduler import scheduler  # 已停用，使用优化版调度器
from datetime import datetime

class TaskDispatcher:
    def __init__(self):
        pass

    async def dispatch_group_task(self, task_data: TaskCreate) -> Task:
        """
        分发组任务
        1. 创建主任务记录
        2. 根据目标范围拆分子任务
        3. 创建任务队列项
        4. 触发任务调度
        """
        db = next(get_db())
        try:
            # 创建主任务
            task = self._create_main_task(db, task_data)

            # 根据目标范围拆分子任务
            if task_data.target_scope == "device":
                await self._dispatch_single_task(db, task, task_data.target_id)
            elif task_data.target_scope == "group":
                await self._dispatch_group_task(db, task, task_data.target_id)
            elif task_data.target_scope == "all":
                await self._dispatch_all_task(db, task)

            return task
        finally:
            db.close()

    def _create_main_task(self, db: Session, task_data: TaskCreate) -> Task:
        """创建主任务记录"""
        task = Task(
            task_type=task_data.task_type,
            parameters=task_data.parameters,
            target_scope=task_data.target_scope,
            target_id=task_data.target_id,
            delay_group=task_data.delay_group,
            delay_like=task_data.delay_like,
            status="pending",
            create_time=datetime.utcnow()
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        return task

    async def _dispatch_single_task(self, db: Session, task: Task, device_id: int):
        """分发单设备任务"""
        device = db.query(Device).filter(Device.id == device_id).first()
        if device:
            self._create_task_queue_item(db, task.id, device.id)
            await scheduler._add_to_device_queue(device.id, task)

    async def _dispatch_group_task(self, db: Session, task: Task, group_id: int):
        """分发设备组任务"""
        devices = db.query(Device).filter(Device.group_id == group_id).all()
        for device in devices:
            self._create_task_queue_item(db, task.id, device.id)
            await scheduler._add_to_device_queue(device.id, task)

    async def _dispatch_all_task(self, db: Session, task: Task):
        """分发所有设备任务"""
        devices = db.query(Device).all()
        for device in devices:
            self._create_task_queue_item(db, task.id, device.id)
            await scheduler._add_to_device_queue(device.id, task)

    def _create_task_queue_item(self, db: Session, task_id: int, device_id: int):
        """创建任务队列项"""
        task_queue = TaskQueue(
            task_id=task_id,
            device_id=device_id,
            status="pending",
            dispatch_time=None,
            finish_time=None
        )
        db.add(task_queue)
        db.commit()

dispatcher = TaskDispatcher()
