"""
任务状态同步服务
用于同步tasks表和task_queue表的状态，以及更新设备当前任务信息
"""

from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.models.group import Group
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

def sync_task_status(db: Session, task_id: int) -> bool:
    """
    同步单个任务的状态
    根据task_queue中的状态更新tasks表的状态
    """
    try:
        # 获取任务
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return False
            
        # 获取该任务的所有队列项
        queue_items = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).all()
        
        if not queue_items:
            # 没有队列项，任务状态保持pending
            return True
            
        # 统计各状态的数量
        status_counts = {
            'pending': 0,
            'running': 0,
            'done': 0,
            'failed': 0
        }
        
        for item in queue_items:
            status_counts[item.status] += 1
            
        total_items = len(queue_items)
        
        # 根据队列状态确定任务状态
        new_status = task.status

        # 如果任务当前是paused状态，不要自动更改
        if task.status == 'paused':
            new_status = 'paused'
        elif status_counts['running'] > 0:
            new_status = 'running'
        elif status_counts['failed'] > 0 and status_counts['done'] + status_counts['failed'] == total_items:
            # 有失败且全部完成
            new_status = 'failed' if status_counts['failed'] == total_items else 'done'
        elif status_counts['done'] == total_items:
            new_status = 'done'
        elif status_counts['done'] + status_counts['failed'] > 0:
            new_status = 'running'
        else:
            new_status = 'pending'
            
        # 更新任务状态
        if task.status != new_status:
            task.status = new_status
            db.commit()
            logger.info(f"任务 {task_id} 状态更新: {task.status} -> {new_status}")
            
        return True
        
    except Exception as e:
        logger.error(f"同步任务状态失败 {task_id}: {e}")
        db.rollback()
        return False

def sync_device_current_task(db: Session, device_id: int) -> bool:
    """
    同步设备当前执行的任务信息
    """
    try:
        device = db.query(Device).filter(Device.id == device_id).first()
        if not device:
            return False
            
        # 查找设备当前正在执行的任务
        current_queue = db.query(TaskQueue).filter(
            and_(
                TaskQueue.device_id == device_id,
                TaskQueue.status == 'running'
            )
        ).first()
        
        if current_queue:
            # 设备有正在执行的任务
            task = db.query(Task).filter(Task.id == current_queue.task_id).first()
            if task:
                device.current_task_id = task.id
                device.current_task_type = task.task_type
                device.has_task = True
            else:
                device.current_task_id = None
                device.current_task_type = None
                device.has_task = False
        else:
            # 设备没有正在执行的任务
            device.current_task_id = None
            device.current_task_type = None
            device.has_task = False
            
        db.commit()
        return True
        
    except Exception as e:
        logger.error(f"同步设备当前任务失败 {device_id}: {e}")
        db.rollback()
        return False

def sync_all_task_status(db: Session) -> Dict[str, int]:
    """
    同步所有任务状态
    """
    try:
        # 获取所有有队列项的任务
        task_ids = db.query(TaskQueue.task_id).distinct().all()
        task_ids = [tid[0] for tid in task_ids]
        
        success_count = 0
        failed_count = 0
        
        for task_id in task_ids:
            if sync_task_status(db, task_id):
                success_count += 1
            else:
                failed_count += 1
                
        return {
            'success': success_count,
            'failed': failed_count,
            'total': len(task_ids)
        }
        
    except Exception as e:
        logger.error(f"批量同步任务状态失败: {e}")
        return {'success': 0, 'failed': 0, 'total': 0}

def sync_all_device_current_task(db: Session) -> Dict[str, int]:
    """
    同步所有设备当前任务信息
    """
    try:
        devices = db.query(Device).all()
        
        success_count = 0
        failed_count = 0
        
        for device in devices:
            if sync_device_current_task(db, device.id):
                success_count += 1
            else:
                failed_count += 1
                
        return {
            'success': success_count,
            'failed': failed_count,
            'total': len(devices)
        }
        
    except Exception as e:
        logger.error(f"批量同步设备当前任务失败: {e}")
        return {'success': 0, 'failed': 0, 'total': 0}

def get_group_task_summary(db: Session, group_id: int) -> Dict:
    """
    获取分组任务摘要
    """
    try:
        # 获取分组信息
        group = db.query(Group).filter(Group.id == group_id).first()
        if not group:
            return None
            
        # 获取分组中的设备
        devices = db.query(Device).filter(Device.group_id == group_id).all()
        device_ids = [d.id for d in devices]
        
        if not device_ids:
            return {
                'group_id': group_id,
                'group_name': group.group_name,
                'device_count': 0,
                'task_summary': {
                    'pending': 0,
                    'running': 0,
                    'done': 0,
                    'failed': 0
                }
            }
            
        # 统计分组任务状态
        task_summary = db.query(
            TaskQueue.status,
            func.count(TaskQueue.id).label('count')
        ).filter(
            TaskQueue.device_id.in_(device_ids)
        ).group_by(TaskQueue.status).all()
        
        status_counts = {
            'pending': 0,
            'running': 0,
            'done': 0,
            'failed': 0
        }
        
        for status, count in task_summary:
            status_counts[status] = count
            
        return {
            'group_id': group_id,
            'group_name': group.group_name,
            'device_count': len(devices),
            'task_summary': status_counts
        }
        
    except Exception as e:
        logger.error(f"获取分组任务摘要失败 {group_id}: {e}")
        return None

def get_device_task_queue(db: Session, device_id: int, limit: int = 10) -> List[Dict]:
    """
    获取设备任务队列
    """
    try:
        queue_items = db.query(TaskQueue).filter(
            TaskQueue.device_id == device_id
        ).order_by(TaskQueue.create_time.desc()).limit(limit).all()
        
        result = []
        for item in queue_items:
            task = db.query(Task).filter(Task.id == item.task_id).first()
            result.append({
                'queue_id': item.id,
                'task_id': item.task_id,
                'task_type': task.task_type if task else 'unknown',
                'status': item.status,
                'create_time': item.create_time,
                'dispatch_time': item.dispatch_time,
                'finish_time': item.finish_time,
                'result_summary': item.result_summary
            })
            
        return result
        
    except Exception as e:
        logger.error(f"获取设备任务队列失败 {device_id}: {e}")
        return []
