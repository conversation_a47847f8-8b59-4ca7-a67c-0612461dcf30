#!/usr/bin/env python3
"""
任务查看器 - 专门用于查看和搜索任务
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import threading

from config import Config
from utils.time_utils import utc_to_beijing

class TaskViewerFrame(ttk.Frame):
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.tasks_data = []
        self.filtered_tasks = []
        
        self.create_widgets()
        
        # 自动刷新任务列表
        self.after(1000, self.refresh)

    def create_widgets(self):
        """创建界面组件"""
        # 搜索区域
        self.create_search_area()
        
        # 任务列表
        self.create_task_list()
        
        # 任务详情
        self.create_task_details()

    def create_search_area(self):
        """创建搜索区域"""
        search_frame = ttk.LabelFrame(self, text="🔍 任务搜索")
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 搜索条件行
        search_row = ttk.Frame(search_frame)
        search_row.pack(fill=tk.X, padx=10, pady=10)
        
        # 博主ID搜索
        ttk.Label(search_row, text="博主ID:").pack(side=tk.LEFT)
        self.search_blogger_id = tk.StringVar()
        self.search_blogger_id.trace('w', self.on_search_changed)
        blogger_entry = ttk.Entry(search_row, textvariable=self.search_blogger_id, width=20)
        blogger_entry.pack(side=tk.LEFT, padx=5)
        
        # 点赞ID搜索
        ttk.Label(search_row, text="点赞ID:").pack(side=tk.LEFT, padx=(20, 5))
        self.search_like_id = tk.StringVar()
        self.search_like_id.trace('w', self.on_search_changed)
        like_entry = ttk.Entry(search_row, textvariable=self.search_like_id, width=20)
        like_entry.pack(side=tk.LEFT, padx=5)
        
        # 任务类型过滤
        ttk.Label(search_row, text="任务类型:").pack(side=tk.LEFT, padx=(20, 5))
        self.search_task_type = ttk.Combobox(search_row, values=["全部"] + list(Config.TASK_TYPES.values()), width=12)
        self.search_task_type.set("全部")
        self.search_task_type.bind('<<ComboboxSelected>>', self.on_search_changed)
        self.search_task_type.pack(side=tk.LEFT, padx=5)
        
        # 状态过滤
        ttk.Label(search_row, text="状态:").pack(side=tk.LEFT, padx=(20, 5))
        self.search_status = ttk.Combobox(search_row, values=["全部"] + list(Config.TASK_STATUS.values()), width=10)
        self.search_status.set("全部")
        self.search_status.bind('<<ComboboxSelected>>', self.on_search_changed)
        self.search_status.pack(side=tk.LEFT, padx=5)
        
        # 操作按钮
        ttk.Button(search_row, text="🔄 刷新", command=self.refresh).pack(side=tk.RIGHT, padx=5)
        ttk.Button(search_row, text="🗑️ 清空", command=self.clear_search).pack(side=tk.RIGHT, padx=5)
        
        # 统计信息
        stats_row = ttk.Frame(search_frame)
        stats_row.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_row, text="总任务: 0 | 显示: 0", font=('Segoe UI', 9), foreground='#7f8c8d')
        self.stats_label.pack(side=tk.LEFT)

    def create_task_list(self):
        """创建任务列表"""
        # 主内容区域
        content_frame = ttk.Frame(self)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 左侧任务列表区域
        list_container = ttk.LabelFrame(content_frame, text="📋 任务列表")
        list_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建Treeview
        tree_frame = ttk.Frame(list_container)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        columns = ('id', 'task_type', 'status', 'blogger_id', 'like_id', 'target_scope', 'create_time')
        self.task_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=25)

        # 设置列标题
        headers = {
            'id': '🆔 ID',
            'task_type': '📝 类型', 
            'status': '📊 状态',
            'blogger_id': '👤 博主ID',
            'like_id': '❤️ 点赞ID',
            'target_scope': '🎯 目标',
            'create_time': '⏰ 创建时间'
        }

        for col, header in headers.items():
            self.task_tree.heading(col, text=header)

        # 优化列宽
        self.task_tree.column('id', width=60, minwidth=50)
        self.task_tree.column('task_type', width=100, minwidth=80)
        self.task_tree.column('status', width=80, minwidth=70)
        self.task_tree.column('blogger_id', width=120, minwidth=100)
        self.task_tree.column('like_id', width=120, minwidth=100)
        self.task_tree.column('target_scope', width=80, minwidth=70)
        self.task_tree.column('create_time', width=140, minwidth=120)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        scrollbar_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.task_tree.xview)
        self.task_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.task_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_y.grid(row=0, column=1, sticky='ns')
        scrollbar_x.grid(row=1, column=0, sticky='ew')

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 绑定事件
        self.task_tree.bind('<<TreeviewSelect>>', self.on_task_selected)
        self.task_tree.bind('<Double-1>', self.on_task_double_click)

    def create_task_details(self):
        """创建任务详情面板"""
        # 右侧详情面板
        details_container = ttk.LabelFrame(self.winfo_children()[-1], text="📋 任务详情")
        details_container.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        details_container.configure(width=350)

        # 详情内容区域
        details_content = ttk.Frame(details_container)
        details_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 基本信息
        info_frame = ttk.LabelFrame(details_content, text="📊 基本信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.detail_labels = {}
        
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill=tk.X, padx=10, pady=10)

        fields = [
            ('🆔 任务ID', 'id'),
            ('📝 类型', 'task_type'),
            ('📊 状态', 'status'),
            ('🎯 目标', 'target_scope'),
            ('⏰ 创建时间', 'create_time')
        ]

        for i, (label_text, field_name) in enumerate(fields):
            ttk.Label(info_grid, text=f"{label_text}:", font=('Segoe UI', 9, 'bold')).grid(
                row=i, column=0, sticky=tk.W, pady=3)
            
            value_label = ttk.Label(info_grid, text="-", foreground="#3498db", font=('Segoe UI', 9))
            value_label.grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=3)
            self.detail_labels[field_name] = value_label

        # 参数信息
        params_frame = ttk.LabelFrame(details_content, text="⚙️ 任务参数")
        params_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        params_content = ttk.Frame(params_frame)
        params_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.params_text = tk.Text(params_content, height=8, wrap=tk.WORD, 
                                  font=('Consolas', 9), bg='#f8f9fa', relief='flat')
        params_scrollbar = ttk.Scrollbar(params_content, orient=tk.VERTICAL, command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=params_scrollbar.set)

        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        params_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 操作按钮
        btn_frame = ttk.Frame(details_content)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="📋 复制任务ID", command=self.copy_task_id).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="🔍 查看详情", command=self.show_task_details).pack(fill=tk.X, pady=2)

    def refresh(self):
        """刷新任务列表"""
        def fetch_data():
            try:
                tasks = self.api_client.get_tasks()
                self.winfo_toplevel().after(0, lambda: self.update_task_list(tasks))
            except Exception as e:
                print(f"获取任务列表失败: {e}")
                self.winfo_toplevel().after(0, lambda: self.update_task_list([]))

        threading.Thread(target=fetch_data, daemon=True).start()

    def update_task_list(self, tasks):
        """更新任务列表显示"""
        self.tasks_data = tasks or []
        self.apply_search_filter()

    def apply_search_filter(self):
        """应用搜索过滤"""
        # 获取搜索条件
        blogger_id_filter = self.search_blogger_id.get().strip().lower()
        like_id_filter = self.search_like_id.get().strip().lower()
        type_filter = self.search_task_type.get()
        status_filter = self.search_status.get()
        
        # 过滤任务
        self.filtered_tasks = []
        for task in self.tasks_data:
            # 检查博主ID
            if blogger_id_filter:
                parameters = task.get('parameters', {})
                blogger_id = str(parameters.get('blogger_id', '')).lower()
                user_id = str(parameters.get('user_id', '')).lower()
                if blogger_id_filter not in blogger_id and blogger_id_filter not in user_id:
                    continue
            
            # 检查点赞ID
            if like_id_filter:
                parameters = task.get('parameters', {})
                like_id = str(parameters.get('like_id', '')).lower()
                if like_id_filter not in like_id:
                    continue
            
            # 检查任务类型
            if type_filter != "全部":
                task_type_display = Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))
                if task_type_display != type_filter:
                    continue
            
            # 检查状态
            if status_filter != "全部":
                status_display = Config.TASK_STATUS.get(task.get('status', ''), task.get('status', ''))
                if status_display != status_filter:
                    continue
            
            self.filtered_tasks.append(task)
        
        # 更新显示
        self.update_tree_display()

    def update_tree_display(self):
        """更新树形控件显示"""
        # 清空现有数据
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)

        # 添加过滤后的任务数据
        for task in self.filtered_tasks:
            # 格式化创建时间
            create_time = task.get('create_time', '')
            if create_time:
                create_time = utc_to_beijing(create_time).split(' ')[1][:5]  # 只显示时分

            # 获取参数中的博主ID和点赞ID
            parameters = task.get('parameters', {})
            blogger_id = parameters.get('blogger_id', parameters.get('user_id', ''))
            like_id = parameters.get('like_id', '')

            # 格式化显示
            task_type_display = Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))
            status_display = Config.TASK_STATUS.get(task.get('status', ''), task.get('status', ''))
            target_display = Config.TARGET_SCOPE.get(task.get('target_scope', ''), task.get('target_scope', ''))

            values = (
                task.get('id', ''),
                task_type_display,
                f"{self.get_status_icon(task.get('status', ''))} {status_display}",
                blogger_id,
                like_id,
                target_display,
                create_time
            )

            self.task_tree.insert('', tk.END, values=values)

        # 更新统计信息
        total_count = len(self.tasks_data)
        filtered_count = len(self.filtered_tasks)
        self.stats_label.configure(text=f"总任务: {total_count} | 显示: {filtered_count}")

    def get_status_icon(self, status):
        """获取状态图标"""
        icons = {
            'pending': '⏳',
            'running': '🔄',
            'done': '✅',
            'failed': '❌',
            'paused': '⏸️',
            'cancelled': '🚫'
        }
        return icons.get(status, '❓')

    def on_search_changed(self, *args):
        """搜索条件变化"""
        self.apply_search_filter()

    def clear_search(self):
        """清空搜索条件"""
        self.search_blogger_id.set("")
        self.search_like_id.set("")
        self.search_task_type.set("全部")
        self.search_status.set("全部")

    def on_task_selected(self, event):
        """任务选择事件"""
        selection = self.task_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.task_tree.item(item, 'values')
        if not values:
            return

        task_id = values[0]
        task_data = None
        for task in self.filtered_tasks:
            if str(task.get('id')) == str(task_id):
                task_data = task
                break

        if task_data:
            self.update_task_details(task_data)

    def on_task_double_click(self, event):
        """任务双击事件"""
        self.show_task_details()

    def update_task_details(self, task_data):
        """更新任务详情显示"""
        # 更新基本信息
        self.detail_labels['id'].configure(text=str(task_data.get('id', '')))
        self.detail_labels['task_type'].configure(text=Config.TASK_TYPES.get(task_data.get('task_type', ''), task_data.get('task_type', '')))
        self.detail_labels['status'].configure(text=Config.TASK_STATUS.get(task_data.get('status', ''), task_data.get('status', '')))
        self.detail_labels['target_scope'].configure(text=Config.TARGET_SCOPE.get(task_data.get('target_scope', ''), task_data.get('target_scope', '')))
        
        create_time = task_data.get('create_time', '')
        if create_time:
            create_time = utc_to_beijing(create_time)
        self.detail_labels['create_time'].configure(text=create_time)

        # 更新参数信息
        self.params_text.delete(1.0, tk.END)
        parameters = task_data.get('parameters', {})
        if parameters:
            import json
            params_str = json.dumps(parameters, indent=2, ensure_ascii=False)
            self.params_text.insert(1.0, params_str)

    def get_selected_task(self):
        """获取选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            return None

        item = selection[0]
        values = self.task_tree.item(item, 'values')
        if not values:
            return None

        task_id = values[0]
        for task in self.filtered_tasks:
            if str(task.get('id')) == str(task_id):
                return task
        return None

    def copy_task_id(self):
        """复制任务ID到剪贴板"""
        task = self.get_selected_task()
        if task:
            self.clipboard_clear()
            self.clipboard_append(str(task['id']))
            messagebox.showinfo("复制成功", f"任务ID {task['id']} 已复制到剪贴板")

    def show_task_details(self):
        """显示任务详情"""
        task = self.get_selected_task()
        if task:
            messagebox.showinfo("任务详情", f"任务ID: {task['id']}\n类型: {Config.TASK_TYPES.get(task.get('task_type', ''), task.get('task_type', ''))}\n状态: {Config.TASK_STATUS.get(task.get('status', ''), task.get('status', ''))}")
