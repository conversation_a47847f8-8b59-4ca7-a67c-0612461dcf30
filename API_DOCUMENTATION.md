# 设备任务分发系统 API 文档

## 1. 设备管理

### 获取所有设备
- **请求方法**: GET
- **请求路径**: `/devices/`
- **响应格式**:
  ```json
  [
    {
      "id": 1,
      "device_number": "device001",
      "device_ip": "***********",
      "online_status": "online",
      "account_status": "normal",
      "last_heartbeat": "2023-01-01T00:00:00"
    }
  ]
  ```

### 创建设备
- **请求方法**: POST
- **请求路径**: `/devices/`
- **请求参数**:
  ```json
  {
    "device_number": "device001",
    "device_ip": "***********",
    "online_status": "online",
    "account_status": "normal"
  }
  ```
  - `device_number`: 设备编号(必填)
    - 类型: string
    - 长度: 3-64个字符
    - 必须唯一
    - 示例: "DEV-001"
  - `device_ip`: 设备IP地址(可选)
    - 类型: string
    - 最大长度: 45个字符
    - 示例: "*************"
  - `online_status`: 在线状态(可选)
    - 类型: enum
    - 可选值: "online"|"offline"
    - 默认值: "offline"
  - `account_status`: 账号状态(可选)
    - 类型: enum
    - 可选值: "normal"|"not_logged_in"|"abnormal"
    - 默认值: "not_logged_in"
- **响应格式**: 同获取所有设备

## 2. 任务管理

### 创建任务
- **请求方法**: POST
- **请求路径**: `/tasks/`
- **请求参数**:
  ```json
  {
    "task_type": "like",
    "parameters": {"blogger_id": "123", "like_id": "456"},
    "target_scope": "group",
    "target_id": 1,
    "delay_group": 5,
    "delay_like": 2
  }
  ```
  - `task_type`: 任务类型(必填)
    - 类型: enum
    - 可选值: 
      - "sign": 签到任务
      - "like": 点赞任务
      - "page_sign": 页面签到任务
  - `parameters`: 任务参数(必填)
    - 类型: JSON对象
    - 格式要求: 根据任务类型不同而不同
    - 示例:
      - 点赞任务: {"blogger_id": "123", "like_id": "456"}
      - 签到任务: {"sign_id": "789"}
  - `target_scope`: 目标范围(必填)
    - 类型: enum
    - 可选值:
      - "single": 单个设备(需提供target_id)
      - "group": 设备分组(需提供target_id)
      - "all": 所有设备
  - `target_id`: 目标ID(条件必填)
    - 类型: integer
    - 当target_scope为"single"或"group"时必填
    - 示例: 1
  - `delay_group`: 分组延迟(可选)
    - 类型: integer
    - 默认值: 0
    - 单位: 秒
    - 描述: 分组内设备之间的任务执行间隔
  - `delay_like`: 点赞延迟(可选)
    - 类型: integer
    - 默认值: 0
    - 单位: 秒
    - 描述: 点赞操作之间的间隔时间
- **响应格式**:
  ```json
  {
    "id": 1,
    "task_type": "like",
    "status": "pending",
    "create_time": "2023-01-01T00:00:00"
  }
  ```

### 获取任务详情
- **请求方法**: GET
- **请求路径**: `/tasks/{task_id}`
- **响应格式**: 同创建任务

### 暂停任务
- **请求方法**: POST
- **请求路径**: `/tasks/{task_id}/pause`
- **请求参数**:
  - `task_id`: 任务ID(路径参数)
    - 类型: integer
    - 必须为已存在的任务ID
    - 示例: 1
- **响应格式**:
  ```json
  {
    "message": "Task 1 paused"
  }
  ```
- **注意事项**:
  - 已开始执行的子任务不会被暂停
  - 暂停后新任务不会被分发
  - 需要管理员权限

### 恢复任务
- **请求方法**: POST
- **请求路径**: `/tasks/{task_id}/resume`
- **请求参数**:
  - `task_id`: 任务ID(路径参数)
    - 类型: integer
    - 必须为已存在的任务ID
    - 示例: 1
- **响应格式**:
  ```json
  {
    "message": "Task 1 resumed"
  }
  ```
- **注意事项**:
  - 只能恢复已暂停的任务
  - 需要管理员权限

### 获取任务状态详情
- **请求方法**: GET
- **请求路径**: `/tasks/{task_id}/status`
- **请求参数**:
  - `task_id`: 任务ID(路径参数)
    - 类型: integer
    - 必须为已存在的任务ID
    - 示例: 1
- **响应格式**:
  ```json
  {
    "task_id": 1,
    "status": "running",
    "queues": {
      "total": 10,
      "pending": 2,
      "running": 5,
      "done": 3,
      "failed": 0
    }
  }
  ```
- **字段说明**:
  - `queues.total`: 总子任务数
  - `queues.pending`: 待处理子任务数
  - `queues.running`: 执行中子任务数
  - `queues.done`: 已完成子任务数
  - `queues.failed`: 失败子任务数

## 3. 分组管理

### 创建分组
- **请求方法**: POST
- **请求路径**: `/groups/`
- **请求参数**:
  ```json
  {
    "group_name": "测试分组",
    "description": "测试用分组"
  }
  ```
  - `group_name`: 分组名称(必填)
    - 类型: string
    - 最大长度: 64个字符
    - 示例: "测试分组"
  - `description`: 分组描述(可选)
    - 类型: string
    - 无长度限制
    - 示例: "用于测试设备的分组"
- **响应格式**:
  ```json
  {
    "id": 1,
    "group_name": "测试分组",
    "create_time": "2023-01-01T00:00:00"
  }
  ```

### 添加设备到分组
- **请求方法**: POST
- **请求路径**: `/groups/{group_id}/devices`
- **请求参数**:
  ```json
  {
    "device_id": 1
  }
  ```
  - `device_id`: 设备ID(必填)
    - 类型: integer
    - 必须为已存在的设备ID
    - 示例: 1
  - `group_id`: 分组ID(路径参数)
    - 类型: integer
    - 必须为已存在的分组ID
    - 示例: 1

## 4. WebSocket 接口

### 设备连接
- **连接地址**: `/ws/{device_number}`
- **协议**: WebSocket
- **消息格式**:
  - 服务端下发任务:
    ```json
    {
      "task_id": 1,
      "type": "like",
      "parameters": {"blogger_id": "123"},
      "delay": 2
    }
    ```
  - 设备上报结果:
    ```json
    {
      "task_id": 1,
      "status": "done",
      "result": "success"
    }
    ```

## 5. 状态查询

### 获取设备状态
- **请求方法**: GET
- **请求路径**: `/devices/{device_id}/status`
- **响应格式**:
  ```json
  {
    "is_online": true,
    "current_task_id": 1,
    "last_heartbeat": "2023-01-01T00:00:00"
  }
  ```

### 获取设备任务队列
- **请求方法**: GET  
- **请求路径**: `/devices/{device_id}/tasks`  
- **响应格式**:
  ```json
  {
    "device_id": 1,
    "current_task": {
      "id": 1,
      "type": "sign",
      "status": "running"
    },
    "queued_tasks": [
      {
        "id": 2,
        "type": "like"
      }
    ]
  }
  ```

### 获取分组设备列表
- **请求方法**: GET
- **请求路径**: `/groups/{group_id}/devices`
- **响应格式**:
  ```json
  [
    {
      "id": 1,
      "device_number": "device001"
    }
  ]
  ```

### 获取组任务进度
- **请求方法**: GET  
- **请求路径**: `/groups/{group_id}/tasks/{task_id}/progress`  
- **响应格式**:
  ```json
  {
    "group_id": 1,
    "task_id": 1,
    "status": "partial",
    "completed": 5,
    "total": 10,
    "progress": "50%",
    "completed_devices": [1,3,5,7,9],
    "pending_devices": [2,4,6,8,10]
  }
  ```

### 获取系统状态概览
- **请求方法**: GET  
- **请求路径**: `/system/status`  
- **响应格式**:
  ```json
  {
    "total_devices": 100,
    "online_devices": 85,
    "active_tasks": 5,
    "task_queues": {
      "pending": 120,
      "running": 35,
      "completed": 420
    }
  }
  ```

## 6. 系统管理

### 更新系统配置
- **请求方法**: PUT  
- **请求路径**: `/system/config`  
- **请求参数**:
  ```json
  {
    "max_concurrent_tasks": 50,
    "task_timeout": 300
  }
  ```
- **响应格式**:
  ```json
  {
    "status": "success",
    "updated_fields": ["max_concurrent_tasks"]
  }
  ```

### 重置任务队列
- **请求方法**: POST  
- **请求路径**: `/system/reset`  
- **请求参数**:
  ```json
  {
    "reset_type": "failed_tasks" // 可选值: "all", "failed_tasks", "pending_tasks"
  }
  ```
- **响应格式**:
  ```json
  {
    "reset_count": 15,
    "remaining_tasks": 30
  }
  ```

## 7. 高级功能

### 批量创建任务
- **请求方法**: POST  
- **请求路径**: `/tasks/batch`  
- **请求参数**:
  ```json
  {
    "tasks": [
      {
        "task_type": "sign",
        "target_scope": "group",
        "target_id": 1
      },
      {
        "task_type": "like",
        "target_scope": "group", 
        "target_id": 2
      }
    ]
  }
  ```
- **响应格式**:
  ```json
  {
    "created_count": 2,
    "task_ids": [101, 102]
  }
  ```

### 任务依赖配置
- **请求方法**: PUT  
- **请求路径**: `/tasks/{task_id}/dependencies`  
- **请求参数**:
  ```json
  {
    "depends_on": [5, 8], // 必须先完成的任务ID列表
    "blocking": true // 是否阻塞后续任务
  }
  ```
- **响应格式**:
  ```json
  {
    "task_id": 10,
    "dependency_count": 2
  }
  ```
