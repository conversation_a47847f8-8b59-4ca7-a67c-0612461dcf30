#!/usr/bin/env python3
"""
数据库清理脚本 - 自动清空所有数据（无需确认）
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db import get_db, engine
from app.models.device import Device
from app.models.group import Group, DeviceGroup
from app.models.task import Task, TaskQueue
from app.models.group_task_status import GroupTaskStatus

def get_table_counts():
    """获取各表的记录数"""
    try:
        db = next(get_db())
        
        counts = {}
        counts['devices'] = db.query(Device).count()
        counts['groups'] = db.query(Group).count()
        counts['device_groups'] = db.query(DeviceGroup).count()
        counts['tasks'] = db.query(Task).count()
        counts['task_queue'] = db.query(TaskQueue).count()
        counts['group_task_status'] = db.query(GroupTaskStatus).count()
        
        db.close()
        return counts
    except Exception as e:
        print(f"❌ 获取表记录数失败: {e}")
        return {}

def clear_database():
    """清空数据库"""
    try:
        print("🔍 检查当前数据库状态...")
        
        # 获取清理前的记录数
        before_counts = get_table_counts()
        if before_counts:
            print("📊 清理前的记录数:")
            for table, count in before_counts.items():
                print(f"   - {table}: {count} 条记录")
        
        total_records = sum(before_counts.values()) if before_counts else 0
        if total_records == 0:
            print("ℹ️ 数据库已经是空的，无需清理")
            return True
        
        print(f"\n🗑️ 开始清理 {total_records} 条记录...")
        
        # 使用原生SQL清理，按依赖关系顺序
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 0. 先禁用外键检查
                print("   🔄 禁用外键检查...")
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                print("      ✅ 外键检查已禁用")

                # 1. 清理任务相关表（有外键依赖）
                print("   🔄 清理 group_task_status 表...")
                result = conn.execute(text("DELETE FROM `group_task_status`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                print("   🔄 清理 task_queue 表...")
                result = conn.execute(text("DELETE FROM `task_queue`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                print("   🔄 清理 tasks 表...")
                result = conn.execute(text("DELETE FROM `tasks`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                # 2. 清理设备组关系表
                print("   🔄 清理 device_groups 表...")
                result = conn.execute(text("DELETE FROM `device_groups`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                # 3. 清理组表
                print("   🔄 清理 groups 表...")
                result = conn.execute(text("DELETE FROM `groups`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                # 4. 清理设备表
                print("   🔄 清理 devices 表...")
                result = conn.execute(text("DELETE FROM `devices`"))
                print(f"      ✅ 删除了 {result.rowcount} 条记录")

                # 5. 重新启用外键检查
                print("   🔄 重新启用外键检查...")
                conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                print("      ✅ 外键检查已重新启用")
                
                # 5. 重置自增ID（可选）
                print("   🔄 重置自增ID...")
                tables_to_reset = [
                    'devices', 'groups', 'device_groups', 
                    'tasks', 'task_queue', 'group_task_status'
                ]
                
                for table in tables_to_reset:
                    try:
                        conn.execute(text(f"ALTER TABLE `{table}` AUTO_INCREMENT = 1"))
                        print(f"      ✅ 重置 {table} 自增ID")
                    except Exception as e:
                        print(f"      ⚠️ 重置 {table} 自增ID失败: {e}")
                
                # 提交事务
                trans.commit()
                print("   ✅ 事务提交成功")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"   ❌ 清理失败，事务已回滚: {e}")
                return False
        
        # 验证清理结果
        print("\n🔍 验证清理结果...")
        after_counts = get_table_counts()
        if after_counts:
            print("📊 清理后的记录数:")
            for table, count in after_counts.items():
                print(f"   - {table}: {count} 条记录")
            
            total_remaining = sum(after_counts.values())
            if total_remaining == 0:
                print("✅ 数据库清理完成！所有数据已删除")
                return True
            else:
                print(f"⚠️ 仍有 {total_remaining} 条记录未删除")
                return False
        else:
            print("❌ 无法验证清理结果")
            return False
            
    except Exception as e:
        print(f"❌ 数据库清理异常: {e}")
        return False

def main():
    """主函数"""
    print("🗑️ 数据库自动清理工具")
    print("="*50)
    
    print("⚠️ 警告：此脚本将自动清空数据库中的所有数据！")
    print("📋 将要清理的表:")
    print("   - tasks (任务表)")
    print("   - task_queue (任务队列表)")
    print("   - group_task_status (组任务状态表)")
    print("   - device_groups (设备组关系表)")
    print("   - groups (组表)")
    print("   - devices (设备表)")
    
    # 执行清理
    print("\n🚀 开始执行数据库清理...")
    success = clear_database()
    
    print("\n" + "="*50)
    if success:
        print("🎉 数据库清理成功完成！")
        print("\n📋 清理结果:")
        print("   ✅ 所有表数据已清空")
        print("   ✅ 自增ID已重置")
        print("   ✅ 数据库结构保持完整")
        
        print("\n💡 后续操作建议:")
        print("   1. 重启后端服务以清理内存状态")
        print("   2. 重新创建测试设备和组")
        print("   3. 验证系统功能正常")
    else:
        print("❌ 数据库清理失败！")
        print("   请检查错误信息并重试")

if __name__ == "__main__":
    main()
