#!/usr/bin/env python3
"""
测试任务创建功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from widgets.task_manager import CreateTaskDialog
from utils.api_client import APIClient
from config import Config

class TestTaskCreation:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("任务创建功能测试")
        self.root.geometry("400x300")
        
        # 创建API客户端
        self.api_client = APIClient(Config.API_BASE_URL)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建测试界面"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="任务创建功能测试", font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # 连接状态
        self.status_label = ttk.Label(main_frame, text="检查API连接中...")
        self.status_label.pack(pady=10)
        
        # 测试按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)
        
        ttk.Button(btn_frame, text="测试创建任务对话框", command=self.test_create_dialog).pack(pady=5)
        ttk.Button(btn_frame, text="测试API连接", command=self.test_api_connection).pack(pady=5)
        ttk.Button(btn_frame, text="查看设备列表", command=self.show_devices).pack(pady=5)
        ttk.Button(btn_frame, text="查看分组列表", command=self.show_groups).pack(pady=5)
        
        # 结果显示
        self.result_text = tk.Text(main_frame, height=10, width=50)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # 初始检查连接
        self.root.after(1000, self.test_api_connection)
        
    def log(self, message):
        """记录日志"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
        
    def test_api_connection(self):
        """测试API连接"""
        self.log("测试API连接...")
        
        connected = self.api_client.check_connection()
        if connected:
            self.status_label.config(text="✅ API连接正常", foreground="green")
            self.log("✅ API连接成功")
        else:
            self.status_label.config(text="❌ API连接失败", foreground="red")
            self.log("❌ API连接失败，请检查后端服务器")
            
    def test_create_dialog(self):
        """测试创建任务对话框"""
        self.log("打开创建任务对话框...")
        
        try:
            def refresh_callback():
                self.log("任务创建回调被调用")
                
            dialog = CreateTaskDialog(self.root, self.api_client, refresh_callback)
            self.log("✅ 创建任务对话框打开成功")
            
        except Exception as e:
            self.log(f"❌ 创建对话框失败: {e}")
            
    def show_devices(self):
        """显示设备列表"""
        self.log("获取设备列表...")
        
        try:
            devices = self.api_client.get_devices()
            if devices:
                self.log(f"✅ 获取到 {len(devices)} 个设备:")
                for i, device in enumerate(devices[:5]):  # 只显示前5个
                    device_number = device.get('device_number', '')
                    device_ip = device.get('device_ip', '')
                    status = device.get('online_status', 'offline')
                    self.log(f"   {i+1}. {device_number} ({device_ip}) - {status}")
                if len(devices) > 5:
                    self.log(f"   ... 还有 {len(devices) - 5} 个设备")
            else:
                self.log("❌ 没有获取到设备")
                
        except Exception as e:
            self.log(f"❌ 获取设备失败: {e}")
            
    def show_groups(self):
        """显示分组列表"""
        self.log("获取分组列表...")
        
        try:
            groups = self.api_client.get_groups()
            if groups:
                self.log(f"✅ 获取到 {len(groups)} 个分组:")
                for i, group in enumerate(groups):
                    group_name = group.get('group_name', '')
                    description = group.get('description', '')
                    self.log(f"   {i+1}. {group_name}")
                    if description:
                        self.log(f"      描述: {description}")
            else:
                self.log("❌ 没有获取到分组")
                
        except Exception as e:
            self.log(f"❌ 获取分组失败: {e}")
            
    def run(self):
        """运行测试"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动任务创建功能测试...")
    
    try:
        app = TestTaskCreation()
        app.run()
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
