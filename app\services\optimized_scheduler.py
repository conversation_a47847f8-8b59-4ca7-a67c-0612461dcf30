"""
优化版任务调度器
重新设计任务分发、排队、执行和状态同步逻辑
"""
import asyncio
import logging
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db import get_db
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.models.group import Group, DeviceGroup
from app.models.group_task_status import GroupTaskStatus
from app.websocket.ws_manager import manager as ws_manager
from app.utils.time_utils import get_beijing_now_naive

logger = logging.getLogger(__name__)

class OptimizedTaskScheduler:
    """优化版任务调度器"""
    
    def __init__(self):
        # 设备队列：每个设备一个队列
        self.device_queues: Dict[int, asyncio.Queue] = {}
        # 设备锁：防止并发执行
        self.device_locks: Dict[int, asyncio.Lock] = {}
        # 运行状态
        self.is_running = False
        # 暂停状态
        self.is_paused = False
        self.pause_event = asyncio.Event()
        self.pause_event.set()  # 初始为非暂停状态
        # 任务处理器
        self.task_processors: Dict[int, asyncio.Task] = {}
        # 设备最后执行任务的时间：记录每个设备最后完成任务的时间
        self.device_last_task_time: Dict[int, datetime] = {}
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
            
        self.is_running = True
        logger.info("🚀 启动优化版任务调度器")
        
        # 初始化设备队列和处理器
        await self._initialize_devices()
        
        # 恢复未完成的任务
        await self._recover_incomplete_tasks()
        
        # 启动任务分发器
        asyncio.create_task(self._task_dispatcher())
        
        # 启动状态同步器
        asyncio.create_task(self._status_synchronizer())
        
        logger.info("✅ 优化版任务调度器启动完成")
    
    async def stop(self):
        """停止调度器"""
        self.is_running = False

        # 停止所有任务处理器
        for processor in self.task_processors.values():
            processor.cancel()

        logger.info("🛑 优化版任务调度器已停止")

    async def pause(self):
        """暂停任务分发"""
        if self.is_paused:
            logger.warning("调度器已处于暂停状态")
            return

        self.is_paused = True
        self.pause_event.clear()

        # 将所有pending任务标记为paused
        db = next(get_db())
        try:
            updated_count = db.query(Task).filter(Task.status == 'pending').update({'status': 'paused'})
            db.commit()
            logger.info(f"⏸️ 调度器已暂停，{updated_count} 个pending任务已暂停")
        except Exception as e:
            logger.error(f"❌ 暂停任务时发生错误: {e}")
            db.rollback()
        finally:
            db.close()

    async def resume(self):
        """恢复任务分发"""
        if not self.is_paused:
            logger.warning("调度器未处于暂停状态")
            return

        self.is_paused = False
        self.pause_event.set()

        # 将所有paused任务恢复为pending
        db = next(get_db())
        try:
            updated_count = db.query(Task).filter(Task.status == 'paused').update({'status': 'pending'})
            db.commit()
            logger.info(f"▶️ 调度器已恢复，{updated_count} 个paused任务已恢复")
        except Exception as e:
            logger.error(f"❌ 恢复任务时发生错误: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def _initialize_devices(self):
        """初始化设备队列和处理器"""
        db = next(get_db())
        try:
            devices = db.query(Device).all()
            logger.info(f"📱 发现 {len(devices)} 个设备")
            
            for device in devices:
                # 创建设备队列和锁
                self.device_queues[device.id] = asyncio.Queue()
                self.device_locks[device.id] = asyncio.Lock()
                
                # 启动设备任务处理器
                processor = asyncio.create_task(
                    self._device_task_processor(device.id)
                )
                self.task_processors[device.id] = processor
                
                logger.info(f"📱 设备 {device.device_number} (ID:{device.id}) 处理器已启动")
                
        finally:
            db.close()
    
    async def _recover_incomplete_tasks(self):
        """恢复未完成的任务"""
        db = next(get_db())
        try:
            # 重置所有running状态的TaskQueue为pending
            running_queues = db.query(TaskQueue).filter(
                TaskQueue.status == 'running'
            ).all()
            
            if running_queues:
                logger.info(f"🔄 发现 {len(running_queues)} 个running状态的任务队列，重置为pending")
                for tq in running_queues:
                    tq.status = 'pending'
                    tq.dispatch_time = None
                db.commit()
            
            # 重新分发所有pending状态的任务
            await self._redistribute_pending_tasks()
            
        finally:
            db.close()
    
    async def _redistribute_pending_tasks(self):
        """重新分发所有pending状态的任务（仅在内存队列为空时）"""
        db = next(get_db())
        try:
            pending_queues = db.query(TaskQueue).filter(
                TaskQueue.status == 'pending'
            ).order_by(TaskQueue.create_time.asc()).all()

            if pending_queues:
                # 只有在有空闲设备时才重新分发
                redistributed_count = 0
                for tq in pending_queues:
                    if tq.device_id in self.device_queues:
                        # 检查设备队列是否为空且没有正在运行的任务
                        if (self.device_queues[tq.device_id].empty() and
                            not await self._device_has_running_task(tq.device_id)):

                            task = db.query(Task).filter(Task.id == tq.task_id).first()
                            if task:
                                await self.device_queues[tq.device_id].put(task)
                                redistributed_count += 1
                                logger.debug(f"📤 任务 {task.id} 重新加入空闲设备 {tq.device_id} 队列")

                if redistributed_count > 0:
                    logger.info(f"📋 重新分发 {redistributed_count} 个pending任务到空闲设备")

        finally:
            db.close()
    
    async def _task_dispatcher(self):
        """任务分发器 - 持续监控新任务并分发"""
        logger.info("🔄 任务分发器已启动")

        while self.is_running:
            try:
                # 等待暂停状态解除
                await self.pause_event.wait()

                await self._dispatch_new_tasks()
                await asyncio.sleep(2)  # 每2秒检查一次新任务
            except Exception as e:
                logger.error(f"❌ 任务分发器错误: {e}")
                await asyncio.sleep(5)
    
    async def _dispatch_new_tasks(self):
        """分发新任务"""
        db = next(get_db())
        try:
            # 查找需要分发的任务（Task表中pending状态且没有对应TaskQueue记录的）
            new_tasks = db.query(Task).filter(
                and_(
                    Task.status == 'pending',
                    ~Task.id.in_(
                        db.query(TaskQueue.task_id).distinct()
                    )
                )
            ).all()

            # 分发新任务
            for task in new_tasks:
                await self._dispatch_task(task)

            # 处理已有TaskQueue但还是pending状态的任务（重新加入内存队列）
            await self._redistribute_pending_tasks()

        finally:
            db.close()
    
    async def _dispatch_task(self, task: Task):
        """分发单个任务"""
        logger.info(f"📤 开始分发任务 {task.id} (类型:{task.task_type}, 范围:{task.target_scope})")

        if task.target_scope == "single":
            await self._dispatch_to_single_device(task)
        elif task.target_scope == "group":
            await self._dispatch_to_group(task)
        elif task.target_scope == "all":
            await self._dispatch_to_all_devices(task)
        else:
            logger.error(f"❌ 未知的任务范围: {task.target_scope}")
    
    async def _dispatch_to_single_device(self, task: Task):
        """分发到单个设备"""
        db = next(get_db())
        try:
            device = db.query(Device).filter(Device.id == task.target_id).first()
            if not device:
                logger.error(f"❌ 设备 {task.target_id} 不存在")
                return
                
            await self._create_task_queue_and_dispatch(task, device.id, db)
            logger.info(f"✅ 任务 {task.id} 已分发到设备 {device.device_number}")
            
        finally:
            db.close()
    
    async def _dispatch_to_group(self, task: Task):
        """分发到设备组"""
        db = next(get_db())
        try:
            # 获取组内所有设备（包括离线设备）
            devices = db.query(Device).join(DeviceGroup).filter(
                DeviceGroup.group_id == task.target_id
            ).all()
            
            if not devices:
                logger.warning(f"⚠️ 组 {task.target_id} 中没有设备")
                return
            
            # 为组内每个设备创建任务队列
            for device in devices:
                await self._create_task_queue_and_dispatch(task, device.id, db)
            
            logger.info(f"✅ 任务 {task.id} 已分发到组 {task.target_id} 的 {len(devices)} 个设备")
            
        finally:
            db.close()
    
    async def _dispatch_to_all_devices(self, task: Task):
        """分发到所有设备"""
        db = next(get_db())
        try:
            devices = db.query(Device).all()
            
            for device in devices:
                await self._create_task_queue_and_dispatch(task, device.id, db)
            
            logger.info(f"✅ 任务 {task.id} 已分发到所有 {len(devices)} 个设备")
            
        finally:
            db.close()
    
    async def _create_task_queue_and_dispatch(self, task: Task, device_id: int, db: Session):
        """创建任务队列记录并分发到设备队列"""
        try:
            # 检查是否已存在任务队列记录
            existing = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.task_id == task.id,
                    TaskQueue.device_id == device_id
                )
            ).first()

            if existing:
                logger.debug(f"📋 任务 {task.id} 在设备 {device_id} 的队列记录已存在，状态: {existing.status}")
                # 不重复加入内存队列，避免重复分发
                return
            
            # 创建新的任务队列记录
            task_queue = TaskQueue(
                task_id=task.id,
                device_id=device_id,
                status='pending',
                create_time=get_beijing_now_naive(),
                group_id=task.target_id if task.target_scope == 'group' else None
            )
            db.add(task_queue)
            db.commit()
            
            # 加入设备内存队列
            if device_id in self.device_queues:
                await self.device_queues[device_id].put(task)
                logger.debug(f"📤 任务 {task.id} 已加入设备 {device_id} 队列")
            else:
                logger.warning(f"⚠️ 设备 {device_id} 队列不存在")
                
        except Exception as e:
            logger.error(f"❌ 创建任务队列失败: 任务{task.id}, 设备{device_id}, 错误:{e}")
            db.rollback()
    
    async def _device_task_processor(self, device_id: int):
        """设备任务处理器"""
        logger.info(f"🔄 设备 {device_id} 任务处理器已启动")

        while self.is_running:
            try:
                # 等待暂停状态解除
                await self.pause_event.wait()

                # 首先检查设备是否在线
                if not await self._is_device_online(device_id):
                    await asyncio.sleep(5)  # 设备离线，等待5秒后重新检查
                    continue

                # 检查设备是否有正在运行的任务
                if await self._device_has_running_task(device_id):
                    await asyncio.sleep(1)
                    continue

                # 从队列获取任务
                try:
                    task = await asyncio.wait_for(
                        self.device_queues[device_id].get(),
                        timeout=1.0
                    )
                    await self._process_device_task(device_id, task)
                except asyncio.TimeoutError:
                    # 队列为空，继续等待
                    continue

            except Exception as e:
                logger.error(f"❌ 设备 {device_id} 任务处理器错误: {e}")
                await asyncio.sleep(5)
    
    async def _is_device_online(self, device_id: int) -> bool:
        """检查设备是否在线"""
        db = next(get_db())
        try:
            device = db.query(Device).filter(Device.id == device_id).first()
            if not device:
                return False

            # 检查WebSocket连接状态
            is_connected = device.device_number in ws_manager.active_connections
            if not is_connected:
                logger.debug(f"🔴 设备 {device_id} ({device.device_number}) 离线")
            return is_connected
        finally:
            db.close()

    async def _device_has_running_task(self, device_id: int) -> bool:
        """检查设备是否有正在运行的任务"""
        db = next(get_db())
        try:
            running_task = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.device_id == device_id,
                    TaskQueue.status == 'running'
                )
            ).first()
            return running_task is not None
        finally:
            db.close()
    
    async def _process_device_task(self, device_id: int, task: Task):
        """处理设备任务"""
        async with self.device_locks[device_id]:
            logger.info(f"🚀 开始处理任务: 任务ID={task.id}, 设备ID={device_id}")

            try:
                # 设备在线状态已在处理器级别检查，这里直接处理任务

                # 应用分组延迟（设备任务间的延迟）
                await self._apply_group_delay_for_device(device_id, task)

                # 更新任务状态为running
                await self._update_task_queue_status(task.id, device_id, 'running')

                # 应用操作延迟（任务内部的延迟）
                if task.delay_like > 0:
                    delay_seconds = task.delay_like / 1000.0
                    logger.info(f"⏰ 应用操作延迟: {delay_seconds}秒")
                    await asyncio.sleep(delay_seconds)

                # 发送任务到设备
                success = await self._send_task_to_device(device_id, task)

                if success:
                    logger.info(f"✅ 任务 {task.id} 发送成功到设备 {device_id}")
                else:
                    # 检查设备是否在线
                    db = next(get_db())
                    try:
                        device = db.query(Device).filter(Device.id == device_id).first()
                        if device and device.device_number in ws_manager.active_connections:
                            # 设备在线但发送失败，标记为failed
                            logger.error(f"❌ 任务 {task.id} 发送失败到在线设备 {device_id}")
                            await self._update_task_queue_status(task.id, device_id, 'failed')
                        else:
                            # 设备离线，保持pending状态
                            logger.warning(f"⚠️ 任务 {task.id} 因设备 {device_id} 离线而暂停，保持pending状态")
                    finally:
                        db.close()

            except Exception as e:
                logger.error(f"❌ 处理任务失败: 任务{task.id}, 设备{device_id}, 错误:{e}")
                await self._update_task_queue_status(task.id, device_id, 'failed')
    
    async def _send_task_to_device(self, device_id: int, task: Task) -> bool:
        """发送任务到设备"""
        db = next(get_db())
        try:
            device = db.query(Device).filter(Device.id == device_id).first()
            if not device:
                return False
            
            # 构建任务消息
            task_msg = {
                'task_id': task.id,
                'type': task.task_type,
                'parameters': task.parameters,
                'device_id': device_id
            }
            
            # 检查WebSocket连接
            if device.device_number in ws_manager.active_connections:
                # 真实发送
                success = await ws_manager.send_task(device.device_number, task_msg)
                logger.info(f"📤 任务 {task.id} 通过WebSocket发送: {'成功' if success else '失败'}")
                return success
            else:
                # 设备离线，不执行任务，保持pending状态等待设备上线
                logger.warning(f"⚠️ 设备 {device.device_number} 离线，任务 {task.id} 保持pending状态等待设备上线")
                logger.info(f"📤 任务消息准备就绪: {task_msg}")

                # 不模拟执行，返回False表示发送失败，但不标记为failed
                # 任务将保持pending状态，等待设备上线后重新分发
                return False
                
        finally:
            db.close()
    
    async def _update_task_queue_status(self, task_id: int, device_id: int, status: str):
        """更新任务队列状态"""
        db = next(get_db())
        try:
            task_queue = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.task_id == task_id,
                    TaskQueue.device_id == device_id
                )
            ).first()
            
            if task_queue:
                task_queue.status = status
                if status == 'running':
                    task_queue.dispatch_time = get_beijing_now_naive()
                elif status in ['done', 'failed']:
                    task_queue.finish_time = get_beijing_now_naive()
                
                db.commit()
                logger.debug(f"📊 任务队列状态更新: 任务{task_id}, 设备{device_id}, 状态{status}")
                
                # 触发主任务状态同步
                await self._sync_main_task_status(task_id)
                
        except Exception as e:
            logger.error(f"❌ 更新任务队列状态失败: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def _apply_group_delay_for_device(self, device_id: int, task: Task):
        """应用设备任务间的分组延迟"""
        if task.delay_group <= 0:
            return  # 没有分组延迟

        current_time = get_beijing_now_naive()
        delay_seconds = task.delay_group / 1000.0  # 转换为秒

        # 检查该设备上次执行任务的时间
        if device_id in self.device_last_task_time:
            last_task_time = self.device_last_task_time[device_id]
            elapsed = (current_time - last_task_time).total_seconds()

            if elapsed < delay_seconds:
                # 需要等待
                wait_time = delay_seconds - elapsed
                logger.info(f"⏰ 应用分组延迟: 设备{device_id} 等待 {wait_time:.1f}秒 (任务间延迟{delay_seconds}秒)")
                await asyncio.sleep(wait_time)
            else:
                logger.info(f"⏰ 分组延迟已满足: 设备{device_id} 距离上次任务 {elapsed:.1f}秒 >= {delay_seconds}秒")
        else:
            logger.info(f"⏰ 设备{device_id} 首次执行任务, 无需延迟")

        # 更新设备最后执行任务的时间
        self.device_last_task_time[device_id] = get_beijing_now_naive()

    async def _simulate_task_execution(self, task_id: int, device_id: int):
        """模拟任务执行过程"""
        try:
            import random

            # 模拟任务执行时间 (1-5秒)
            execution_time = random.uniform(1.0, 5.0)
            await asyncio.sleep(execution_time)

            # 模拟任务结果 (70%成功, 25%失败, 5%会超时)
            result_type = random.choices(
                ['success', 'failed', 'timeout'],
                weights=[70, 25, 5]
            )[0]

            if result_type == 'timeout':
                # 超时任务不立即处理，等待超时监控处理
                logger.info(f"⏰ 模拟任务 {task_id} 超时，等待超时监控处理")
                return
            else:
                # 立即处理成功/失败结果
                status = 'done' if result_type == 'success' else 'failed'
                await self._update_task_queue_status(task_id, device_id, status)

                # 模拟返回结果数据
                result_data = {
                    'task_id': task_id,
                    'device_id': device_id,
                    'status': status,
                    'execution_time': f"{execution_time:.2f}s",
                    'result': f"模拟{result_type}结果",
                    'timestamp': get_beijing_now_naive().isoformat()
                }

                logger.info(f"✅ 模拟任务 {task_id} 执行完成: {status}, 耗时{execution_time:.2f}秒")
                logger.info(f"📊 任务结果: {result_data}")

        except Exception as e:
            logger.error(f"❌ 模拟任务执行失败: 任务{task_id}, 设备{device_id}, 错误:{e}")
            await self._update_task_queue_status(task_id, device_id, 'failed')

    async def _sync_main_task_status(self, task_id: int):
        """同步主任务状态"""
        db = next(get_db())
        try:
            from app.services.task_sync import sync_task_status
            sync_task_status(db, task_id)
        finally:
            db.close()
    
    async def _status_synchronizer(self):
        """状态同步器 - 定期同步所有任务状态"""
        logger.info("🔄 状态同步器已启动")
        
        while self.is_running:
            try:
                await self._sync_all_task_status()
                await asyncio.sleep(30)  # 每30秒同步一次
            except Exception as e:
                logger.error(f"❌ 状态同步器错误: {e}")
                await asyncio.sleep(60)
    
    async def _sync_all_task_status(self):
        """同步所有任务状态"""
        db = next(get_db())
        try:
            from app.services.task_sync import sync_all_task_status
            result = sync_all_task_status(db)
            logger.debug(f"📊 状态同步完成: {result}")
        finally:
            db.close()

# 创建全局实例
optimized_scheduler = OptimizedTaskScheduler()
