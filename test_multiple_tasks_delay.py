#!/usr/bin/env python3
"""
多任务分组延迟测试
创建多个任务验证分组延迟功能
"""

import asyncio
import websockets
import json
import time
import requests
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

class TestDevice:
    """测试设备"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.websocket = None
        self.running = False
        self.received_tasks = []
        self.task_times = []  # 记录任务接收时间
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(f"{WS_URL}/{self.device_number}")
            self.running = True
            log_with_time(f"🔌 设备 {self.device_number} 已连接")
            
            # 启动消息监听
            asyncio.create_task(self._message_listener())
            return True
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 连接失败: {e}")
            return False
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            log_with_time(f"🔌 设备 {self.device_number} 已断开连接")
            
    async def _message_listener(self):
        """监听服务器消息"""
        try:
            while self.running and self.websocket:
                message = await self.websocket.recv()
                await self._handle_message(message)
        except Exception as e:
            log_with_time(f"📨 设备 {self.device_number} 消息监听结束: {e}")
            
    async def _handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
            
            # 检查是否是任务消息
            if self._is_task_message(data):
                receive_time = time.time()
                self.received_tasks.append(data)
                self.task_times.append(receive_time)
                
                task_id = data.get('task_id')
                log_with_time(f"📨 设备 {self.device_number} 收到任务 {task_id}")
                
                await self._handle_task(data)
            elif data.get('type') == 'ack':
                # 忽略ack消息的日志
                pass
            else:
                log_with_time(f"ℹ️ 设备 {self.device_number} 其他消息: {data}")
                
        except json.JSONDecodeError:
            log_with_time(f"❌ 设备 {self.device_number} 消息解析失败: {message}")
        except Exception as e:
            log_with_time(f"❌ 设备 {self.device_number} 消息处理异常: {e}")
            
    def _is_task_message(self, data):
        """判断是否是任务消息"""
        return (
            isinstance(data, dict) and
            'task_id' in data and
            'type' in data and
            data['type'] in ['like', 'sign', 'page_sign', 'inex']
        )
        
    async def _handle_task(self, task_data):
        """处理任务"""
        task_id = task_data.get('task_id')
        
        # 模拟任务执行时间（1秒）
        execution_time = 1.0
        await asyncio.sleep(execution_time)
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "status": "success",
            "result": f"设备{self.device_number}执行成功",
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(completion_msg))
        log_with_time(f"✅ 设备 {self.device_number} 任务 {task_id} 完成")

def create_test_task(task_name, group_id, delay_group_sec=3):
    """创建测试任务"""
    task_data = {
        "task_type": "like",
        "parameters": {
            "count": 1, 
            "test_name": task_name,
            "like_id": "test123"
        },
        "target_scope": "group",
        "target_id": group_id,
        "delay_group": int(delay_group_sec * 1000),
        "delay_like": 1000
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/tasks", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            log_with_time(f"✅ {task_name} 创建成功，ID: {task.get('id')}")
            return task
        else:
            log_with_time(f"❌ {task_name} 创建失败: {response.status_code}")
            return None
    except Exception as e:
        log_with_time(f"❌ 创建 {task_name} 失败: {e}")
        return None

async def test_multiple_tasks_with_delay():
    """测试多任务分组延迟功能"""
    log_with_time("=== 多任务分组延迟测试 ===")
    
    # 创建测试设备
    device = TestDevice("ceshi212")  # 使用分组5的设备
    
    try:
        # 1. 连接设备
        if not await device.connect():
            log_with_time("❌ 设备连接失败，测试终止")
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 2. 快速创建多个连续任务
        delay_seconds = 3  # 3秒分组延迟
        log_with_time(f"🚀 快速创建3个连续任务，分组延迟: {delay_seconds}秒")
        
        test_start_time = time.time()
        
        tasks = []
        for i in range(3):
            task_name = f"多任务延迟测试{i+1}"
            task = create_test_task(task_name, 5, delay_seconds)  # 分组5
            if task:
                tasks.append(task)
            # 快速创建，几乎无间隔
            time.sleep(0.1)
        
        if len(tasks) < 3:
            log_with_time("❌ 任务创建失败，测试终止")
            return
        
        log_with_time(f"📋 已创建 {len(tasks)} 个任务")
        
        # 3. 等待所有任务完成
        log_with_time("⏳ 等待所有任务完成...")
        await asyncio.sleep(25)  # 等待25秒（3个任务 + 2次延迟 = 约15秒）
        
        # 4. 分析结果
        log_with_time("📊 分析分组延迟效果...")
        
        if len(device.task_times) >= 2:
            log_with_time("任务接收时间间隔:")
            total_intervals = []
            
            for i in range(len(device.task_times) - 1):
                time1 = device.task_times[i]
                time2 = device.task_times[i + 1]
                interval = time2 - time1
                total_intervals.append(interval)
                
                task1_id = device.received_tasks[i].get('task_id')
                task2_id = device.received_tasks[i + 1].get('task_id')
                
                log_with_time(f"   任务{task1_id} → 任务{task2_id}: {interval:.1f}秒")
                
                # 检查是否符合预期（任务完成1秒 + 分组延迟3秒 = 约4秒间隔）
                expected_min = 3.5
                expected_max = 5.0
                
                if expected_min <= interval <= expected_max:
                    log_with_time(f"   ✅ 分组延迟正常 ({interval:.1f}秒在预期范围{expected_min}-{expected_max}秒内)")
                else:
                    log_with_time(f"   ⚠️ 分组延迟异常 ({interval:.1f}秒不在预期范围{expected_min}-{expected_max}秒内)")
            
            # 总体分析
            if total_intervals:
                avg_interval = sum(total_intervals) / len(total_intervals)
                log_with_time(f"📊 平均间隔: {avg_interval:.1f}秒")
                
                if 3.5 <= avg_interval <= 5.0:
                    log_with_time("🎉 分组延迟功能正常工作！")
                else:
                    log_with_time("⚠️ 分组延迟功能可能有问题")
        else:
            log_with_time("❌ 收到的任务数量不足，无法分析间隔")
        
        total_time = time.time() - test_start_time
        log_with_time(f"📊 总测试时间: {total_time:.1f}秒")
        log_with_time(f"📊 收到任务数量: {len(device.received_tasks)}")
        log_with_time(f"📊 预期任务数量: {len(tasks)}")
        
        if len(device.received_tasks) == len(tasks):
            log_with_time("✅ 所有任务都被正确接收")
        else:
            log_with_time(f"⚠️ 任务接收不完整: 预期{len(tasks)}个，实际{len(device.received_tasks)}个")
        
    finally:
        await device.disconnect()

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 多任务分组延迟测试")
    print("💡 创建多个任务验证分组延迟功能")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_with_time("✅ API连接正常")
        else:
            log_with_time("❌ API连接失败")
            return
    except:
        log_with_time("❌ API连接失败")
        return
    
    print("\n" + "=" * 40)
    print("🚀 开始测试")
    print("=" * 40)
    
    # 测试多任务分组延迟功能
    await test_multiple_tasks_with_delay()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果分组延迟正常工作:")
    print("   1. 第一个任务立即执行")
    print("   2. 第二个任务等待第一个完成 + 3秒延迟")
    print("   3. 第三个任务等待第二个完成 + 3秒延迟")
    print("   4. 任务间隔应该约为 4秒（1秒执行 + 3秒延迟）")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
