"""
稳定版任务调度器
专注于稳定性和可靠性，具有完善的异常处理和自动恢复机制
"""

import asyncio
import logging
from typing import Dict
from datetime import datetime
# 🔥 安全导入数据库相关模块
try:
    from sqlalchemy.orm import Session
    from sqlalchemy import and_
    from app.db import get_db
    from app.models.task import Task, TaskQueue
    from app.models.device import Device
    from app.utils.time_utils import get_beijing_now_naive
    DB_AVAILABLE = True
except ImportError as e:
    # 数据库模块不可用时的替代
    print(f"数据库模块导入失败: {e}")
    DB_AVAILABLE = False

    class Session:
        pass

    class Task:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class TaskQueue:
        pass

    class Device:
        pass

    def get_db():
        return None

    def and_(*args):
        return None

    def get_beijing_now_naive():
        from datetime import datetime
        return datetime.now()
try:
    from app.models.device_group import DeviceGroup
except ImportError:
    # 如果模块不存在，创建一个简单的替代
    class DeviceGroup:
        group_id = None

# 如果上面的 get_beijing_now_naive 导入失败，使用这个
if not DB_AVAILABLE:
    def get_beijing_now_naive():
        from datetime import datetime
        return datetime.now()

logger = logging.getLogger(__name__)

class StableTaskScheduler:
    """稳定版任务调度器 - 专注于稳定性"""
    
    def __init__(self):
        # 基础状态
        self.is_running = False
        self.is_paused = False
        
        # 设备队列和锁
        self.device_queues: Dict[int, asyncio.Queue] = {}
        self.device_locks: Dict[int, asyncio.Lock] = {}
        
        # 任务处理器
        self.task_processors: Dict[int, asyncio.Task] = {}
        
        # 缓存（简化版，减少复杂性）
        self.device_online_cache: Dict[int, bool] = {}
        self.cache_update_time = datetime.now()
        self.cache_ttl = 5  # 5秒缓存
        
        # 稳定性监控
        self.last_heartbeat = datetime.now()
        self.error_count = 0
        self.max_errors = 10  # 最大错误次数
        
        # 🔥 关键：延迟初始化事件
        self.pause_event = None
        
    async def start(self):
        """启动调度器 - 增强稳定性"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        try:
            # 初始化事件
            if self.pause_event is None:
                self.pause_event = asyncio.Event()
                self.pause_event.set()
            
            self.is_running = True
            self.error_count = 0
            logger.info("🚀 启动稳定版任务调度器")
            
            # 初始化设备
            await self._safe_initialize_devices()
            
            # 启动核心组件
            asyncio.create_task(self._stable_task_dispatcher())
            asyncio.create_task(self._stability_monitor())
            
            logger.info("✅ 稳定版调度器启动完成")
            
        except Exception as e:
            logger.error(f"❌ 调度器启动失败: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """停止调度器"""
        self.is_running = False
        
        # 停止所有任务处理器
        for processor in self.task_processors.values():
            if not processor.done():
                processor.cancel()
        
        logger.info("🛑 稳定版调度器已停止")

    async def pause(self):
        """暂停任务分发"""
        if self.is_paused:
            logger.warning("调度器已处于暂停状态")
            return

        self.is_paused = True
        if self.pause_event:
            self.pause_event.clear()

        logger.info("⏸️ 稳定版调度器已暂停")

    async def resume(self):
        """恢复任务分发"""
        if not self.is_paused:
            logger.warning("调度器未处于暂停状态")
            return

        self.is_paused = False
        if self.pause_event:
            self.pause_event.set()

        logger.info("▶️ 稳定版调度器已恢复")
    
    async def _safe_initialize_devices(self):
        """安全初始化设备"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                db = next(get_db())
                try:
                    devices = db.query(Device).all()
                    logger.info(f"📱 发现 {len(devices)} 个设备")
                    
                    for device in devices:
                        # 创建设备队列和锁
                        self.device_queues[device.id] = asyncio.Queue(maxsize=100)  # 限制队列大小
                        self.device_locks[device.id] = asyncio.Lock()
                        
                        # 启动设备任务处理器
                        processor = asyncio.create_task(
                            self._stable_device_processor(device.id)
                        )
                        self.task_processors[device.id] = processor
                        
                        logger.info(f"📱 设备 {device.device_number} (ID:{device.id}) 处理器已启动")
                    
                    return  # 成功，退出重试循环
                    
                finally:
                    db.close()
                    
            except Exception as e:
                logger.error(f"❌ 初始化设备失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # 指数退避
    
    async def _stable_task_dispatcher(self):
        """稳定的任务分发器"""
        logger.info("🔄 稳定任务分发器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.is_running:
            try:
                # 更新心跳
                self.last_heartbeat = datetime.now()
                
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 分发新任务
                await self._safe_dispatch_new_tasks()
                
                # 重置错误计数
                consecutive_errors = 0
                
                # 正常等待
                await asyncio.sleep(3)  # 3秒检查一次
                
            except Exception as e:
                consecutive_errors += 1
                self.error_count += 1
                
                logger.error(f"❌ 任务分发器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error("❌ 连续错误过多，调度器可能需要重启")
                    # 不停止调度器，而是延长等待时间
                    await asyncio.sleep(30)
                    consecutive_errors = 0
                else:
                    # 指数退避
                    wait_time = min(2 ** consecutive_errors, 30)
                    await asyncio.sleep(wait_time)
    
    async def _safe_dispatch_new_tasks(self):
        """安全分发新任务"""
        try:
            db = next(get_db())
            try:
                # 查找新任务（简化查询）
                new_tasks = db.query(Task).filter(
                    and_(
                        Task.status == 'pending',
                        ~Task.id.in_(
                            db.query(TaskQueue.task_id).distinct()
                        )
                    )
                ).order_by(Task.create_time.asc()).limit(10).all()  # 限制数量
                
                if new_tasks:
                    logger.info(f"📋 发现 {len(new_tasks)} 个新任务待分发")
                
                for task in new_tasks:
                    await self._safe_dispatch_single_task(task, db)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 分发新任务失败: {e}")
    
    async def _safe_dispatch_single_task(self, task: Task, db: Session):
        """安全分发单个任务"""
        try:
            if task.target_scope == "group":
                # 获取组内设备
                devices = db.query(Device).join(DeviceGroup).filter(
                    DeviceGroup.group_id == task.target_id
                ).all()
                
                for device in devices:
                    await self._safe_create_task_queue(task, device.id, db)
                    
            elif task.target_scope == "single":
                await self._safe_create_task_queue(task, task.target_id, db)
                
            elif task.target_scope == "all":
                devices = db.query(Device).all()
                for device in devices:
                    await self._safe_create_task_queue(task, device.id, db)
                    
        except Exception as e:
            logger.error(f"❌ 分发任务 {task.id} 失败: {e}")
    
    async def _safe_create_task_queue(self, task: Task, device_id: int, db: Session):
        """安全创建任务队列"""
        try:
            # 检查是否已存在
            existing = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.task_id == task.id,
                    TaskQueue.device_id == device_id
                )
            ).first()
            
            if existing:
                return
            
            # 创建任务队列记录
            task_queue = TaskQueue(
                task_id=task.id,
                device_id=device_id,
                status='pending',
                create_time=get_beijing_now_naive()
            )
            
            db.add(task_queue)
            db.commit()
            
            # 加入内存队列
            if device_id in self.device_queues:
                try:
                    self.device_queues[device_id].put_nowait(task)
                    logger.debug(f"📤 任务 {task.id} 已加入设备 {device_id} 队列")
                except asyncio.QueueFull:
                    logger.warning(f"⚠️ 设备 {device_id} 队列已满，跳过任务 {task.id}")
            
        except Exception as e:
            logger.error(f"❌ 创建任务队列失败: 任务{task.id}, 设备{device_id}, 错误:{e}")
            db.rollback()
    
    async def _stable_device_processor(self, device_id: int):
        """稳定的设备任务处理器"""
        logger.info(f"🔄 设备 {device_id} 稳定处理器已启动")
        
        consecutive_errors = 0
        max_consecutive_errors = 3
        
        while self.is_running:
            try:
                # 等待暂停状态解除
                if self.pause_event:
                    await self.pause_event.wait()
                
                # 检查设备是否在线
                if not await self._safe_is_device_online(device_id):
                    await asyncio.sleep(5)
                    continue
                
                # 获取任务
                try:
                    task = await asyncio.wait_for(
                        self.device_queues[device_id].get(),
                        timeout=3.0
                    )
                    
                    # 处理任务
                    await self._safe_process_task(device_id, task)
                    
                    # 重置错误计数
                    consecutive_errors = 0
                    
                except asyncio.TimeoutError:
                    # 队列为空，继续等待
                    continue
                
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"❌ 设备 {device_id} 处理器错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"❌ 设备 {device_id} 连续错误过多，暂停处理")
                    await asyncio.sleep(60)  # 暂停1分钟
                    consecutive_errors = 0
                else:
                    await asyncio.sleep(5)
    
    async def _safe_process_task(self, device_id: int, task: Task):
        """安全处理任务"""
        try:
            async with self.device_locks[device_id]:
                logger.info(f"🚀 开始处理任务: 任务ID={task.id}, 设备ID={device_id}")
                
                # 更新状态为running
                await self._safe_update_task_status(task.id, device_id, 'running')
                
                # 发送任务到设备
                success = await self._safe_send_task(device_id, task)
                
                if success:
                    logger.info(f"✅ 任务 {task.id} 发送成功到设备 {device_id}")
                else:
                    logger.warning(f"⚠️ 任务 {task.id} 发送失败，保持pending状态")
                    await self._safe_update_task_status(task.id, device_id, 'pending')
                
        except Exception as e:
            logger.error(f"❌ 处理任务失败: 任务{task.id}, 设备{device_id}, 错误:{e}")
            await self._safe_update_task_status(task.id, device_id, 'failed')
    
    async def _safe_send_task(self, device_id: int, task: Task) -> bool:
        """安全发送任务到设备"""
        try:
            # 🔥 修复：导入WebSocket管理器
            from app.websocket.ws_manager import manager as ws_manager
            
            db = next(get_db())
            try:
                device = db.query(Device).filter(Device.id == device_id).first()
                if not device:
                    return False
                
                # 构建任务消息
                task_msg = {
                    'task_id': task.id,
                    'type': task.task_type,
                    'parameters': task.parameters,
                    'device_id': device_id
                }
                
                # 检查WebSocket连接
                if device.device_number in ws_manager.active_connections:
                    success = await ws_manager.send_task(device.device_number, task_msg)
                    return success
                else:
                    logger.warning(f"⚠️ 设备 {device.device_number} 离线")
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 发送任务失败: {e}")
            return False
    
    async def _safe_update_task_status(self, task_id: int, device_id: int, status: str):
        """安全更新任务状态"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                db = next(get_db())
                try:
                    task_queue = db.query(TaskQueue).filter(
                        and_(
                            TaskQueue.task_id == task_id,
                            TaskQueue.device_id == device_id
                        )
                    ).first()
                    
                    if task_queue:
                        task_queue.status = status
                        
                        if status == 'running':
                            task_queue.dispatch_time = get_beijing_now_naive()
                        elif status in ['done', 'failed']:
                            task_queue.finish_time = get_beijing_now_naive()
                        
                        db.commit()
                        logger.debug(f"📊 任务状态更新: 任务{task_id}, 设备{device_id}, 状态={status}")
                        return
                    
                finally:
                    db.close()
                    
            except Exception as e:
                logger.error(f"❌ 更新任务状态失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    logger.error(f"❌ 任务状态更新最终失败: 任务{task_id}, 设备{device_id}")
                else:
                    await asyncio.sleep(1)
    
    async def _safe_is_device_online(self, device_id: int) -> bool:
        """安全检查设备是否在线"""
        try:
            # 更新缓存
            await self._safe_update_cache()
            
            # 从缓存获取状态
            return self.device_online_cache.get(device_id, False)
            
        except Exception as e:
            logger.error(f"❌ 检查设备在线状态失败: {e}")
            return False
    
    async def _safe_update_cache(self):
        """安全更新缓存"""
        current_time = datetime.now()
        
        # 检查缓存是否过期
        if (current_time - self.cache_update_time).total_seconds() < self.cache_ttl:
            return
        
        try:
            # 🔥 修复：导入WebSocket管理器
            from app.websocket.ws_manager import manager as ws_manager
            
            db = next(get_db())
            try:
                devices = db.query(Device).all()
                
                # 更新设备在线状态
                for device in devices:
                    is_online = device.device_number in ws_manager.active_connections
                    self.device_online_cache[device.id] = is_online
                
                self.cache_update_time = current_time
                
                # 记录在线设备数量
                online_count = sum(1 for is_online in self.device_online_cache.values() if is_online)
                logger.debug(f"📊 缓存更新: {online_count}/{len(devices)} 设备在线")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 更新缓存失败: {e}")
    
    async def _stability_monitor(self):
        """稳定性监控器"""
        logger.info("🔄 稳定性监控器已启动")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 检查心跳
                heartbeat_age = (current_time - self.last_heartbeat).total_seconds()
                if heartbeat_age > 60:  # 1分钟没有心跳
                    logger.warning(f"⚠️ 调度器心跳异常: {heartbeat_age:.1f}秒无响应")
                
                # 检查错误率
                if self.error_count > self.max_errors:
                    logger.warning(f"⚠️ 错误次数过多: {self.error_count}")
                    # 重置错误计数
                    self.error_count = 0
                
                # 检查任务处理器状态
                dead_processors = []
                for device_id, processor in self.task_processors.items():
                    if processor.done():
                        dead_processors.append(device_id)
                
                # 重启死掉的处理器
                for device_id in dead_processors:
                    logger.warning(f"⚠️ 重启设备 {device_id} 的任务处理器")
                    processor = asyncio.create_task(
                        self._stable_device_processor(device_id)
                    )
                    self.task_processors[device_id] = processor
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 稳定性监控错误: {e}")
                await asyncio.sleep(60)

# 创建全局实例
stable_scheduler = StableTaskScheduler()
