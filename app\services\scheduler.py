import asyncio
from typing import Dict, Optional, List
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, and_
from sqlalchemy.orm import Session
from app.db import get_db
from app.config import DATABASE_URL
from app.models.task import Task, TaskQueue
from app.models.device import Device
from app.models.group import DeviceGroup
from app.models.group_task_status import GroupTaskStatus
from app.websocket.ws_manager import manager as ws_manager
from app.services.task_sync import sync_task_status
import logging

logger = logging.getLogger(__name__)

class TaskScheduler:
    def __init__(self):
        self.active_tasks: Dict[int, asyncio.Task] = {}
        self.device_queues: Dict[int, asyncio.Queue] = {}
        self._paused: bool = False
        self._pause_event = asyncio.Event()
        self._pause_event.set()
        self.device_locks: Dict[int, asyncio.Lock] = {}  # 添加设备锁

    async def start_scheduler(self):
        db = next(get_db())
        devices = db.query(Device).all()
        print(f"[调度器启动] 发现 {len(devices)} 个设备")

        # 检查并重新处理running状态的任务
        running_tasks = db.query(TaskQueue).filter(TaskQueue.status == 'running').all()
        if running_tasks:
            print(f"[调度器启动] 发现 {len(running_tasks)} 个running状态的任务，重置为pending")
            for task_queue in running_tasks:
                task_queue.status = 'pending'
            db.commit()

        for device in devices:
            self.device_queues[device.id] = asyncio.Queue()
            task = asyncio.create_task(self._process_device_queue(device.id))
            print(f"[调度器启动] 为设备 {device.device_number} (ID:{device.id}) 启动处理线程")
        asyncio.create_task(self._dispatch_tasks())
        print(f"[调度器启动] 任务分发线程已启动")

    async def pause(self):
        self._paused = True
        self._pause_event.clear()
        db = next(get_db())
        db.query(Task).filter(Task.status == 'pending').update({'status': 'paused'})
        db.commit()

    async def resume(self):
        self._paused = False
        self._pause_event.set()
        db = next(get_db())
        db.query(Task).filter(Task.status == 'paused').update({'status': 'pending'})
        db.commit()

    async def _dispatch_tasks(self):
        """基于队列的任务分发"""
        while True:
            await self._pause_event.wait()
            
            try:
                # 使用独立连接并验证数据库
                engine = create_engine(DATABASE_URL)
                
                # 验证连接和表名
                with engine.connect() as conn:
                    # 检查tasks表是否存在
                    tasks_table_exists = conn.execute(text(
                        "SELECT 1 FROM information_schema.tables "
                        "WHERE table_schema = 'wb' AND table_name = 'tasks'"
                    )).scalar()
                    
                    if not tasks_table_exists:
                        raise Exception("tasks表在wb数据库中不存在")
                        
                    # 检查task_queue表是否存在
                    task_queue_exists = conn.execute(text(
                        "SELECT 1 FROM information_schema.tables "
                        "WHERE table_schema = 'wb' AND table_name = 'task_queue'"
                    )).scalar()
                    
                    if not task_queue_exists:
                        raise Exception("task_queue表在wb数据库中不存在")
                
                # 重新建立连接用于查询
                conn = engine.connect()
                
                try:
                    # 查询需要处理的任务队列(状态为pending)并获取任务范围信息
                    stmt = text("""
                        SELECT 
                            q.id AS queue_id,
                            q.task_id,
                            q.device_id,
                            q.status AS queue_status,
                            q.create_time,
                            q.dispatch_time,
                            q.finish_time,
                            t.id AS task_id,
                            t.task_type,
                            t.parameters,
                            t.target_scope,
                            t.target_id,
                            t.status AS task_status
                        FROM task_queue q
                        JOIN tasks t ON q.task_id = t.id
                        WHERE q.status = 'pending'
                        ORDER BY q.create_time ASC
                        LIMIT 100
                    """)
                    tasks = conn.execute(stmt).fetchall()
                    
                    print(f"\n=== 任务初始化 ===")
                    print(f"需要初始化的任务: {len(tasks)}")
                    
                    for task in tasks:
                        # 根据任务范围创建队列记录
                        if task.target_scope == 'single':
                            await self._init_task_queue(task.task_id, task.target_id)
                        elif task.target_scope == 'group':
                            group_devices = conn.execute(text("""
                                SELECT d.id FROM devices d
                                JOIN device_group dg ON d.id = dg.device_id
                                WHERE dg.group_id = :group_id
                            """), {'group_id': task.target_id}).fetchall()
                            for device in group_devices:
                                await self._init_task_queue(task.task_id, device.id)
                        elif task.target_scope == 'all':
                            devices = conn.execute(text("SELECT id FROM devices")).fetchall()
                            for device in devices:
                                await self._init_task_queue(task.task_id, device.id)
                
                finally:
                    conn.close()
                
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"任务初始化异常: {str(e)}")
                await asyncio.sleep(5)

    async def _init_task_queue(self, task_id: int, device_id: int):
        """初始化任务队列记录"""
        db = None
        try:
            db = next(get_db())
            
            # 严格验证任务存在性
            task_exists = db.query(Task.id).filter(Task.id == task_id).scalar()
            if not task_exists:
                print(f"错误: 任务 {task_id} 不存在，跳过队列记录创建")
                return
                
            # 检查是否已存在队列记录
            exists = db.query(TaskQueue).filter(
                TaskQueue.task_id == task_id,
                TaskQueue.device_id == device_id
            ).first()
            
            if not exists:
                try:
                    task_queue = TaskQueue(
                        task_id=task_id,
                        device_id=device_id,
                        status='pending',
                        create_time=datetime.utcnow()
                    )
                    db.add(task_queue)
                    db.commit()
                    print(f"初始化队列记录: 任务 {task_id} -> 设备 {device_id}")
                except Exception as e:
                    db.rollback()
                    print(f"创建队列记录失败: {str(e)}")
        finally:
            if db:
                db.close()

    async def _dispatch_to_device(self, task: Task, device_id: int):
        db = next(get_db())
        device = db.query(Device).filter(Device.id == device_id).first()
        if device:
            await self._add_to_device_queue(device.id, task)
            # 不要在这里改变任务状态，任务状态应该在实际开始执行时才改为running
            # task.status = 'running'  # 移除这行
            db.commit()

    async def _dispatch_to_group(self, task: Task, group_id: int):
        db = next(get_db())
        try:
            # 初始化组任务状态(仅第一次)
            with db.begin():
                # 主组记录
                main_status = db.query(GroupTaskStatus).filter(
                    GroupTaskStatus.group_id == group_id,
                    GroupTaskStatus.task_id == task.id,
                    GroupTaskStatus.device_id == 0
                ).first()
                
                if not main_status:
                    # 获取组内设备数量
                    device_count = db.query(Device.id).join(DeviceGroup).filter(
                        DeviceGroup.group_id == group_id
                    ).count()
                    
                    main_status = GroupTaskStatus(
                        group_id=group_id,
                        task_id=task.id,
                        device_id=0,
                        total_devices=device_count,
                        status='pending'
                    )
                    db.add(main_status)
                    
                    # 为每个设备创建子记录
                    devices = db.query(Device).join(DeviceGroup).filter(
                        DeviceGroup.group_id == group_id
                    ).all()
                    
                    for device in devices:
                        device_status = GroupTaskStatus(
                            group_id=group_id,
                            task_id=task.id,
                            device_id=device.id,
                            total_devices=1,
                            status='pending'
                        )
                        db.add(device_status)
                
                # 更新任务状态
                db.query(Task).filter(Task.id == task.id).update({'status': 'running'})
                
            # 只分发到在线设备
            online_devices = db.query(Device).join(DeviceGroup).filter(
                DeviceGroup.group_id == group_id,
                Device.device_number.in_(ws_manager.active_connections.keys())
            ).all()
            
            for device in online_devices:
                await self._add_to_device_queue(device.id, task)
                
        except Exception as e:
            print(f"组任务分发失败: {str(e)}")
            db.rollback()
            raise
        finally:
            db.close()

    async def _dispatch_to_all(self, task: Task):
        db = next(get_db())
        devices = db.query(Device).all()
        for device in devices:
            await self._add_to_device_queue(device.id, task)
        # 不要在这里改变任务状态，任务状态应该在实际开始执行时才改为running
        # task.status = 'running'  # 移除这行
        db.commit()

    async def _add_to_device_queue(self, device_id: int, task: Task):
        """任务队列添加逻辑(仅过滤已完成任务)"""
        db = None
        try:
            # 创建新会话并检查事务状态
            db = next(get_db())
            if db.in_transaction():
                db.rollback()
            
            # 确保设备队列存在
            if device_id not in self.device_queues:
                self.device_queues[device_id] = asyncio.Queue()
            
            # 使用独立事务处理
            try:
                with db.begin():
                    # 检查任务状态(带行锁)
                    task_queue = db.query(TaskQueue).filter(
                        TaskQueue.task_id == task.id,
                        TaskQueue.device_id == device_id
                    ).with_for_update().one_or_none()
                    
                    if task_queue:
                        # 仅过滤已完成任务
                        if task_queue.status == 'completed':
                            print(f"[任务完成] 任务 {task.id} 已在设备 {device_id} 完成")
                            return
                        # 其他状态(pending/running/failed)都继续处理
                        print(f"[任务继续] 任务 {task.id} 当前状态: {task_queue.status}")
                    else:
                        # 创建新的任务记录
                        task_queue = TaskQueue(
                            task_id=task.id,
                            device_id=device_id,
                            status='pending',
                            create_time=datetime.utcnow(),
                            group_id=getattr(task, 'group_id', None)
                        )
                        db.add(task_queue)
                        print(f"[新任务] 任务 {task.id} 创建记录")
                    
                # 添加到内存队列
                await self.device_queues[device_id].put(task)
                print(f"[任务分发] 任务 {task.id} 已加入设备 {device_id} 队列")
                
            except Exception as e:
                print(f"[数据库错误] 任务处理失败: {str(e)}")
                raise
                
        except Exception as e:
            print(f"[系统错误] 添加任务失败(任务ID:{task.id}, 设备ID:{device_id}): {str(e)}")
            if db:
                db.rollback()
            # 允许重试而不是直接抛出异常
            await asyncio.sleep(1)
            raise
        finally:
            if db:
                db.close()



    async def _update_task_state(self, task_id: int, device_id: int, new_state: str):
        """统一状态更新方法"""
        VALID_STATES = ['pending', 'running', 'done', 'failed']
        if new_state not in VALID_STATES:
            raise ValueError(f"无效状态: {new_state}")
            
        db = next(get_db())
        try:
            with db.begin():
                task_queue = db.query(TaskQueue).filter(
                    TaskQueue.task_id == task_id,
                    TaskQueue.device_id == device_id
                ).with_for_update().first()
                
                if not task_queue:
                    raise ValueError("任务队列记录不存在")
                    
                # 更新状态
                task_queue.status = new_state
                if new_state == 'running':
                    # 使用北京时间，与超时监控保持一致
                    from app.utils.time_utils import get_beijing_now_naive
                    task_queue.dispatch_time = get_beijing_now_naive()
                elif new_state == 'done':
                    from app.utils.time_utils import get_beijing_now_naive
                    task_queue.finish_time = get_beijing_now_naive()
                    
        except Exception as e:
            db.rollback()
            raise
        finally:
            db.close()

    async def _reactivate_pending_tasks(self, db: Session, device_id: int):
        """重新激活设备的待处理任务"""
        try:
            # 查找该设备的所有pending状态的任务
            pending_tasks = db.query(TaskQueue).filter(
                and_(
                    TaskQueue.device_id == device_id,
                    TaskQueue.status == 'pending'
                )
            ).order_by(TaskQueue.id).all()

            if pending_tasks:
                print(f"[调度器] 🔄 发现设备 {device_id} 有 {len(pending_tasks)} 个待处理任务")

                # 将第一个任务加入设备队列进行处理
                first_task = pending_tasks[0]
                task = db.query(Task).filter(Task.id == first_task.task_id).first()

                if task:
                    print(f"[调度器] 🚀 重新激活任务: 任务ID={task.id}, 设备ID={device_id}")

                    # 将任务加入设备队列
                    if device_id not in self.device_queues:
                        self.device_queues[device_id] = asyncio.Queue()

                    await self.device_queues[device_id].put(task)
                    print(f"[调度器] ✅ 任务 {task.id} 已加入设备 {device_id} 队列")
                else:
                    print(f"[调度器] ❌ 未找到任务详情: 任务ID={first_task.task_id}")
            else:
                print(f"[调度器] ℹ️ 设备 {device_id} 没有待处理的任务")

        except Exception as e:
            print(f"[调度器] ❌ 重新激活待处理任务失败: 设备ID={device_id}, 错误: {e}")

    async def _process_device_queue(self, device_id: int):
        """严格按顺序处理设备任务队列"""
        print(f"[设备处理线程] 设备 {device_id} 处理线程已启动")

        # 获取设备信息
        db = next(get_db())
        try:
            device = db.query(Device).filter(Device.id == device_id).first()
            if not device:
                print(f"[设备处理线程] 设备ID {device_id} 不存在")
                return

            print(f"[设备处理线程] 设备 {device.device_number} (ID:{device_id}) 处理线程初始化完成")

            # 初始化设备锁
            if device_id not in self.device_locks:
                self.device_locks[device_id] = asyncio.Lock()
        finally:
            db.close()

        while True:
            try:
                # 检查设备在线状态 - 优先检查数据库状态，兼容WebSocket连接
                db_check = next(get_db())
                try:
                    current_device = db_check.query(Device).filter(Device.id == device_id).first()
                    if not current_device:
                        print(f"[调度器] 设备ID {device_id} 不存在")
                        await asyncio.sleep(10)
                        continue

                    # 检查设备在线状态：数据库状态 OR WebSocket连接
                    is_online_db = current_device.online_status == 'online'
                    is_online_ws = device.device_number in ws_manager.active_connections

                    # 添加详细的调试信息
                    active_devices = list(ws_manager.active_connections.keys())
                    print(f"[调度器] 设备 {device.device_number} 状态检查:")
                    print(f"  - 数据库状态: {is_online_db}")
                    print(f"  - WebSocket连接: {is_online_ws}")
                    print(f"  - 当前WebSocket连接设备: {active_devices}")

                    if not (is_online_db or is_online_ws):
                        # 检查是否有内存队列中的任务需要处理（即使设备离线）
                        if device_id in self.device_queues and not self.device_queues[device_id].empty():
                            print(f"[调度器] 设备 {device.device_number} 离线，但内存队列有任务，尝试模拟处理")
                            # 继续处理，但会使用模拟发送
                        else:
                            print(f"[调度器] 设备 {device.device_number} 离线 (DB:{is_online_db}, WS:{is_online_ws})")
                            await asyncio.sleep(5)
                            continue

                    # 如果设备刚刚上线，检查是否有待处理的任务需要重新激活
                    if is_online_ws and not getattr(self, f'_last_ws_status_{device_id}', False):
                        print(f"[调度器] 🔄 设备 {device.device_number} 刚刚上线，检查待处理任务...")
                        await self._reactivate_pending_tasks(db_check, device_id)
                        setattr(self, f'_last_ws_status_{device_id}', True)
                    elif not is_online_ws:
                        setattr(self, f'_last_ws_status_{device_id}', False)

                    # 如果数据库显示在线但WebSocket未连接，记录警告但继续处理
                    if is_online_db and not is_online_ws:
                        print(f"[调度器] ⚠️ 设备 {device.device_number} 数据库在线但WebSocket未连接，尝试处理任务")

                finally:
                    db_check.close()

                async with self.device_locks[device_id]:  # 获取设备锁
                    # 检查当前是否有正在运行的任务
                    db = next(get_db())
                    try:
                        running_task = db.query(TaskQueue).filter(
                            TaskQueue.device_id == device_id,
                            TaskQueue.status == 'running'
                        ).first()

                        if running_task:
                            # 等待正在运行的任务完成
                            await self._wait_for_task_completion(running_task.task_id, device_id)
                            continue

                        # 优先检查内存队列中是否有任务
                        task = None
                        if device_id in self.device_queues and not self.device_queues[device_id].empty():
                            try:
                                task = await asyncio.wait_for(self.device_queues[device_id].get(), timeout=0.1)
                                print(f"[调度器] 📋 从内存队列获取任务: 任务ID={task.id}, 设备ID={device_id}")
                            except asyncio.TimeoutError:
                                task = None

                        # 如果内存队列没有任务，从数据库获取
                        if not task:
                            task_queue = db.query(TaskQueue).filter(
                                TaskQueue.device_id == device_id,
                                TaskQueue.status == 'pending'
                            ).order_by(TaskQueue.create_time.asc()).first()

                            if not task_queue:
                                await asyncio.sleep(1)
                                continue

                            task = db.query(Task).filter(Task.id == task_queue.task_id).first()
                            if not task:
                                continue
                            print(f"[调度器] 📋 从数据库获取任务: 任务ID={task.id}, 设备ID={device_id}")

                        if not task:
                            await asyncio.sleep(1)
                            continue

                        print(f"[调度器] 🚀 开始处理任务: 任务ID={task.id}, 设备ID={device_id}")

                        # 更新为运行状态
                        try:
                            await self._update_task_state(task.id, device_id, 'running')
                            print(f"[调度器] ✅ 任务状态已更新为running: 任务ID={task.id}")
                        except Exception as e:
                            print(f"[调度器] ❌ 更新任务状态失败: 任务ID={task.id}, 错误: {e}")
                            continue

                        # 应用任务间隔（数据库存储的是毫秒，需要转换为秒）
                        if task.delay_like > 0:
                            delay_seconds = task.delay_like / 1000.0  # 毫秒转秒
                            print(f"[调度器] ⏰ 应用任务间隔: {delay_seconds}秒 (原始值:{task.delay_like}毫秒)")
                            await asyncio.sleep(delay_seconds)
                            print(f"[调度器] ✅ 任务间隔等待完成，开始发送任务")

                        # 发送任务
                        print(f"[调度器] 📤 准备发送任务: 任务ID={task.id}, 设备={device.device_number}")
                        task_msg = {
                            'task_id': task.id,
                            'type': task.task_type,
                            'parameters': task.parameters
                        }

                        # 尝试发送任务
                        success = False
                        if device.device_number in ws_manager.active_connections:
                            # WebSocket连接可用，正常发送
                            success = await ws_manager.send_task(device.device_number, task_msg)
                            print(f"[调度器] 任务 {task.id} 通过WebSocket发送: {'成功' if success else '失败'}")
                        else:
                            # WebSocket连接不可用，模拟发送（用于测试）
                            print(f"[调度器] ⚠️ 设备 {device.device_number} WebSocket未连接，模拟任务发送")
                            print(f"[调度器] 任务消息: {task_msg}")

                            # 模拟任务执行（立即标记为完成，用于测试）
                            await asyncio.sleep(1)  # 模拟执行时间
                            await self._update_task_state(task.id, device_id, 'done')
                            print(f"[调度器] 模拟任务 {task.id} 执行完成")
                            success = True

                        if success:
                            if device.device_number in ws_manager.active_connections:
                                # 真实WebSocket连接，等待任务完成
                                await self._wait_for_task_completion(task.id, device_id)
                                print(f"[调度器] 任务 {task.id} 已完成，继续处理下一个任务")
                            else:
                                # 模拟连接，任务已经完成
                                print(f"[调度器] 模拟任务 {task.id} 已完成，继续处理下一个任务")
                    finally:
                        db.close()
                        
            except Exception as e:
                print(f"设备 {device_id} 队列处理异常: {str(e)}")
                await asyncio.sleep(5)

    async def _wait_for_task_completion(self, task_id: int, device_id: int):
        """等待任务完成 - 修复版本"""
        max_wait_time = 300  # 最大等待5分钟
        check_interval = 0.5  # 每0.5秒检查一次
        elapsed_time = 0

        print(f"[等待任务完成] 任务ID: {task_id}, 设备ID: {device_id}")

        while elapsed_time < max_wait_time:
            db = None
            try:
                db = next(get_db())

                # 查询任务状态
                task_queue = db.query(TaskQueue).filter(
                    TaskQueue.task_id == task_id,
                    TaskQueue.device_id == device_id
                ).first()

                if task_queue:
                    print(f"[任务状态检查] 任务 {task_id} 当前状态: {task_queue.status}")

                    # 检查是否已完成
                    if task_queue.status in ['done', 'failed']:
                        print(f"[任务完成] 任务 {task_id} 状态: {task_queue.status}")
                        return

                    # 如果状态不是running，可能有问题
                    if task_queue.status not in ['running', 'pending']:
                        print(f"[警告] 任务 {task_id} 状态异常: {task_queue.status}")
                else:
                    print(f"[错误] 未找到任务记录: task_id={task_id}, device_id={device_id}")
                    return

            except Exception as e:
                print(f"[错误] 检查任务状态失败: {str(e)}")
            finally:
                if db:
                    db.close()

            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

        # 超时处理
        print(f"[超时] 任务 {task_id} 等待完成超时 ({max_wait_time}秒)")
        raise Exception(f"任务 {task_id} 等待完成超时")

    async def _update_task_status(self, task_id: int, device_id: int, status: str, result: str = None):
        """优化版状态更新(带资源优先级)"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            # 使用高优先级连接池
            engine = create_engine(
                DATABASE_URL,
                pool_size=3,
                max_overflow=5,
                pool_timeout=30
            )
            conn = None
            try:
                # 获取连接(带超时控制)
                conn = engine.connect()
                cursor = conn.cursor()
                
                # 执行原生SQL更新
                update_query = """
                    UPDATE task_queue 
                    SET status = %s,
                        result_summary = %s,
                        finish_time = CASE WHEN %s = 'done' THEN %s ELSE NULL END
                    WHERE task_id = %s AND device_id = %s
                """
                params = (
                    status,
                    result,
                    status,
                    datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'),
                    task_id,
                    device_id
                )
                
                cursor.execute(update_query, params)
                rowcount = cursor.rowcount
                
                if rowcount == 0:
                    print(f"[警告] 未找到任务记录: task_id={task_id}, device_id={device_id}")
                else:
                    print(f"[成功] 更新任务状态: task_id={task_id}, status={status}")
                
                conn.commit()
                return
                
            except Exception as e:
                retry_count += 1
                error_msg = f"状态更新失败(尝试 {retry_count}/{max_retries}): {str(e)}"
                print(f"[错误] {error_msg}")
                
                if retry_count >= max_retries:
                    raise Exception(f"最终失败: {error_msg}")
                
                await asyncio.sleep(1)
                
            finally:
                try:
                    if conn:
                        conn.close()
                except:
                    pass
                try:
                    if engine:
                        engine.dispose()
                except:
                    pass

    def _update_group_task_status(self, db: Session, group_id: int, task_id: int):
        """更新组任务状态"""
        try:
            # 获取已完成设备数量
            completed_count = db.query(TaskQueue).filter(
                TaskQueue.group_id == group_id,
                TaskQueue.task_id == task_id,
                TaskQueue.status == 'done'
            ).count()
            
            # 更新主组记录
            main_status = db.query(GroupTaskStatus).filter(
                GroupTaskStatus.group_id == group_id,
                GroupTaskStatus.task_id == task_id,
                GroupTaskStatus.device_id == 0
            ).one_or_none()
            
            if main_status:
                main_status.completed_devices = completed_count
                if completed_count >= main_status.total_devices:
                    main_status.status = 'done'
                    main_status.finish_time = datetime.utcnow()
        except Exception as e:
            print(f"更新组任务状态失败: {str(e)}")
            raise

scheduler = TaskScheduler()
