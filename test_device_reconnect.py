#!/usr/bin/env python3
"""
测试设备重连后任务分发功能
"""

import requests
import json
import time

def test_device_reconnect_task_dispatch():
    """测试设备重连后任务分发功能"""
    print("🧪 测试设备重连后任务分发功能")
    print("="*60)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 创建测试设备
        print("\n📱 步骤1: 创建测试设备")
        device_data = {
            "device_number": "test_reconnect_device",
            "device_name": "重连测试设备",
            "ip_address": "*************"
        }
        
        response = requests.post(f"{base_url}/devices/", json=device_data, timeout=10)
        if response.status_code == 200:
            device = response.json()
            device_id = device.get('id')
            print(f"   ✅ 设备创建成功: ID={device_id}, 设备号={device['device_number']}")
        else:
            print(f"   ❌ 设备创建失败: {response.status_code}")
            return False
        
        # 2. 创建测试任务（设备离线状态）
        print("\n📋 步骤2: 创建测试任务（设备离线状态）")
        task_data = {
            "task_type": "like",
            "parameters": {
                "blogger_id": "test_blogger_reconnect",
                "like_id": "test_like_reconnect",
                "delay_click": 1000
            },
            "target_scope": "single",
            "target_id": device_id,
            "delay_like": 2000
        }
        
        response = requests.post(f"{base_url}/tasks/", json=task_data, timeout=10)
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            print(f"   ✅ 任务创建成功: ID={task_id}, 类型={task['task_type']}")
        else:
            print(f"   ❌ 任务创建失败: {response.status_code}")
            return False
        
        # 3. 检查任务状态（应该是pending，因为设备离线）
        print("\n🔍 步骤3: 检查任务状态（设备离线时）")
        time.sleep(2)
        
        response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
        if response.status_code == 200:
            running_info = response.json()
            running_tasks = running_info.get('tasks', [])
            
            related_tasks = [t for t in running_tasks if t.get('task_id') == task_id]
            if related_tasks:
                print(f"   ⚠️ 意外：任务已在运行中（设备离线时不应该运行）")
            else:
                print(f"   ✅ 正确：任务未运行（设备离线）")
        
        # 4. 模拟设备连接（这里我们无法真正连接WebSocket，但可以检查系统行为）
        print("\n🔌 步骤4: 等待设备连接...")
        print("   💡 请手动连接设备WebSocket: ws://localhost:8000/ws/test_reconnect_device")
        print("   💡 或者等待30秒后检查系统是否能正确处理重连...")
        
        # 等待一段时间，让用户有机会连接设备
        for i in range(30, 0, -1):
            print(f"   ⏰ 等待 {i} 秒... (可以在此期间连接设备)", end='\r')
            time.sleep(1)
        print("\n")
        
        # 5. 检查设备状态
        print("\n📊 步骤5: 检查设备状态")
        response = requests.get(f"{base_url}/devices/{device_id}", timeout=10)
        if response.status_code == 200:
            device_info = response.json()
            online_status = device_info.get('online_status')
            print(f"   📱 设备状态: {online_status}")
            
            if online_status == 'online':
                print("   ✅ 设备已上线")
                
                # 6. 检查任务是否开始执行
                print("\n🚀 步骤6: 检查任务执行状态")
                time.sleep(3)
                
                response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
                if response.status_code == 200:
                    running_info = response.json()
                    running_tasks = running_info.get('tasks', [])
                    
                    related_tasks = [t for t in running_tasks if t.get('task_id') == task_id]
                    if related_tasks:
                        task_info = related_tasks[0]
                        runtime = task_info.get('runtime_minutes', 0)
                        print(f"   ✅ 任务已开始执行: 运行时间={runtime}分钟")
                        return True
                    else:
                        print(f"   ❌ 任务未开始执行（可能需要更多时间）")
                        
                        # 再等待一段时间
                        print("   ⏰ 再等待10秒...")
                        time.sleep(10)
                        
                        response = requests.get(f"{base_url}/timeout-monitor/running-tasks", timeout=10)
                        if response.status_code == 200:
                            running_info = response.json()
                            running_tasks = running_info.get('tasks', [])
                            
                            related_tasks = [t for t in running_tasks if t.get('task_id') == task_id]
                            if related_tasks:
                                print(f"   ✅ 任务现在已开始执行")
                                return True
                            else:
                                print(f"   ❌ 任务仍未执行")
                                return False
            else:
                print("   ⚠️ 设备仍然离线")
                return False
        else:
            print(f"   ❌ 获取设备信息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 设备重连任务分发测试")
    print("="*60)
    
    print("📋 测试目标:")
    print("   1. 设备离线时创建任务")
    print("   2. 任务应该处于pending状态")
    print("   3. 设备上线后任务应该自动开始执行")
    print("   4. 验证重新激活机制是否正常工作")
    
    success = test_device_reconnect_task_dispatch()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试通过！设备重连后任务分发功能正常")
        print("\n✅ 验证结果:")
        print("   - 设备离线时任务正确进入pending状态")
        print("   - 设备上线后任务自动开始执行")
        print("   - 重新激活机制工作正常")
    else:
        print("❌ 测试失败！需要检查重连机制")
        print("\n🔍 可能的问题:")
        print("   - 设备状态更新不及时")
        print("   - 任务重新激活机制未触发")
        print("   - WebSocket连接状态检测有问题")
    
    print("\n💡 注意事项:")
    print("   - 确保设备能正常连接WebSocket")
    print("   - 检查调度器日志中的重新激活信息")
    print("   - 验证数据库中的任务状态变化")

if __name__ == "__main__":
    main()
