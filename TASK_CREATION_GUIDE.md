# 任务创建功能使用指南

## 🎯 功能概述

现在的任务创建功能已经完全实现，支持所有任务类型的创建，包括新增的 `inex`（主页关注）任务。

## 🚀 如何创建任务

### 1. 打开创建任务对话框

在任务管理页面点击 **"创建任务"** 按钮，会弹出任务创建对话框。

### 2. 选择任务类型

对话框中有以下任务类型可选：

- **签到任务** (`sign`)
- **点赞任务** (`like`) 
- **页面签到任务** (`page_sign`)
- **主页关注任务** (`inex`) - 新增功能

### 3. 选择目标范围

可以选择任务的执行范围：

- **单个设备**：任务只在选定的一个设备上执行
- **设备分组**：任务在选定分组的所有设备上执行

### 4. 选择具体目标

#### 选择单个设备时：
- 下拉框会显示所有可用设备
- 格式：`🟢/🔴 设备编号 (IP地址)`
- 🟢 表示在线，🔴 表示离线

#### 选择设备分组时：
- 下拉框会显示所有分组
- 格式：`分组名称 (X个设备) - 描述`
- 显示分组中包含的设备数量

### 5. 输入任务参数

根据选择的任务类型，会显示不同的参数输入框：

#### 签到任务 (`sign`)
- **签到次数**：执行签到的次数（默认1次）

#### 点赞任务 (`like`)
- **博主ID**：要点赞的博主ID（必填）
- **点赞ID**：具体的点赞内容ID（必填）
- **点赞次数**：执行点赞的次数（默认1次）

#### 页面签到任务 (`page_sign`)
- **页面URL**：要进行签到的页面地址（必填）

#### 主页关注任务 (`inex`)
- **用户ID**：要关注的用户ID（必填）
- **关注次数**：执行关注的次数（默认1次）

### 6. 设置延迟参数

可以设置任务执行的延迟时间：

- **分组延迟(ms)**：分组间的延迟时间（默认1000毫秒）
- **点赞延迟(ms)**：点赞间的延迟时间（默认500毫秒）

### 7. 创建任务

点击 **"创建"** 按钮完成任务创建。

## 📋 任务创建示例

### 示例1：创建签到任务
1. 任务类型：选择 "签到任务"
2. 目标范围：选择 "单个设备"
3. 选择设备：选择 "🟢 test001 (127.0.0.1)"
4. 签到次数：输入 "3"
5. 点击创建

### 示例2：创建分组点赞任务
1. 任务类型：选择 "点赞任务"
2. 目标范围：选择 "设备分组"
3. 选择分组：选择 "自动化测试组 (5个设备)"
4. 博主ID：输入 "blogger123"
5. 点赞ID：输入 "post456"
6. 点赞次数：输入 "2"
7. 点击创建

### 示例3：创建主页关注任务
1. 任务类型：选择 "主页关注任务"
2. 目标范围：选择 "单个设备"
3. 选择设备：选择在线设备
4. 用户ID：输入 "target_user_123"
5. 关注次数：输入 "1"
6. 点击创建

## 🔧 功能特点

### ✅ 已实现的功能

1. **完整的任务类型支持**：
   - 支持所有4种任务类型
   - 包括新增的inex（主页关注）功能

2. **智能目标选择**：
   - 设备状态实时显示（在线/离线）
   - 分组设备数量显示
   - 友好的显示格式

3. **参数验证**：
   - 必填参数检查
   - 数字格式验证
   - 错误提示

4. **延迟控制**：
   - 可自定义分组延迟
   - 可自定义点赞延迟

5. **用户友好**：
   - 直观的界面设计
   - 清晰的参数说明
   - 实时数据加载

### 🎯 解决的问题

1. **✅ 无法创建任务** - 现在可以正常创建所有类型的任务
2. **✅ 无法选择分组** - 现在可以选择分组并显示设备数量
3. **✅ 参数输入问题** - 现在有专门的参数输入界面
4. **✅ 缺少inex任务** - 已添加主页关注任务支持

## 🚀 使用建议

1. **创建任务前**：
   - 确保后端服务器正在运行
   - 检查设备连接状态
   - 确认分组配置正确

2. **参数设置**：
   - 博主ID和用户ID请使用正确的格式
   - 延迟时间根据实际需要调整
   - 次数设置要合理

3. **目标选择**：
   - 优先选择在线设备
   - 分组任务适合批量操作
   - 单设备任务适合测试

## 🔍 故障排除

### 问题1：无法加载设备/分组列表
**解决方案**：
- 检查API连接状态
- 确认后端服务器运行正常
- 重新打开创建对话框

### 问题2：创建任务失败
**解决方案**：
- 检查所有必填参数是否填写
- 确认参数格式正确
- 查看错误提示信息

### 问题3：找不到设备或分组
**解决方案**：
- 确认设备已正确注册
- 检查分组配置
- 刷新数据

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查API连接状态
3. 确认后端服务正常
4. 参考本指南的故障排除部分
