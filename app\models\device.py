from sqlalchemy import Column, Integer, String, Enum, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from app.db import Base

class Device(Base):
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    device_number = Column(String(64), unique=True, index=True, nullable=False)
    device_ip = Column(String(45))
    online_status = Column(Enum("online", "offline", name="online_status_enum"), default="offline")
    account_status = Column(Enum("normal", "not_logged_in", "abnormal", name="account_status_enum"), default="not_logged_in")
    has_task = Column(Boolean, default=False)
    current_task_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)  # 当前执行的任务ID
    current_task_type = Column(String(50), nullable=True)  # 当前任务类型
    last_heartbeat = Column(DateTime, default=datetime.utcnow)
    group_id = Column(Integer, ForeignKey("groups.id"))

    tasks = relationship("TaskQueue", back_populates="device")
    current_task = relationship("Task", foreign_keys=[current_task_id])  # 当前任务关联
    group = relationship("Group", back_populates="devices")
    status = relationship("DeviceStatus", back_populates="device", uselist=False)
    device_groups = relationship("DeviceGroup", back_populates="device")
